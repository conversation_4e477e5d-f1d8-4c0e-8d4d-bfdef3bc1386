
"use client"

import { createContext, useContext, useState } from "react"
import axios from "axios"

const NewsContext = createContext()
const API_URL = "http://localhost:5432/api/v1"

export const useNews = () => {
  const context = useContext(NewsContext)
  if (!context) {
    throw new Error("useNews must be used within a NewsProvider")
  }
  return context
}

export const NewsProvider = ({ children }) => {
  const [news, setNews] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Configure axios with auth token
  // const getAuthHeaders = () => {
  //   const token = localStorage.getItem("authToken")
  //   return {
  //     headers: {
  //       Authorization: `Bearer ${token}`,
  //     },

  //   }
  // }
const getAuthHeaders = () => {
  const token = localStorage.getItem("authToken")
  return {
    withCredentials: true, // ✅ دا دلته اضافه شو
    headers: {
      Authorization: `Bearer ${token}`,
    },
  }
}

  const fetchNews = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await axios.get(`${API_URL}/news`, getAuthHeaders() )

      if (response.data.success) {
        setNews(response.data.data || [])
        return response.data.data
      } else {
        throw new Error(response.data.error || "Failed to fetch news")
      }
    } catch (error) {
      console.error("Error fetching news:", error)
      setError(error.response?.data?.error || error.message || "An error occurred while fetching news")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const addNews = async (newsData) => {
    try {
      setLoading(true)
      setError(null)

      // Format data according to API requirements
      const formData = new FormData()
      formData.append("A_Title", newsData.title)
      formData.append("A_Body", newsData.content)
      formData.append("u_Id", newsData.authorId)
      formData.append("Category", newsData.category)
      formData.append("Excerpt", newsData.excerpt)
      formData.append("Status", newsData.status === "published" ? "Published" : "Draft")
      formData.append("FeaturedNews", newsData.featured)




      // Handle image upload
      if (newsData.image) {


        formData.append("A_Image", newsData.image)

      }

      const response = await axios.post(`${API_URL}/news`, formData,


        getAuthHeaders(),

      )
      console.log("ok 3");

      if (response.data.success) {
        // Refresh news list after adding
        await fetchNews()
        return response.data.article
      } else {
        throw new Error(response.data.error || "Failed to add news")
      }
    } catch (error) {
      console.error("Error adding news:", error)
      setError(error.response?.data?.error || error.message || "An error occurred while adding news")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateNews = async (id, newsData) => {
    try {
      setLoading(true)
      setError(null)

      // Format data according to API requirements
      const formData = new FormData()
      formData.append("A_Title", newsData.title)
      formData.append("A_Body", newsData.content)
      formData.append("u_Id", newsData.authorId)
      formData.append("Category", newsData.category)
      formData.append("Excerpt", newsData.excerpt)
      formData.append("Status", newsData.status === "published" ? "Published" : "Draft")
      formData.append("FeaturedNews", newsData.featured)

      // Handle image upload
         if (newsData.image) {


        formData.append("A_Image", newsData.image)

      }


      const response = await axios.put(`${API_URL}/news/${id}`, formData, {
        ...getAuthHeaders()

      })

      if (response.data.success) {
        // Refresh news list after updating
        await fetchNews()
        return response.data.article
      } else {
        throw new Error(response.data.error || "Failed to update news")
      }
    } catch (error) {
      console.error("Error updating news:", error)
      setError(error.response?.data?.error || error.message || "An error occurred while updating news")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deleteNews = async (id) => {
    try {
      setLoading(true)
      setError(null)

      const response = await axios.delete(`${API_URL}/news/${id}`, getAuthHeaders())

      if (response.data.success) {
        // Update local state after successful deletion
        setNews(news.filter((item) => item.A_Id !== id))
        return true
      } else {
        throw new Error(response.data.error || "Failed to delete news")
      }
    } catch (error) {
      console.error("Error deleting news:", error)
      setError(error.response?.data?.error || error.message || "An error occurred while deleting news")
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getNewsById = async (id) => {
    try {
      setLoading(true)
      setError(null)

      const response = await axios.get(`${API_URL}/news/${id}`, getAuthHeaders())

      if (response.data.success) {
        return response.data.data
      } else {
        throw new Error(response.data.error || "News item not found")
      }
    } catch (error) {
      console.error("Error fetching news by ID:", error)
      setError(error.response?.data?.error || error.message || "An error occurred while fetching news")
      throw error
    } finally {
      setLoading(false)
    }
  }

  // Map API response to frontend format
  const mapNewsItem = (item) => {
    return {
      id: item.A_Id,
      title: item.A_Title,
      content: item.A_Body,
      image: item.A_Image,
      authorId: item.u_Id,
      excerpt: item.Excerpt,
      autherName: item.U_FirstName,
      category: item.Category,
      status: item.Status,
      featured: item.FeaturedNews === 1 || item.FeaturedNews === true,
      // Backend now provides proper createdAt timestamp
      createdAt: item.createdAt,
      author: item.U_FirstName + (item.U_LastName ? ` ${item.U_LastName}` : ''), // Full author name
      date: item.createdAt, // For backward compatibility
    }
  }

  return (
    <NewsContext.Provider
      value={{
        news: news.map(mapNewsItem),
        loading,
        error,
        fetchNews,
        addNews,
        updateNews,
        deleteNews,
        getNewsById,
      }}
    >
      {children}
    </NewsContext.Provider>
  )
}



/////////////////
// import React, { createContext, useContext, useState, useEffect } from 'react';
// import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';

// const NewsContext = createContext();

// export const useNews = () => {
//   const context = useContext(NewsContext);
//   if (!context) {
//     throw new Error('useNews must be used within a NewsProvider');
//   }
//   return context;
// };

// export const NewsProvider = ({ children }) => {
//   const [news, setNews] = useState(() => loadFromLocalStorage('news', []));

//   useEffect(() => {
//     saveToLocalStorage('news', news);
//   }, [news]);

//   const fetchNews = async () => {
//     try {
//       const savedNews = loadFromLocalStorage('news', []);
//       setNews(savedNews);
//       return savedNews;
//     } catch (error) {
//       console.error('Error fetching news:', error);
//       throw error;
//     }
//   };

//   const addNews = async (newsItem) => {
//     try {
//       const newNews = {
//         ...newsItem,
//         id: Date.now(),
//         date: new Date().toISOString(),
//       };
//       setNews((prev) => [...prev, newNews]);
//       return newNews;
//     } catch (error) {
//       console.error('Error adding news:', error);
//       throw error;
//     }
//   };

//   const updateNews = async (id, updatedNews) => {
//     try {
//       const newsExists = news.some((item) => item.id === id);
//       if (!newsExists) {
//         throw new Error('News item not found');
//       }

//       setNews((prev) =>
//         prev.map((item) => (item.id === id ? { ...item, ...updatedNews, date: new Date().toISOString() } : item))
//       );

//       return true;
//     } catch (error) {
//       console.error('Error updating news:', error);
//       throw error;
//     }
//   };

//   const deleteNews = async (id) => {
//     try {
//       const newsExists = news.some((item) => item.id === id);
//       if (!newsExists) {
//         throw new Error('News item not found');
//       }

//       setNews((prev) => prev.filter((item) => item.id !== id));
//       return true;
//     } catch (error) {
//       console.error('Error deleting news:', error);
//       throw error;
//     }
//   };

//   const getNewsById = (id) => {
//     const newsItem = news.find((item) => item.id === id);
//     if (!newsItem) {
//       throw new Error('News item not found');
//     }
//     return newsItem;
//   };

//   const clearNews = () => {
//     setNews([]);
//     removeFromLocalStorage('news');
//   };

//   return (
//     <NewsContext.Provider
//       value={{
//         news,
//         setNews,
//         addNews,
//         updateNews,
//         deleteNews,
//         clearNews,
//         fetchNews,
//         getNewsById,
//       }}
//     >
//       {children}
//     </NewsContext.Provider>
//   );
// };
