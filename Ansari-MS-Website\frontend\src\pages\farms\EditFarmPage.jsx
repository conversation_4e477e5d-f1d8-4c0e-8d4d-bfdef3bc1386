'use client';

import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Save, X } from 'lucide-react';
import { useFarm } from '../../contexts/FarmContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import MapLocationPicker from '../../components/MapLocationPicker';
import LocationMapCell from '../../components/LocationMapCell';
import Button from '../../components/Button';
import axios from 'axios';

const EditFarmPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { farms, updateFarm } = useFarm();
  const { language, translations } = useLanguage();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [farmers, setFarmers] = useState([]);
  const [selectedFarmer, setSelectedFarmer] = useState(null);
  const [farmersLoaded, setFarmersLoaded] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    owner: '',
    phone: '',
    location: '',
    userId: '',
  });

  // Fetch farmers on component mount
  useEffect(() => {
    if (farmersLoaded) return; // Prevent multiple calls

    const fetchFarmers = async () => {
      try {
        console.log('Fetching farmers...');
        const token = localStorage.getItem('authToken');
        const response = await axios.get('http://localhost:5432/api/v1/users/farmers', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.data.success) {
          console.log('Farmers loaded:', response.data.data);
          setFarmers(response.data.data);
          setFarmersLoaded(true);
        }
      } catch (error) {
        console.error('Error fetching farmers:', error);
        setFarmersLoaded(true); // Mark as loaded even on error to prevent retries
      }
    };

    fetchFarmers();
  }, [farmersLoaded]);

  // Populate form when farm data is available (only once)
  useEffect(() => {
    if (farms.length === 0) return; // Wait for farms to load

    const farm = farms.find((f) => f.id === id || f.id.toString() === id);
    console.log('Looking for farm with ID:', id);
    console.log('Found farm:', farm);

    if (farm && formData.name === '') { // Only populate if form is empty
      console.log('Populating form with farm data:', farm);
      setFormData({
        name: farm.name || '',
        email: farm.email || '',
        owner: farm.owner || '',
        phone: farm.phone || '',
        location: farm.location || '',
        userId: farm.userId || '',
      });

      // Clear any previous error
      setFeedback({ type: '', message: '' });
    } else if (!farm && farms.length > 0) {
      setFeedback({
        type: 'error',
        message: t('farm_not_found') || 'Farm not found. Please check if the farm exists.',
      });
    }
  }, [id, farms, t, formData.name]); // Added formData.name to prevent re-population

  // Separate effect to handle farmer selection when farmers are loaded (only once)
  useEffect(() => {
    if (farmersLoaded && farmers.length > 0 && formData.userId && !selectedFarmer) {
      const farmer = farmers.find(f => f.u_Id.toString() === formData.userId.toString());
      console.log('Setting farmer for userId:', formData.userId);
      console.log('Found farmer:', farmer);

      if (farmer) {
        setSelectedFarmer(farmer);
        // Only update owner if it's empty or matches the farmer
        if (!formData.owner || formData.owner === farmer.name) {
          setFormData(prev => ({
            ...prev,
            owner: farmer.name
          }));
        }
      }
    }
  }, [farmersLoaded, farmers.length, formData.userId]); // Removed selectedFarmer to prevent loops

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`Field changed: ${name} = ${value}`);
    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: value,
      };
      console.log('New form data:', newData);
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const handleFarmerSelect = (e) => {
    const farmerId = e.target.value;
    const farmer = farmers.find(f => f.u_Id.toString() === farmerId);

    console.log('Farmer selected:', farmer);

    if (farmer) {
      setSelectedFarmer(farmer);
      setFormData(prev => ({
        ...prev,
        userId: farmer.u_Id.toString(),
        owner: farmer.name
      }));
    } else {
      setSelectedFarmer(null);
      setFormData(prev => ({
        ...prev,
        userId: '',
        owner: ''
      }));
    }
    setFeedback({ type: '', message: '' });
  };

  const handleLocationSelect = (location) => {
    setFormData(prev => ({
      ...prev,
      location: location
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) throw new Error(t('name_required') || 'Farm name is required');
    if (!formData.email.trim()) throw new Error(t('email_required') || 'Email is required');
    if (!formData.phone.trim()) throw new Error(t('phone_required') || 'Phone number is required');
    if (!formData.location.trim()) throw new Error(t('location_required') || 'Location is required');
    if (!selectedFarmer) throw new Error(t('farmer_required') || 'Please select a farmer');

    // Validate phone format (+93xxxxxxxxx)
    const phoneRegex = /^\+93[0-9]{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      throw new Error(t('invalid_phone_format') || 'Phone must be in format +93xxxxxxxxx');
    }

    // Validate location format (latitude longitude)
    const locationRegex = /^[-+]?[0-9]*\.?[0-9]+ [-+]?[0-9]*\.?[0-9]+$/;
    if (!locationRegex.test(formData.location)) {
      throw new Error(t('invalid_location_format') || 'Location must be in format "latitude longitude"');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      // Prepare farm data with owner ID
      const farmDataToSubmit = {
        ...formData,
        ownerId: selectedFarmer?.u_Id || user?.u_Id || user?.userId || 1,
      };

      console.log('Updating farm with data:', farmDataToSubmit);
      console.log('Selected farmer:', selectedFarmer);

      await updateFarm(id, farmDataToSubmit);
      setFeedback({
        type: 'success',
        message: t('farm_updated_successfully') || 'Farm updated successfully',
      });

      setTimeout(() => {
        navigate('/admin/farms');
      }, 1500);
    } catch (error) {
      console.error('Error updating farm:', error);

      // Extract error message from backend response
      let errorMessage = error.message || t('update_farm_error') || 'Failed to update farm';

      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        <Card>
          <CardHeader>
            <div className={`flex justify-between items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <div>
                <CardTitle>{t('edit_farm')}</CardTitle>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('farm_information')}</p>
              </div>
              <Button
                variant="outline"
                onClick={() => navigate('/admin/farms')}
                className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <X className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                {t('cancel')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-6 p-4 rounded-lg ${
                  feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`}
              >
                {feedback.message}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Farm Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('farm_name') || 'Farm Name'}
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder={t('enter_farm_name') || 'Enter farm name'}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('email') || 'Email'}
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder={t('enter_email') || 'Enter email address'}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                </div>

                {/* Owner (Read-only) */}
                <div>
                  <label htmlFor="owner" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('owner') || 'Owner (Selected Farmer)'}
                  </label>
                  <input
                    type="text"
                    id="owner"
                    name="owner"
                    value={formData.owner}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder="Select a farmer to set as owner"
                    readOnly
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  <small className="text-gray-500">This field is automatically set to the selected farmer</small>
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('phone') || 'Phone Number'}
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder="+93xxxxxxxxx"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  <small className="text-gray-500">Format: +93xxxxxxxxx</small>
                </div>

                {/* Farmer Selection */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('select_farmer') || 'Select Farmer'}
                  </label>
                  <select
                    value={selectedFarmer?.u_Id || ''}
                    onChange={handleFarmerSelect}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    required
                  >
                    <option value="">{t('choose_farmer') || 'Choose a farmer...'}</option>
                    {farmers.map((farmer) => (
                      <option key={farmer.u_Id} value={farmer.u_Id}>
                        {farmer.name} ({farmer.email})
                      </option>
                    ))}
                  </select>
                  {selectedFarmer && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-md">
                      <small className="text-blue-700">
                        Selected: {selectedFarmer.name} - User ID: {selectedFarmer.u_Id}
                      </small>
                    </div>
                  )}
                </div>
              </div>

              {/* Current Location Display */}
              {formData.location && (
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('current_location') || 'Current Location'}
                  </label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <LocationMapCell location={formData.location} />
                  </div>
                </div>
              )}

              {/* Location Selection with Map */}
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('update_location') || 'Update Location'}
                </label>
                <MapLocationPicker
                  onLocationSelect={handleLocationSelect}
                  initialLocation={formData.location}
                  className="w-full"
                />
              </div>

              {/* Submit Button */}
              <div className="mt-6 flex justify-end">
                <Button
                  type="submit"
                  disabled={loading}
                  className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                >
                  {loading ? (
                    <>
                      <svg
                        className={`animate-spin h-5 w-5 text-white ${language === 'ps' ? 'ml-2' : 'mr-2'}`}
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      {t('saving')}
                    </>
                  ) : (
                    <>
                      <Save className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                      {t('save_farm')}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditFarmPage;
