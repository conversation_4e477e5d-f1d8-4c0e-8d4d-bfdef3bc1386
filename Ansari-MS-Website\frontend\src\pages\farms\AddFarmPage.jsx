import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFarm } from '../../contexts/FarmContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/Button';
import { Card, CardHeader, CardContent, CardTitle } from '../../components/feed-components/card';
import MapLocationPicker from '../../components/MapLocationPicker';
import LocationMapCell from '../../components/LocationMapCell';
import { ArrowLeft } from 'lucide-react';
import axios from 'axios';

const AddFarmPage = () => {
  const { addFarm } = useFarm();
  const { language, translations } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [farmers, setFarmers] = useState([]);
  const [selectedFarmer, setSelectedFarmer] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    owner: '',
    phone: '',
    location: '',
    userId: '',
  });

  const t = (key) => translations[language][key] || key;

  // Fetch farmers on component mount
  useEffect(() => {
    const fetchFarmers = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const response = await axios.get('http://localhost:5432/api/v1/users/farmers', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.data.success) {
          setFarmers(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching farmers:', error);
      }
    };

    fetchFarmers();
  }, []);

  // Update owner field when farmer is selected
  useEffect(() => {
    if (selectedFarmer) {
      setFormData(prev => ({
        ...prev,
        owner: selectedFarmer.name
      }));
    }
  }, [selectedFarmer]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleFarmerSelect = (e) => {
    const farmerId = e.target.value;
    const farmer = farmers.find(f => f.u_Id.toString() === farmerId);

    if (farmer) {
      setSelectedFarmer(farmer);
      setFormData(prev => ({
        ...prev,
        userId: farmer.u_Id.toString()
      }));
    } else {
      setSelectedFarmer(null);
      setFormData(prev => ({
        ...prev,
        userId: ''
      }));
    }
    setFeedback({ type: '', message: '' });
  };

  const handleLocationSelect = (location) => {
    setFormData(prev => ({
      ...prev,
      location: location
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      // Validate form
      if (!formData.name.trim()) {
        throw new Error(t('name_required') || 'Farm name is required');
      }
      if (!formData.email.trim()) {
        throw new Error(t('email_required') || 'Email is required');
      }
      if (!formData.phone.trim()) {
        throw new Error(t('phone_required') || 'Phone number is required');
      }
      if (!formData.location.trim()) {
        throw new Error(t('location_required') || 'Location is required');
      }
      if (!selectedFarmer) {
        throw new Error(t('farmer_required') || 'Please select a farmer');
      }

      // Validate phone format (+93xxxxxxxxx)
      const phoneRegex = /^\+93[0-9]{9}$/;
      if (!phoneRegex.test(formData.phone)) {
        throw new Error(t('invalid_phone_format') || 'Phone must be in format +93xxxxxxxxx');
      }

      // Validate location format (latitude longitude)
      const locationRegex = /^[-+]?[0-9]*\.?[0-9]+ [-+]?[0-9]*\.?[0-9]+$/;
      if (!locationRegex.test(formData.location)) {
        throw new Error(t('invalid_location_format') || 'Location must be in format "latitude longitude"');
      }

      // Prepare farm data with owner ID
      const farmDataToSubmit = {
        ...formData,
        ownerId: selectedFarmer?.u_Id || user?.u_Id || user?.userId || 1, // Use selected farmer's ID as owner
      };

      console.log('Form data to submit:', farmDataToSubmit);
      console.log('Selected farmer:', selectedFarmer);
      console.log('Current user:', user);

      await addFarm(farmDataToSubmit);

      setFeedback({
        type: 'success',
        message: t('farm_added_successfully') || 'Farm added successfully',
      });

      setTimeout(() => {
        navigate('/admin/farms');
      }, 1500);
    } catch (error) {
      console.error('Error adding farm:', error);

      // Extract error message from backend response
      let errorMessage = error.message || t('add_farm_error') || 'Failed to add farm';

      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Button variant="secondary" onClick={() => navigate('/admin/farms')} className="mr-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">{t('add_new_farm')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('farm_information') || 'Farm Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">{t('farm_name') || 'Farm Name'}</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder={t('enter_farm_name') || 'Enter farm name'}
                  className="w-full p-2 border rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">{t('email') || 'Email'}</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder={t('enter_email') || 'Enter email address'}
                  className="w-full p-2 border rounded-md"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">{t('owner') || 'Owner (Selected Farmer)'}</label>
                <input
                  type="text"
                  name="owner"
                  value={formData.owner}
                  className="w-full p-2 border rounded-md bg-gray-100"
                  readOnly
                  placeholder="Select a farmer to set as owner"
                />
                <small className="text-gray-500">This field is automatically set to the selected farmer</small>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">{t('phone') || 'Phone Number'}</label>
                <input
                  type="text"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="+93xxxxxxxxx"
                  className="w-full p-2 border rounded-md"
                  required
                />
                <small className="text-gray-500">Format: +93xxxxxxxxx</small>
              </div>

              {/* Farmer Selection */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">{t('select_farmer') || 'Select Farmer'}</label>
                <select
                  value={selectedFarmer?.u_Id || ''}
                  onChange={handleFarmerSelect}
                  className="w-full p-2 border rounded-md"
                  required
                >
                  <option value="">{t('choose_farmer') || 'Choose a farmer...'}</option>
                  {farmers.map((farmer) => (
                    <option key={farmer.u_Id} value={farmer.u_Id}>
                      {farmer.name} ({farmer.email})
                    </option>
                  ))}
                </select>
                {selectedFarmer && (
                  <div className="mt-2 p-2 bg-blue-50 rounded-md">
                    <small className="text-blue-700">
                      Selected: {selectedFarmer.name} - User ID: {selectedFarmer.u_Id}
                    </small>
                  </div>
                )}
              </div>
            </div>

            {/* Current Location Display */}
            {formData.location && (
              <div className="mt-6">
                <label className="block text-sm font-medium mb-2">
                  {t('selected_location') || 'Selected Location'}
                </label>
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <LocationMapCell location={formData.location} />
                </div>
              </div>
            )}

            {/* Location Selection with Map */}
            <div className="mt-6">
              <label className="block text-sm font-medium mb-1">{t('location') || 'Select Location'}</label>
              <MapLocationPicker
                onLocationSelect={handleLocationSelect}
                initialLocation={formData.location}
                className="w-full"
              />
            </div>
            <div className="flex justify-end">
              <Button type="submit" disabled={loading}>
                {loading ? t('saving') : t('save_farm')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddFarmPage;
