import { Link } from 'react-router-dom';

const NotFound = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center p-8 bg-white rounded-lg shadow-md max-w-md">
        <h1 className="text-6xl font-bold text-[#FF6B00] mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-[#2C3E50] mb-4">Page Not Found</h2>
        <p className="text-gray-600 mb-6">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/"
            className="bg-[#FF6B00] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#D32F2F] transition-all duration-300"
          >
            Go to Homepage
          </Link>
          <Link
            to="/contact"
            className="bg-[#2C3E50] text-white font-bold px-6 py-3 rounded-lg hover:bg-[#1a2530] transition-all duration-300"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
