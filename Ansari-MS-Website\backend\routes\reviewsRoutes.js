import express from "express";
import ReviewsController from "../controllers/reviewsController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/", authenticate, ReviewsController.create);
router.get("/", ReviewsController.getAll);
router.get("/:id", ReviewsController.getById);
router.put("/:id", authenticate, ReviewsController.update);
router.delete("/:id", authenticate, authorizeAdmin, ReviewsController.delete);

export default router;
