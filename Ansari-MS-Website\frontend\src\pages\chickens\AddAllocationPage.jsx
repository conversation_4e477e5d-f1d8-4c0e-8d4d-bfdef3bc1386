import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Save, Truck } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useFarm } from '../../contexts/FarmContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';

const AddAllocationPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { allocateToFarm, purchases, fetchPurchases } = useChicken();
  const { farms } = useFarm(); // Use farms from FarmContext
  const { language, translations } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    purchaseId: searchParams.get('purchaseId') || '',
    farmId: '',
    allocationDate: new Date().toISOString().split('T')[0],
    quantity: '',
    pricePerChicken: '',
    totalPrice: '',
  });

  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [selectedFarm, setSelectedFarm] = useState(null);

  useEffect(() => {
    fetchPurchases();
  }, []);



  useEffect(() => {
    if (formData.purchaseId && purchases.length > 0) {
      const purchase = purchases.find(p => p.id.toString() === formData.purchaseId);
      if (purchase) {
        setSelectedPurchase(purchase);
        setFormData(prev => ({
          ...prev,
          pricePerChicken: purchase.pricePerChicken.toString(),
        }));
      }
    }
  }, [formData.purchaseId, purchases]);

  useEffect(() => {
    if (formData.farmId && farms.length > 0) {
      const farm = farms.find(f => f.id.toString() === formData.farmId);
      setSelectedFarm(farm);
    }
  }, [formData.farmId, farms]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newData = { ...prev, [name]: value };
      
      // Auto-calculate total price when quantity or price per chicken changes
      if (name === 'quantity' || name === 'pricePerChicken') {
        const quantity = parseFloat(name === 'quantity' ? value : newData.quantity) || 0;
        const pricePerChicken = parseFloat(name === 'pricePerChicken' ? value : newData.pricePerChicken) || 0;
        newData.totalPrice = (quantity * pricePerChicken).toFixed(2);
      }
      
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.purchaseId) throw new Error(t('purchase_required') || 'Please select a purchase');
    if (!formData.farmId) throw new Error(t('farm_required') || 'Please select a farm');
    if (!formData.allocationDate) throw new Error(t('allocation_date_required') || 'Allocation date is required');
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('quantity_required') || 'Valid quantity is required');
    if (!formData.pricePerChicken || formData.pricePerChicken <= 0) throw new Error(t('price_required') || 'Valid price per chicken is required');
    
    if (selectedPurchase) {
      const remainingQuantity = selectedPurchase.remainingQuantity || (selectedPurchase.quantity - (selectedPurchase.allocatedQuantity || 0));
      if (parseInt(formData.quantity) > remainingQuantity) {
        throw new Error(t('quantity_exceeds_available') || `Quantity cannot exceed available chickens (${remainingQuantity})`);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      const allocationData = {
        purchaseId: parseInt(formData.purchaseId),
        farmId: parseInt(formData.farmId),
        allocationDate: formData.allocationDate,
        quantity: parseInt(formData.quantity),
        pricePerChicken: parseFloat(formData.pricePerChicken),
        totalPrice: parseFloat(formData.totalPrice),
      };

      await allocateToFarm(allocationData);
      
      setFeedback({
        type: 'success',
        message: t('allocation_created_successfully') || 'Chickens allocated to farm successfully',
      });

      setTimeout(() => {
        navigate('/admin/chickens/allocations');
      }, 1500);
    } catch (error) {
      console.error('Error creating allocation:', error);
      
      let errorMessage = error.message || t('create_allocation_error') || 'Failed to allocate chickens to farm';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  // Calculate remaining quantity for each purchase
  const availablePurchases = purchases ? purchases.filter(p => {
    const remainingQuantity = p.remainingQuantity || (p.quantity - (p.allocatedQuantity || 0));
    return remainingQuantity > 0;
  }).map(p => ({
    ...p,
    remainingQuantity: p.remainingQuantity || (p.quantity - (p.allocatedQuantity || 0))
  })) : [];

  // Show loading state while data is being fetched
  if (!purchases) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_data') || 'Loading data...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <Button
          variant="secondary"
          onClick={() => navigate('/admin/chickens/allocations')}
          className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back_to_allocations') || 'Back to Allocations'}
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('allocate_to_farm') || 'Allocate to Farm'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('allocate_chickens_to_farm_description') || 'Allocate chickens to a farm for 45 days'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {t('allocation_information') || 'Allocation Information'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Purchase Selection */}
              <div className="md:col-span-2">
                <label htmlFor="purchaseId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('select_purchase') || 'Select Purchase'}
                </label>
                <select
                  id="purchaseId"
                  name="purchaseId"
                  value={formData.purchaseId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                >
                  <option value="">{t('choose_purchase') || 'Choose a purchase...'}</option>
                  {availablePurchases && availablePurchases.map((purchase) => (
                    <option key={purchase.id} value={purchase.id}>
                      {purchase.supplierName} - {purchase.remainingQuantity} chickens available 
                      (Date: {new Date(purchase.purchaseDate).toLocaleDateString()})
                    </option>
                  ))}
                </select>
                {availablePurchases.length === 0 && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-700">
                      <strong>No purchases with available chickens.</strong> You need to create purchases first.
                      <br />
                      <a href="/admin/chickens/purchases/add" className="text-yellow-800 underline hover:text-yellow-900">
                        Click here to create a purchase
                      </a>
                    </p>
                  </div>
                )}
                {selectedPurchase && (
                  <div className="mt-2 p-3 bg-blue-50 rounded-md">
                    <div className="text-sm text-blue-700">
                      <div><strong>Supplier:</strong> {selectedPurchase.supplierName}</div>
                      <div><strong>Available:</strong> {selectedPurchase.remainingQuantity || (selectedPurchase.quantity - (selectedPurchase.allocatedQuantity || 0))} chickens</div>
                      <div><strong>Price per chicken:</strong> {selectedPurchase.pricePerChicken} AFN</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Farm Selection */}
              <div className="md:col-span-2">
                <label htmlFor="farmId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('select_farm') || 'Select Farm'}
                </label>
                <select
                  id="farmId"
                  name="farmId"
                  value={formData.farmId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                >
                  <option value="">{t('choose_farm') || 'Choose a farm...'}</option>
                  {farms && farms.length > 0 ? farms.map((farm) => (
                    <option key={farm.id} value={farm.id}>
                      {farm.name} - {farm.owner}
                    </option>
                  )) : (
                    <option value="" disabled>No farms available</option>
                  )}
                </select>
                {(!farms || farms.length === 0) && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-700">
                      <strong>No farms available.</strong> You need to create farms first.
                      <br />
                      <a href="/admin/farms/add" className="text-yellow-800 underline hover:text-yellow-900">
                        Click here to create a farm
                      </a>
                    </p>
                  </div>
                )}
                {selectedFarm && (
                  <div className="mt-2 p-3 bg-green-50 rounded-md">
                    <div className="text-sm text-green-700">
                      <div><strong>Farm:</strong> {selectedFarm.name}</div>
                      <div><strong>Owner:</strong> {selectedFarm.owner}</div>
                      <div><strong>Email:</strong> {selectedFarm.email}</div>
                      <div><strong>Phone:</strong> {selectedFarm.phone}</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Allocation Date */}
              <div>
                <label htmlFor="allocationDate" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('allocation_date') || 'Allocation Date'}
                </label>
                <input
                  type="date"
                  id="allocationDate"
                  name="allocationDate"
                  value={formData.allocationDate}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                />
              </div>

              {/* Quantity */}
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('quantity') || 'Quantity (Number of Chickens)'}
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  required
                  min="1"
                  max={selectedPurchase ? (selectedPurchase.remainingQuantity || (selectedPurchase.quantity - (selectedPurchase.allocatedQuantity || 0))) : ''}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_quantity') || 'Enter number of chickens'}
                />
                {selectedPurchase && (
                  <small className="text-gray-500">
                    Maximum available: {selectedPurchase.remainingQuantity || (selectedPurchase.quantity - (selectedPurchase.allocatedQuantity || 0))} chickens
                  </small>
                )}
              </div>

              {/* Price Per Chicken */}
              <div>
                <label htmlFor="pricePerChicken" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('price_per_chicken') || 'Price Per Chicken (AFN)'}
                </label>
                <input
                  type="number"
                  id="pricePerChicken"
                  name="pricePerChicken"
                  value={formData.pricePerChicken}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_price_per_chicken') || 'Enter price per chicken'}
                />
              </div>

              {/* Total Price (Auto-calculated) */}
              <div>
                <label htmlFor="totalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('total_price') || 'Total Price (AFN)'}
                </label>
                <input
                  type="number"
                  id="totalPrice"
                  name="totalPrice"
                  value={formData.totalPrice}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-600 dark:text-white dark:border-gray-600"
                  placeholder={t('auto_calculated') || 'Auto-calculated'}
                />
                <small className="text-gray-500">
                  {t('total_price_auto_calculated') || 'This field is automatically calculated'}
                </small>
              </div>
            </div>

            {/* Expected Return Date Info */}
            {formData.allocationDate && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">
                  {t('allocation_timeline') || 'Allocation Timeline'}
                </h4>
                <div className="text-sm text-yellow-700">
                  <div><strong>Allocation Date:</strong> {new Date(formData.allocationDate).toLocaleDateString()}</div>
                  <div><strong>Expected Return Date:</strong> {new Date(new Date(formData.allocationDate).getTime() + 45 * 24 * 60 * 60 * 1000).toLocaleDateString()}</div>
                  <div><strong>Duration:</strong> 45 days</div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/admin/chickens/allocations')}
              >
                {t('cancel') || 'Cancel'}
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Save className="h-4 w-4" />
                {loading ? (t('allocating') || 'Allocating...') : (t('allocate_to_farm') || 'Allocate to Farm')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddAllocationPage;
