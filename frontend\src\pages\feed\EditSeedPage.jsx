import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useFeed } from '../../contexts/FeedContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import api from '../../utils/api';
import { toast } from 'react-hot-toast';

const EditSeedPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { updateSeed, loading } = useFeed();
  const { language, translations } = useLanguage();

  const [formData, setFormData] = useState({
    S_Name: '',
    S_Price: '',
    S_StartDate: '',
    S_ExpireDate: '',
  });

  const [errors, setErrors] = useState({});
  const [loadingSeed, setLoadingSeed] = useState(true);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    const fetchSeed = async () => {
      try {
        const response = await api.get(`/seeds/${id}`);
        if (response.data.success) {
          const seed = response.data.data;
          setFormData({
            S_Name: seed.S_Name || '',
            S_Price: seed.S_Price || '',
            S_StartDate: seed.S_StartDate ? seed.S_StartDate.split('T')[0] : '',
            S_ExpireDate: seed.S_ExpireDate ? seed.S_ExpireDate.split('T')[0] : '',
          });
        }
      } catch (error) {
        console.error('Error fetching seed:', error);
        toast.error('Failed to fetch seed data');
        navigate('/admin/feed');
      } finally {
        setLoadingSeed(false);
      }
    };

    if (id) {
      fetchSeed();
    }
  }, [id, navigate]);

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.S_Name.trim()) {
      newErrors.S_Name = 'Seed name is required';
    } else if (formData.S_Name.trim().length < 3) {
      newErrors.S_Name = 'Seed name must be at least 3 characters';
    }

    // Price validation
    if (!formData.S_Price) {
      newErrors.S_Price = 'Price is required';
    } else if (parseFloat(formData.S_Price) < 1) {
      newErrors.S_Price = 'Price must be at least 1';
    }

    // Expiry date validation
    if (!formData.S_ExpireDate) {
      newErrors.S_ExpireDate = 'Expiry date is required';
    } else if (formData.S_StartDate && new Date(formData.S_ExpireDate) <= new Date(formData.S_StartDate)) {
      newErrors.S_ExpireDate = 'Expiry date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const seedData = {
        ...formData,
        S_Price: parseFloat(formData.S_Price),
        S_StartDate: formData.S_StartDate || null,
      };

      await updateSeed(id, seedData);
      navigate('/admin/feed');
    } catch (error) {
      console.error('Error updating seed:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (loadingSeed) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6B00] mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading seed data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/feed')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Seed</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Update seed information
            </p>
          </div>
        </div>

        {/* Form */}
        <Card>
          <CardHeader>
            <CardTitle>Seed Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Seed Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Seed Name *
                  </label>
                  <input
                    type="text"
                    name="S_Name"
                    value={formData.S_Name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.S_Name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter seed name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.S_Name && (
                    <p className="text-red-500 text-sm mt-1">{errors.S_Name}</p>
                  )}
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Price ($) *
                  </label>
                  <input
                    type="number"
                    name="S_Price"
                    value={formData.S_Price}
                    onChange={handleChange}
                    min="1"
                    step="0.01"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.S_Price ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter price"
                  />
                  {errors.S_Price && (
                    <p className="text-red-500 text-sm mt-1">{errors.S_Price}</p>
                  )}
                </div>

                {/* Start Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    name="S_StartDate"
                    value={formData.S_StartDate}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  />
                </div>

                {/* Expiry Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Expiry Date *
                  </label>
                  <input
                    type="date"
                    name="S_ExpireDate"
                    value={formData.S_ExpireDate}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.S_ExpireDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.S_ExpireDate && (
                    <p className="text-red-500 text-sm mt-1">{errors.S_ExpireDate}</p>
                  )}
                </div>
              </div>

              {/* Form Actions */}
              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button
                  type="submit"
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  {loading ? 'Updating...' : 'Update Seed'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/feed')}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {t('cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditSeedPage;
