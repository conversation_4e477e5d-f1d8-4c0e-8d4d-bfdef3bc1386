import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';
import { Card, Table, Button, Input, DatePicker, Space, Tag, Alert } from 'antd';
import { SearchOutlined, PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const InventoryPage = () => {
  const { language, translations } = useLanguage();
  const { notifyLowStock, notifyExpiry } = useNotifications();
  const [inventory, setInventory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedDate, setSelectedDate] = useState(null);

  // Get translation function with safe fallback
  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    // Load inventory data from localStorage
    const savedInventory = localStorage.getItem('inventory');
    if (savedInventory) {
      setInventory(JSON.parse(savedInventory));
    }
  }, []);

  useEffect(() => {
    // Save inventory data to localStorage whenever it changes
    localStorage.setItem('inventory', JSON.stringify(inventory));
  }, [inventory]);

  const columns = [
    {
      title: t('item_name'),
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: t('category'),
      dataIndex: 'category',
      key: 'category',
      filters: [
        { text: t('chicken'), value: 'chicken' },
        { text: t('medicine'), value: 'medicine' },
        { text: t('feed'), value: 'feed' },
      ],
      onFilter: (value, record) => record.category === value,
    },
    {
      title: t('quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a, b) => a.quantity - b.quantity,
      render: (quantity, record) => (
        <Space>
          <span>
            {quantity} {record.unit}
          </span>
          {quantity < record.minimumStock && <Tag color="red">{t('low_stock')}</Tag>}
        </Space>
      ),
    },
    {
      title: t('batch_number'),
      dataIndex: 'batchNumber',
      key: 'batchNumber',
    },
    {
      title: t('expiry_date'),
      dataIndex: 'expiryDate',
      key: 'expiryDate',
      sorter: (a, b) => new Date(a.expiryDate) - new Date(b.expiryDate),
      render: (date) => {
        const expiryDate = new Date(date);
        const today = new Date();
        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiry < 0) {
          return <Tag color="red">{t('expired')}</Tag>;
        } else if (daysUntilExpiry < 30) {
          return <Tag color="orange">{t('expiring_soon')}</Tag>;
        }
        return date;
      },
    },
    {
      title: t('actions'),
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="primary" onClick={() => handleEdit(record)}>
            {t('edit')}
          </Button>
          <Button danger onClick={() => handleDelete(record)}>
            {t('delete')}
          </Button>
        </Space>
      ),
    },
  ];

  const handleSearch = (value) => {
    setSearchText(value);
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
  };

  const handleEdit = (record) => {
    // Implement edit functionality
    console.log('Edit:', record);
  };

  const handleDelete = (record) => {
    // Implement delete functionality
    console.log('Delete:', record);
  };

  const handleAdd = () => {
    // Implement add functionality
    console.log('Add new item');
  };

  return (
    <div className="inventory-page">
      <Card title={t('inventory_management')}>
        <Space style={{ marginBottom: 16 }}>
          <Input
            placeholder={t('search_items')}
            prefix={<SearchOutlined />}
            onChange={(e) => handleSearch(e.target.value)}
          />
          <DatePicker onChange={handleDateChange} />
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t('add_item')}
          </Button>
        </Space>

        <Table columns={columns} dataSource={inventory} loading={loading} rowKey="id" pagination={{ pageSize: 10 }} />
      </Card>
    </div>
  );
};

export default InventoryPage;
