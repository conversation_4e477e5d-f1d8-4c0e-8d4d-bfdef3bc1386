import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import axios from 'axios';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import Badge from '../../components/shared/Badge';
import Link from '../../components/feed-components/Link';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Download,
  FileText,
  FileSpreadsheet,
  FileJson,
  Printer,
  ChevronDown
} from 'lucide-react';

const ChickenProcessReportPage = () => {
  const { language, translations } = useLanguage();
  const t = (key) => translations[key] || key;

  const [processData, setProcessData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [summary, setSummary] = useState({
    totalPurchases: 0,
    completedProcesses: 0,
    totalInvestment: 0,
    totalRevenue: 0,
    totalProfit: 0,
    profitMargin: 0
  });

  useEffect(() => {
    fetchProcessReport();
  }, []);

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportMenu && !event.target.closest('.relative')) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportMenu]);

  const fetchProcessReport = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const response = await axios.get('http://localhost:5432/api/v1/chickens/process-report', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        calculateProcessData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching process report:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProcessData = (rawData) => {
    const processMap = new Map();

    // Process the raw data from backend
    rawData.forEach(row => {
      if (!processMap.has(row.purchaseId)) {
        // Initialize purchase data
        processMap.set(row.purchaseId, {
          purchaseId: row.purchaseId,
          purchaseDate: row.purchaseDate,
          supplier: row.supplierName,
          initialQuantity: row.initialQuantity,
          purchaseCost: parseFloat(row.purchaseCost) || 0,
          pricePerChicken: parseFloat(row.purchasePricePerChicken) || 0,

          // Process stages
          allocations: [],
          buybacks: [],
          distributions: [],

          // Calculations
          totalAllocated: 0,
          totalBoughtBack: 0,
          totalDistributed: 0,
          totalRevenue: 0,
          totalCosts: parseFloat(row.purchaseCost) || 0,
          netProfit: 0,
          profitMargin: 0,

          // Status - determined by furthest stage reached
          status: row.processStatus || 'purchased',
          completionRate: 0,
          losses: 0,
          isCompleted: false
        });
      }

      const process = processMap.get(row.purchaseId);

      // Add allocation data
      if (row.allocationId && !process.allocations.find(a => a.id === row.allocationId)) {
        process.allocations.push({
          id: row.allocationId,
          allocationDate: row.allocationDate,
          quantity: row.allocatedQuantity,
          farmName: row.farmName,
          farmOwner: row.farmOwner
        });
        process.totalAllocated += row.allocatedQuantity || 0;
      }

      // Add buyback data
      if (row.buybackId && !process.buybacks.find(b => b.id === row.buybackId)) {
        process.buybacks.push({
          id: row.buybackId,
          buybackDate: row.buybackDate,
          quantity: row.buybackQuantity,
          farmName: row.farmName,
          daysCompleted: row.daysCompleted
        });
        process.totalBoughtBack += row.buybackQuantity || 0;
        process.totalRevenue += parseFloat(row.buybackRevenue) || 0;
      }

      // Add distribution data
      if (row.distributionId && !process.distributions.find(d => d.id === row.distributionId)) {
        process.distributions.push({
          id: row.distributionId,
          distributionDate: row.distributionDate,
          quantity: row.distributedQuantity,
          shopName: row.shopName
        });
        process.totalDistributed += row.distributedQuantity || 0;
        process.totalRevenue += parseFloat(row.distributionRevenue) || 0;

        // Mark as completed when distribution exists
        process.isCompleted = true;
        process.status = 'completed';
      }
    });

    // Calculate final metrics for each process
    const processArray = Array.from(processMap.values()).map(process => {
      // Calculate losses (chickens that were allocated but not bought back)
      process.losses = Math.max(0, process.totalAllocated - process.totalBoughtBack);

      // Calculate net profit
      process.netProfit = process.totalRevenue - process.purchaseCost;

      // Calculate profit margin
      process.profitMargin = process.purchaseCost > 0
        ? ((process.netProfit / process.purchaseCost) * 100)
        : 0;

      // Calculate completion rate (percentage of chickens that reached final distribution)
      process.completionRate = process.initialQuantity > 0
        ? ((process.totalDistributed / process.initialQuantity) * 100)
        : 0;

      return process;
    });



    setProcessData(processArray);

    // Calculate summary
    const totalInvestment = processArray.reduce((sum, p) => sum + p.purchaseCost, 0);
    const totalRevenue = processArray.reduce((sum, p) => sum + p.totalRevenue, 0);
    const totalProfit = totalRevenue - totalInvestment;
    const profitMargin = totalInvestment > 0 ? ((totalProfit / totalInvestment) * 100) : 0;

    setSummary({
      totalPurchases: processArray.length,
      completedProcesses: processArray.filter(p => p.isCompleted || p.status === 'completed').length,
      totalInvestment,
      totalRevenue,
      totalProfit,
      profitMargin
    });
  };

  const formatCurrency = (amount) => {
    const numericAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status, isCompleted) => {
    const statusConfig = {
      purchased: { variant: 'info', label: 'Purchased Only' },
      allocated: { variant: 'warning', label: 'At Farm' },
      bought_back: { variant: 'default', label: 'Bought Back' },
      completed: { variant: 'success', label: '✅ COMPLETED' },
      distributed: { variant: 'success', label: '✅ COMPLETED' }
    };

    // Override status if process is completed
    const finalStatus = isCompleted ? 'completed' : status;
    const config = statusConfig[finalStatus] || statusConfig.purchased;

    return (
      <Badge variant={config.variant}>
        {config.label}
      </Badge>
    );
  };

  const getProfitIcon = (profit) => {
    if (profit > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (profit < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <DollarSign className="h-4 w-4 text-gray-500" />;
  };

  // Filter options for process status
  const filterOptions = [
    { value: 'all', label: 'All Processes', count: processData.length },
    { value: 'completed', label: '✅ Completed', count: processData.filter(p => p.isCompleted || p.status === 'completed').length },
    { value: 'bought_back', label: '🔄 At Buyback', count: processData.filter(p => p.status === 'bought_back').length },
    { value: 'allocated', label: '🚚 At Farm', count: processData.filter(p => p.status === 'allocated').length },
    { value: 'purchased', label: '🛒 Purchased Only', count: processData.filter(p => p.status === 'purchased').length },
    { value: 'profitable', label: '💰 Profitable', count: processData.filter(p => p.netProfit > 0).length },
    { value: 'losses', label: '⚠️ With Losses', count: processData.filter(p => p.netProfit < 0 || p.losses > 0).length }
  ];

  // Get filtered data based on current filter
  const getFilteredData = () => {
    switch (statusFilter) {
      case 'completed':
        return processData.filter(p => p.isCompleted || p.status === 'completed');
      case 'bought_back':
        return processData.filter(p => p.status === 'bought_back');
      case 'allocated':
        return processData.filter(p => p.status === 'allocated');
      case 'purchased':
        return processData.filter(p => p.status === 'purchased');
      case 'profitable':
        return processData.filter(p => p.netProfit > 0);
      case 'losses':
        return processData.filter(p => p.netProfit < 0 || p.losses > 0);
      default:
        return processData;
    }
  };

  const filteredProcessData = getFilteredData();

  // Export Functions
  const exportToPDF = () => {
    const dataToExport = filteredProcessData;
    const filterLabel = filterOptions.find(f => f.value === statusFilter)?.label || 'All Processes';

    const printContent = document.createElement('div');
    printContent.innerHTML = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h1 style="color: #333; text-align: center; margin-bottom: 30px;">Chicken Process Report</h1>
        <div style="text-align: center; margin-bottom: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
          <strong>Filter Applied:</strong> ${filterLabel} (${dataToExport.length} records)
        </div>
        <div style="margin-bottom: 30px;">
          <h2 style="color: #666; border-bottom: 2px solid #FF6B00; padding-bottom: 10px;">Summary</h2>
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0;">
            <div><strong>Total Records:</strong> ${dataToExport.length}</div>
            <div><strong>Total Investment:</strong> ${formatCurrency(dataToExport.reduce((sum, p) => sum + p.purchaseCost, 0))}</div>
            <div><strong>Total Revenue:</strong> ${formatCurrency(dataToExport.reduce((sum, p) => sum + p.totalRevenue, 0))}</div>
            <div><strong>Net Profit:</strong> ${formatCurrency(dataToExport.reduce((sum, p) => sum + p.netProfit, 0))}</div>
          </div>
        </div>
        <div>
          <h2 style="color: #666; border-bottom: 2px solid #FF6B00; padding-bottom: 10px;">Detailed Process Data</h2>
          <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
              <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Date</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Supplier</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Quantity</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Investment</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Revenue</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Profit</th>
                <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Status</th>
              </tr>
            </thead>
            <tbody>
              ${dataToExport.map(process => `
                <tr>
                  <td style="border: 1px solid #ddd; padding: 8px;">${formatDate(process.purchaseDate)}</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${process.supplier}</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${process.initialQuantity}</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(process.purchaseCost)}</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(process.totalRevenue)}</td>
                  <td style="border: 1px solid #ddd; padding: 8px; color: ${process.netProfit >= 0 ? 'green' : 'red'};">${formatCurrency(process.netProfit)}</td>
                  <td style="border: 1px solid #ddd; padding: 8px;">${process.status}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
          Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
        </div>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Chicken Process Report</title>
          <style>
            @media print {
              body { margin: 0; }
              @page { margin: 1in; }
            }
          </style>
        </head>
        <body>
          ${printContent.innerHTML}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const exportToCSV = () => {
    const dataToExport = filteredProcessData;
    const filterLabel = filterOptions.find(f => f.value === statusFilter)?.label || 'All Processes';

    const headers = [
      'Purchase Date',
      'Supplier',
      'Initial Quantity',
      'Investment (AFN)',
      'Allocated',
      'Bought Back',
      'Distributed',
      'Losses',
      'Revenue (AFN)',
      'Net Profit (AFN)',
      'Profit Margin (%)',
      'Completion Rate (%)',
      'Status'
    ];

    const csvData = dataToExport.map(process => [
      formatDate(process.purchaseDate),
      process.supplier,
      process.initialQuantity,
      process.purchaseCost,
      process.totalAllocated,
      process.totalBoughtBack,
      process.totalDistributed,
      process.losses,
      process.totalRevenue,
      process.netProfit,
      process.profitMargin.toFixed(2),
      process.completionRate.toFixed(2),
      process.status
    ]);

    const csvContent = [
      [`# Chicken Process Report - ${filterLabel} (${dataToExport.length} records)`],
      [`# Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`],
      [],
      headers,
      ...csvData
    ]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `chicken_process_report_${statusFilter}_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const exportToExcel = () => {
    const dataToExport = filteredProcessData;
    const filterLabel = filterOptions.find(f => f.value === statusFilter)?.label || 'All Processes';

    const headers = [
      'Purchase Date',
      'Supplier',
      'Initial Quantity',
      'Investment (AFN)',
      'Allocated',
      'Bought Back',
      'Distributed',
      'Losses',
      'Revenue (AFN)',
      'Net Profit (AFN)',
      'Profit Margin (%)',
      'Completion Rate (%)',
      'Status'
    ];

    const excelData = dataToExport.map(process => ({
      'Purchase Date': formatDate(process.purchaseDate),
      'Supplier': process.supplier,
      'Initial Quantity': process.initialQuantity,
      'Investment (AFN)': process.purchaseCost,
      'Allocated': process.totalAllocated,
      'Bought Back': process.totalBoughtBack,
      'Distributed': process.totalDistributed,
      'Losses': process.losses,
      'Revenue (AFN)': process.totalRevenue,
      'Net Profit (AFN)': process.netProfit,
      'Profit Margin (%)': process.profitMargin.toFixed(2),
      'Completion Rate (%)': process.completionRate.toFixed(2),
      'Status': process.status
    }));

    // Create Excel-compatible HTML
    const excelHTML = `
      <html>
        <head>
          <meta charset="utf-8">
          <title>Chicken Process Report - ${filterLabel}</title>
        </head>
        <body>
          <h1>Chicken Process Report</h1>
          <p><strong>Filter:</strong> ${filterLabel} (${dataToExport.length} records)</p>
          <p><strong>Generated:</strong> ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          <table border="1">
            <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
            ${excelData.map(row =>
              `<tr>${headers.map(h => `<td>${row[h] || ''}</td>`).join('')}</tr>`
            ).join('')}
          </table>
        </body>
      </html>
    `;

    const blob = new Blob([excelHTML], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `chicken_process_report_${statusFilter}_${new Date().toISOString().split('T')[0]}.xls`;
    link.click();
  };

  const exportToJSON = () => {
    const dataToExport = filteredProcessData;
    const filterLabel = filterOptions.find(f => f.value === statusFilter)?.label || 'All Processes';

    const exportData = {
      reportInfo: {
        title: 'Chicken Process Report',
        generatedAt: new Date().toISOString(),
        filter: {
          type: statusFilter,
          label: filterLabel,
          recordCount: dataToExport.length
        },
        summary: {
          totalRecords: dataToExport.length,
          totalInvestment: dataToExport.reduce((sum, p) => sum + p.purchaseCost, 0),
          totalRevenue: dataToExport.reduce((sum, p) => sum + p.totalRevenue, 0),
          netProfit: dataToExport.reduce((sum, p) => sum + p.netProfit, 0),
          completedProcesses: dataToExport.filter(p => p.isCompleted).length,
          profitableProcesses: dataToExport.filter(p => p.netProfit > 0).length,
          processesWithLosses: dataToExport.filter(p => p.netProfit < 0 || p.losses > 0).length
        }
      },
      processData: dataToExport.map(process => ({
        purchaseId: process.purchaseId,
        purchaseDate: process.purchaseDate,
        supplier: process.supplier,
        initialQuantity: process.initialQuantity,
        purchaseCost: process.purchaseCost,
        totalAllocated: process.totalAllocated,
        totalBoughtBack: process.totalBoughtBack,
        totalDistributed: process.totalDistributed,
        losses: process.losses,
        totalRevenue: process.totalRevenue,
        netProfit: process.netProfit,
        profitMargin: process.profitMargin,
        completionRate: process.completionRate,
        status: process.status,
        isCompleted: process.isCompleted
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `chicken_process_report_${statusFilter}_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  const printReport = () => {
    window.print();
  };

  const exportOptions = [
    {
      label: 'Export as PDF',
      icon: <FileText className="h-4 w-4" />,
      action: exportToPDF,
      description: 'Formatted PDF document'
    },
    {
      label: 'Export as CSV',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      action: exportToCSV,
      description: 'Comma-separated values'
    },
    {
      label: 'Export as Excel',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      action: exportToExcel,
      description: 'Microsoft Excel format'
    },
    {
      label: 'Export as JSON',
      icon: <FileJson className="h-4 w-4" />,
      action: exportToJSON,
      description: 'JSON data format'
    },
    {
      label: 'Print Report',
      icon: <Printer className="h-4 w-4" />,
      action: printReport,
      description: 'Print current page'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_process_report') || 'Loading process report...'}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          .print-break {
            page-break-after: always;
          }
          body {
            font-size: 12px;
          }
          table {
            font-size: 10px;
          }
        }
      `}</style>

    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('chicken_process_report') || 'Chicken Process Report'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('complete_lifecycle_analysis') || 'Complete lifecycle analysis with profit/loss tracking'}
          </p>
          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Process Completion:</strong> A process is marked as "COMPLETED" when chickens have gone through all 4 stages:
              Purchase → Farm Allocation → Buyback → Shop Distribution
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 no-print">
          <Link
            to="/admin/chickens"
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('back_to_dashboard') || 'Back to Dashboard'}
          </Link>
          <div className="relative">
            <button
              onClick={() => setShowExportMenu(!showExportMenu)}
              className="flex items-center px-4 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#e55a00] transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              {t('export_report') || 'Export Report'}
              <ChevronDown className="h-4 w-4 ml-2" />
            </button>

            {showExportMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div className="py-2">
                  {exportOptions.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        option.action();
                        setShowExportMenu(false);
                      }}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
                    >
                      <div className="flex items-center text-gray-600 dark:text-gray-300 mr-3">
                        {option.icon}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {option.label}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {option.description}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_purchases') || 'Total Purchases'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {summary.totalPurchases}
                </h3>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('completed_processes') || 'Completed Processes'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {summary.completedProcesses}
                </h3>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_investment') || 'Total Investment'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(summary.totalInvestment)}
                </h3>
              </div>
              <DollarSign className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className={`bg-gradient-to-br ${summary.totalProfit >= 0 ? 'from-green-500/10 to-green-500/5 border-green-500/20' : 'from-red-500/10 to-red-500/5 border-red-500/20'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('net_profit') || 'Net Profit'}
                </p>
                <h3 className={`text-2xl font-bold ${summary.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(summary.totalProfit)}
                </h3>
                <p className={`text-xs ${summary.totalProfit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {summary.profitMargin.toFixed(1)}% margin
                </p>
              </div>
              {getProfitIcon(summary.totalProfit)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter Buttons */}
      <Card className="no-print">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            {t('filter_by_status') || 'Filter by Process Status'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {filterOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setStatusFilter(option.value)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  statusFilter === option.value
                    ? 'bg-[#FF6B00] text-white border-[#FF6B00]'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {option.label}
                <span className="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                  {option.count}
                </span>
              </button>
            ))}
          </div>
          <div className="mt-4 text-sm text-gray-600">
            <strong>Showing:</strong> {filteredProcessData.length} of {processData.length} processes
            {statusFilter !== 'all' && (
              <button
                onClick={() => setStatusFilter('all')}
                className="ml-2 text-[#FF6B00] hover:underline"
              >
                Clear filter
              </button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Process Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            {t('detailed_process_analysis') || 'Detailed Process Analysis'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('purchase_date') || 'Purchase Date'}</TableHead>
                  <TableHead>{t('supplier') || 'Supplier'}</TableHead>
                  <TableHead>{t('initial_qty') || 'Initial Qty'}</TableHead>
                  <TableHead>{t('investment') || 'Investment'}</TableHead>
                  <TableHead>{t('allocated') || 'Allocated'}</TableHead>
                  <TableHead>{t('bought_back') || 'Bought Back'}</TableHead>
                  <TableHead>{t('distributed') || 'Distributed'}</TableHead>
                  <TableHead>{t('losses') || 'Losses'}</TableHead>
                  <TableHead>{t('revenue') || 'Revenue'}</TableHead>
                  <TableHead>{t('profit_loss') || 'Profit/Loss'}</TableHead>
                  <TableHead>{t('completion') || 'Completion'}</TableHead>
                  <TableHead>{t('status') || 'Status'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProcessData.length > 0 ? (
                  filteredProcessData.map((process) => (
                    <TableRow key={process.purchaseId}>
                      <TableCell>{formatDate(process.purchaseDate)}</TableCell>
                      <TableCell>
                        <div className="font-medium">{process.supplier}</div>
                      </TableCell>
                      <TableCell className="font-medium">{process.initialQuantity}</TableCell>
                      <TableCell>{formatCurrency(process.purchaseCost)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="font-medium">{process.totalAllocated}</span>
                          {process.allocations.length > 0 && (
                            <span className="ml-1 text-xs text-gray-500">
                              ({process.allocations.length} farms)
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{process.totalBoughtBack}</TableCell>
                      <TableCell className="font-medium">{process.totalDistributed}</TableCell>
                      <TableCell>
                        {process.losses > 0 ? (
                          <div className="flex items-center text-red-600">
                            <AlertTriangle className="h-4 w-4 mr-1" />
                            <span className="font-medium">{process.losses}</span>
                          </div>
                        ) : (
                          <span className="text-green-600 font-medium">0</span>
                        )}
                      </TableCell>
                      <TableCell>{formatCurrency(process.totalRevenue)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getProfitIcon(process.netProfit)}
                          <div className="ml-2">
                            <div className={`font-medium ${process.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatCurrency(process.netProfit)}
                            </div>
                            <div className={`text-xs ${process.netProfit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {process.profitMargin.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className={`h-2 rounded-full ${process.completionRate >= 100 ? 'bg-green-500' : process.completionRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'}`}
                              style={{ width: `${Math.min(process.completionRate, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-xs font-medium">
                            {process.completionRate.toFixed(0)}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(process.status, process.isCompleted)}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={12} className="text-center py-4">
                      {t('no_process_data') || 'No process data available'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Process Breakdown Cards */}
      {filteredProcessData.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">
                {t('top_profitable_purchases') || 'Top Profitable Purchases'}
                <span className="text-sm font-normal text-gray-500 ml-2">
                  (from {filterOptions.find(f => f.value === statusFilter)?.label || 'All'})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredProcessData
                  .filter(p => p.netProfit > 0)
                  .sort((a, b) => b.netProfit - a.netProfit)
                  .slice(0, 5)
                  .map((process) => (
                    <div key={process.purchaseId} className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                      <div>
                        <div className="font-medium">{process.supplier}</div>
                        <div className="text-sm text-gray-500">
                          {formatDate(process.purchaseDate)} • {process.initialQuantity} chickens
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">
                          {formatCurrency(process.netProfit)}
                        </div>
                        <div className="text-xs text-green-500">
                          {process.profitMargin.toFixed(1)}% margin
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Loss Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">
                {t('loss_analysis') || 'Loss Analysis'}
                <span className="text-sm font-normal text-gray-500 ml-2">
                  (from {filterOptions.find(f => f.value === statusFilter)?.label || 'All'})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredProcessData
                  .filter(p => p.netProfit < 0 || p.losses > 0)
                  .sort((a, b) => a.netProfit - b.netProfit)
                  .slice(0, 5)
                  .map((process) => (
                    <div key={process.purchaseId} className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                      <div>
                        <div className="font-medium">{process.supplier}</div>
                        <div className="text-sm text-gray-500">
                          {formatDate(process.purchaseDate)} • {process.losses} chickens lost
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-red-600">
                          {formatCurrency(process.netProfit)}
                        </div>
                        <div className="text-xs text-red-500">
                          {process.profitMargin.toFixed(1)}% margin
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
    </>
  );
};

export default ChickenProcessReportPage;
