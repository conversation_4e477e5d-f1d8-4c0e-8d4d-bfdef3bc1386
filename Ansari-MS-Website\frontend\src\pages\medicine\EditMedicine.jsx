/* eslint-disable react/prop-types */
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Button from '../../components/Button';

export default function EditMedicine({ medicines = [], setMedicines }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const medicineId = parseInt(id, 10);

  // Ensure medicines is not undefined before accessing .find()
  const medicineData = medicines?.find((med) => med.id === medicineId) || null;

  const [formData, setFormData] = useState(
    medicineData || {
      farm: '',
      location: '',
      medicine: 0,
      value: 0,
      percentage: 0,
    }
  );

  useEffect(() => {
    if (medicineData) {
      setFormData(medicineData);
    }
  }, [medicineData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!medicineData) return;

    setMedicines((prev) => prev.map((med) => (med.id === medicineId ? { ...med, ...formData } : med)));
    navigate('/admin/medicine');
  };

  if (!medicineData) {
    return (
      <div className="p-6">
        <h1 className="text-xl font-semibold text-red-600">Error: Medicine not found!</h1>
        <Button variant="secondary" onClick={() => navigate('/admin/medicine')}>
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      <h1 className="text-2xl font-semibold">Edit Medicine</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        {['farm', 'location'].map((field) => (
          <div key={field}>
            <label className="block text-sm font-medium text-gray-700">{field}</label>
            <input
              type="text"
              name={field}
              value={formData[field]}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        ))}
        {['medicine', 'value', 'percentage'].map((field) => (
          <div key={field}>
            <label className="block text-sm font-medium text-gray-700">{field} (Numeric)</label>
            <input
              type="number"
              name={field}
              value={formData[field]}
              onChange={handleChange}
              required
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        ))}
        <div className="flex space-x-2 mt-4">
          <Button type="submit" variant="primary">
            Save Changes
          </Button>
          <Button type="button" variant="secondary" onClick={() => navigate('/admin/medicine')}>
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}
