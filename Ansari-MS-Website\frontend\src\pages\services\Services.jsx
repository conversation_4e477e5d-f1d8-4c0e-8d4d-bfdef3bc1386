

"use client"

import { useState } from "react"
import { Search, Filter } from "lucide-react"
import { Link } from "react-router-dom"
import { useServices } from "../../contexts/ServicesContext"

const Services = () => {
  const { services, loading } = useServices()
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")

  // Filter and sort services
  const filteredServices = services
    .filter((service) => {
      // Search filter
      const matchesSearch = service.Title?.toLowerCase().includes(searchQuery.toLowerCase())

      // Category filter
      const matchesCategory = categoryFilter === "all" || service.Category === categoryFilter

      // Status filter
      const matchesStatus = statusFilter === "all" || service.Status === statusFilter

      return matchesSearch && matchesCategory && matchesStatus
    })
    .sort((a, b) => {
      // Sort by selected field
      if (sortBy === "name") {
        return a.Title?.localeCompare(b.Title)
      } else if (sortBy === "price") {
        return (a.Price || 0) - (b.Price || 0)
      }
      return 0
    })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Services</h1>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search services..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <Filter size={20} />
              Filters
            </button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <select
              className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <select
              className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="all">All Categories</option>
              <option value="Premium Hens">Premium Hens</option>
              <option value="Baby Chickens">Baby Chickens</option>
              <option value="Wholesale">Wholesale</option>
            </select>
            <select
              className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
            </select>
          </div>
        )}
      </div>

      {/* Services Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-700">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Service Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredServices.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No services found
                  </td>
                </tr>
              ) : (
                filteredServices.map((service) => (
                  <tr key={service.SR_Id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{service.Title}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 dark:text-gray-300">
                        {service.Description?.length > 50
                          ? `${service.Description.substring(0, 50)}...`
                          : service.Description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">${service.Price}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-300">{service.Category}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          service.Status === "active"
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {service.Status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        to={`/services/${service.SR_Id}`}
                        className="text-[#FF6B00] hover:text-[#FF8533] dark:hover:text-[#FF8533]"
                      >
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default Services


/////////////////////

// import React, { useState } from 'react';
// import { Search, Filter, Plus, MoreVertical, Edit, Trash2 } from 'lucide-react';
// import { Link } from 'react-router-dom';

// const Services = () => {
//   const [searchQuery, setSearchQuery] = useState('');
//   const [showFilters, setShowFilters] = useState(false);
//   const [activeMenu, setActiveMenu] = useState(null);

//   // Mock data for services
//   const services = [
//     {
//       id: 1,
//       name: 'Veterinary Consultation',
//       description: 'Professional veterinary services for farm animals',
//       price: '150.00',
//       duration: '1 hour',
//       status: 'active',
//     },
//     {
//       id: 2,
//       name: 'Feed Supply',
//       description: 'Quality animal feed delivery service',
//       price: '500.00',
//       duration: '1 day',
//       status: 'active',
//     },
//     {
//       id: 3,
//       name: 'Farm Equipment Rental',
//       description: 'Rental of essential farming equipment',
//       price: '200.00',
//       duration: '1 day',
//       status: 'inactive',
//     },
//     {
//       id: 4,
//       name: 'Animal Transportation',
//       description: 'Safe and comfortable animal transportation service',
//       price: '300.00',
//       duration: '1 day',
//       status: 'active',
//     },
//     {
//       id: 5,
//       name: 'Farm Maintenance',
//       description: 'Regular maintenance and cleaning services',
//       price: '400.00',
//       duration: '1 day',
//       status: 'active',
//     },
//   ];

//   const filteredServices = services.filter((service) => service.name.toLowerCase().includes(searchQuery.toLowerCase()));

//   const toggleMenu = (id) => {
//     setActiveMenu(activeMenu === id ? null : id);
//   };

//   return (
//     <div className="p-6">
//       <div className="flex justify-between items-center mb-6">
//         <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Services</h1>
//         <Link
//           to="/admin/services/add"
//           className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-[#FF8533] transition-colors"
//         >
//           <Plus size={20} />
//           Add Service
//         </Link>
//       </div>

//       {/* Filters and Search */}
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
//         <div className="flex flex-col md:flex-row gap-4">
//           <div className="flex-1">
//             <div className="relative">
//               <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
//               <input
//                 type="text"
//                 placeholder="Search services..."
//                 value={searchQuery}
//                 onChange={(e) => setSearchQuery(e.target.value)}
//                 className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               />
//             </div>
//           </div>
//           <div className="flex gap-2">
//             <button
//               onClick={() => setShowFilters(!showFilters)}
//               className="flex items-center gap-2 px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
//             >
//               <Filter size={20} />
//               Filters
//             </button>
//           </div>
//         </div>

//         {/* Advanced Filters */}
//         {showFilters && (
//           <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
//             <select className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white">
//               <option value="">Status</option>
//               <option value="active">Active</option>
//               <option value="inactive">Inactive</option>
//             </select>
//             <select className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white">
//               <option value="">Duration</option>
//               <option value="1 hour">1 Hour</option>
//               <option value="1 day">1 Day</option>
//               <option value="1 week">1 Week</option>
//             </select>
//             <select className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white">
//               <option value="">Sort By</option>
//               <option value="name">Name</option>
//               <option value="price">Price</option>
//               <option value="duration">Duration</option>
//             </select>
//           </div>
//         )}
//       </div>

//       {/* Services Table */}
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
//         <div className="overflow-x-auto">
//           <table className="w-full">
//             <thead>
//               <tr className="bg-gray-50 dark:bg-gray-700">
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Service Name
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Description
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Price
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Duration
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Status
//                 </th>
//                 <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Actions
//                 </th>
//               </tr>
//             </thead>
//             <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
//               {filteredServices.map((service) => (
//                 <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <div className="text-sm font-medium text-gray-900 dark:text-white">{service.name}</div>
//                   </td>
//                   <td className="px-6 py-4">
//                     <div className="text-sm text-gray-500 dark:text-gray-300">{service.description}</div>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <div className="text-sm text-gray-900 dark:text-white">${service.price}</div>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <div className="text-sm text-gray-500 dark:text-gray-300">{service.duration}</div>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap">
//                     <span
//                       className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                         service.status === 'active'
//                           ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
//                           : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
//                       }`}
//                     >
//                       {service.status}
//                     </span>
//                   </td>
//                   <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
//                     <div className="relative">
//                       <button
//                         onClick={() => toggleMenu(service.id)}
//                         className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
//                       >
//                         <MoreVertical size={20} />
//                       </button>
//                       {activeMenu === service.id && (
//                         <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-10">
//                           <div className="py-1">
//                             <Link
//                               to={`/admin/services/edit/${service.id}`}
//                               className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
//                             >
//                               <Edit size={16} className="mr-2" />
//                               Edit
//                             </Link>
//                             <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
//                               <Trash2 size={16} className="mr-2" />
//                               Delete
//                             </button>
//                           </div>
//                         </div>
//                       )}
//                     </div>
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Services;
