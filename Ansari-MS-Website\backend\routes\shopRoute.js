import express from "express";
import shopController from "../controllers/shopController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/", authenticate, shopController.create);
router.get("/", shopController.getAll);
router.get("/:id", shopController.getById);
router.put("/:id", authenticate, shopController.update);
router.delete("/:id", authenticate, authorizeAdmin, shopController.delete);

export default router;
