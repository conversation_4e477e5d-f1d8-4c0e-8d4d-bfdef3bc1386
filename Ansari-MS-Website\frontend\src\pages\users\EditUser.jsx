"use client"

import { useState, useEffect } from "react"
import { useParams, useNavigate } from "react-router-dom"
import { Save, X, Trash2 } from "lucide-react"
import FileUploadComponent from "../../components/file-upload"
import { useUsers } from "../../contexts/UsersContext"

const EditUser = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { updateUser, deleteUser, loading: apiLoading } = useUsers()

  const [formData, setFormData] = useState({
    U_FirstName: "",
    U_LastName: "",
    U_Email: "",
    Role: "user",
    status: "active",
    address: "",
  })

  const [avatar, setAvatar] = useState(null)
  const [previewImage, setPreviewImage] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [errors, setErrors] = useState({})

  useEffect(() => {
    // Fetch user data from API
    const fetchUserData = async () => {
      try {
        const response = await fetch(`http://localhost:5432/api/v1/users/${id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        })

        if (!response.ok) {
          throw new Error("Failed to fetch user data")
        }

        const data = await response.json()

        if (data.success && data.user) {
          setFormData({
            U_FirstName: data.user.U_FirstName,
            U_LastName: data.user.U_LastName,
            U_Email: data.user.U_Email,
            Role: data.user.Role,
            status: data.user.status,
            address: data.user.address || "",
          })

          if (data.user.image) {
            setPreviewImage(`http://localhost:5432/public/images/users/${data.user.image}`)
          }
        } else {
          throw new Error(data.error || "Failed to fetch user data")
        }
      } catch (error) {
        console.error("Error fetching user:", error)
        setErrors({ form: "Failed to load user data. Please try again." })
      } finally {
        setLoading(false)
      }
    }

    fetchUserData()
  }, [id])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: null,
      }))
    }
  }

  const handleFileChange = (file) => {
    setAvatar(file)
  }

  const validateForm = () => {
    const newErrors = {}

    // Validate required fields
    if (!formData.U_FirstName.trim()) newErrors.U_FirstName = "First name is required"
    if (formData.U_FirstName.length < 3 || formData.U_FirstName.length > 25) {
      newErrors.U_FirstName = "First name must be between 3 and 25 characters"
    }

    // Last name is optional but if provided, must be valid
    if (formData.U_LastName && (formData.U_LastName.length < 3 || formData.U_LastName.length > 25)) {
      newErrors.U_LastName = "Last name must be between 3 and 25 characters"
    }

    // Validate email
    if (!formData.U_Email.trim()) {
      newErrors.U_Email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.U_Email)) {
      newErrors.U_Email = "Email is invalid"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) return

    setSaving(true)

    try {
      const userData = {
        U_FirstName: formData.U_FirstName,
        U_LastName: formData.U_LastName,
        U_Email: formData.U_Email,
        Role: formData.Role,
        status: formData.status,
        address: formData.address,
      }

      if (avatar) {
        userData.image = avatar
      }

      const response = await updateUser(id, userData)

      if (response.success) {
        navigate("/admin/users")
      } else {
        setErrors({ form: response.error || "Failed to update user" })
      }
    } catch (error) {
      console.error("Error updating user:", error)
      setErrors({ form: "An error occurred while updating the user" })
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    try {
      const response = await deleteUser(id)

      if (response && response.success) {
        setIsDeleteModalOpen(false)
        navigate("/admin/users")
      } else {
        setErrors({ form: response?.error || "Failed to delete user" })
        setIsDeleteModalOpen(false)
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      setErrors({ form: "An error occurred while deleting the user: " + error.message })
      setIsDeleteModalOpen(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-[#2C3E50]">Edit User</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setIsDeleteModalOpen(true)}
            className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 flex items-center"
          >
            <Trash2 size={18} className="mr-2" />
            Delete
          </button>
          <button
            onClick={() => navigate("/admin/users")}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center"
          >
            <X size={18} className="mr-2" />
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={saving || apiLoading}
            className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg hover:bg-[#D32F2F] transition-colors flex items-center"
          >
            {saving || apiLoading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <Save size={18} className="mr-2" />
                Update User
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        {errors.form && <div className="mb-6 p-4 rounded-lg bg-red-100 text-red-700">{errors.form}</div>}

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Personal Information */}
              <div>
                <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Personal Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="U_FirstName" className="block text-sm font-medium text-gray-700 mb-1">
                      First Name
                    </label>
                    <input
                      type="text"
                      id="U_FirstName"
                      name="U_FirstName"
                      value={formData.U_FirstName}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border ${
                        errors.U_FirstName ? "border-red-500" : "border-gray-300"
                      } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                      placeholder="Enter first name"
                    />
                    {errors.U_FirstName && <p className="mt-1 text-sm text-red-600">{errors.U_FirstName}</p>}
                  </div>
                  <div>
                    <label htmlFor="U_LastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="U_LastName"
                      name="U_LastName"
                      value={formData.U_LastName}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border ${
                        errors.U_LastName ? "border-red-500" : "border-gray-300"
                      } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                      placeholder="Enter last name"
                    />
                    {errors.U_LastName && <p className="mt-1 text-sm text-red-600">{errors.U_LastName}</p>}
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div>
                <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Account Information</h2>
                <div>
                  <label htmlFor="U_Email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="U_Email"
                    name="U_Email"
                    value={formData.U_Email}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 border ${
                      errors.U_Email ? "border-red-500" : "border-gray-300"
                    } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                    placeholder="<EMAIL>"
                  />
                  {errors.U_Email && <p className="mt-1 text-sm text-red-600">{errors.U_Email}</p>}
                </div>

                <div className="mt-4">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                    placeholder="Enter address"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {/* Avatar */}
              <div>
                <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Profile Picture</h2>
                <FileUploadComponent
                  onFileChange={handleFileChange}
                  previewImage={previewImage}
                  setPreviewImage={setPreviewImage}
                />
              </div>

              {/* Role */}
              <div>
                <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Role & Status</h2>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="Role" className="block text-sm font-medium text-gray-700 mb-1">
                      Role
                    </label>
                    <select
                      id="Role"
                      name="Role"
                      value={formData.Role}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                    >
                      <option value="admin">Admin</option>
                      <option value="user">User</option>
                      <option value="farmer">Farmer</option>
                      <option value="shopper">Shopper</option>
                    </select>
                  </div>

                  {/* Status */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center">
                        <input
                          id="status-active"
                          name="status"
                          type="radio"
                          value="active"
                          checked={formData.status === "active"}
                          onChange={handleChange}
                          className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                        />
                        <label htmlFor="status-active" className="ml-3 block text-sm font-medium text-gray-700">
                          Active
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="status-inactive"
                          name="status"
                          type="radio"
                          value="inactive"
                          checked={formData.status === "inactive"}
                          onChange={handleChange}
                          className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                        />
                        <label htmlFor="status-inactive" className="ml-3 block text-sm font-medium text-gray-700">
                          Inactive
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold text-[#2C3E50] mb-4">Confirm Delete</h3>
            <p className="mb-6">Are you sure you want to delete this user? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
              >
                Cancel
              </button>
              <button onClick={handleDelete} className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EditUser



// "use client"

// import { useState, useEffect } from "react"
// import { useParams, useNavigate } from "react-router-dom"
// import { Save, X, Trash2 } from "lucide-react"
// import FileUploadComponent from "../../components/file-upload"

// const EditUser = () => {
//   const { id } = useParams()
//   const navigate = useNavigate()
//   const [formData, setFormData] = useState({
//     firstName: "",
//     lastName: "",
//     email: "",
//     role: "staff",
//     status: "active",
//   })
//   // this is edit
//   const [avatar, setAvatar] = useState(null)
//   const [previewImage, setPreviewImage] = useState(null)
//   const [loading, setLoading] = useState(true)
//   const [saving, setSaving] = useState(false)
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
//   const [errors, setErrors] = useState({})

//   useEffect(() => {
//     // In a real app, you would fetch the user data from your API
//     // Simulating API call
//     setTimeout(() => {
//       const mockUser = {
//         id: Number.parseInt(id),
//         firstName: "John",
//         lastName: "Doe",
//         email: "<EMAIL>",
//         role: "admin",
//         status: "active",
//         avatar: "/placeholder.svg?height=200&width=200",
//       }

//       setFormData({
//         firstName: mockUser.firstName,
//         lastName: mockUser.lastName,
//         email: mockUser.email,
//         role: mockUser.role,
//         status: mockUser.status,
//       })
//       setPreviewImage(mockUser.avatar)
//       setLoading(false)
//     }, 800)
//   }, [id])

//   const handleChange = (e) => {
//     const { name, value } = e.target
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))

//     // Clear error when field is edited
//     if (errors[name]) {
//       setErrors((prev) => ({
//         ...prev,
//         [name]: null,
//       }))
//     }
//   }

//   const handleFileChange = (file) => {
//     setAvatar(file)
//   }

//   const validateForm = () => {
//     const newErrors = {}

//     // Validate required fields
//     if (!formData.firstName.trim()) newErrors.firstName = "First name is required"
//     if (!formData.lastName.trim()) newErrors.lastName = "Last name is required"

//     // Validate email
//     if (!formData.email.trim()) {
//       newErrors.email = "Email is required"
//     } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
//       newErrors.email = "Email is invalid"
//     }

//     setErrors(newErrors)
//     return Object.keys(newErrors).length === 0
//   }

//   const handleSubmit = async (e) => {
//     e.preventDefault()

//     if (!validateForm()) return

//     setSaving(true)

//     try {
//       // In a real app, you would update the user in your API
//       // Simulating API call
//       setTimeout(() => {
//         console.log("Form submitted:", { ...formData, avatar })
//         setSaving(false)
//         navigate("/admin/users")
//       }, 1500)
//     } catch (error) {
//       console.error("Error updating user:", error)
//       setSaving(false)
//     }
//   }

//   const handleDelete = () => {
//     // In a real app, you would delete the user from your API
//     // Simulating API call
//     setTimeout(() => {
//       setIsDeleteModalOpen(false)
//       navigate("/admin/users")
//     }, 800)
//   }

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     )
//   }

//   return (
//     <div>
//       <div className="flex justify-between items-center mb-6">
//         <h1 className="text-2xl font-bold text-[#2C3E50]">Edit User</h1>
//         <div className="flex space-x-3">
//           <button
//             onClick={() => setIsDeleteModalOpen(true)}
//             className="px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 flex items-center"
//           >
//             <Trash2 size={18} className="mr-2" />
//             Delete
//           </button>
//           <button
//             onClick={() => navigate("/admin/users")}
//             className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center"
//           >
//             <X size={18} className="mr-2" />
//             Cancel
//           </button>
//           <button
//             onClick={handleSubmit}
//             disabled={saving}
//             className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg hover:bg-[#D32F2F] transition-colors flex items-center"
//           >
//             {saving ? (
//               <>
//                 <svg
//                   className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
//                   xmlns="http://www.w3.org/2000/svg"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                 >
//                   <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                   <path
//                     className="opacity-75"
//                     fill="currentColor"
//                     d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                   ></path>
//                 </svg>
//                 Saving...
//               </>
//             ) : (
//               <>
//                 <Save size={18} className="mr-2" />
//                 Update User
//               </>
//             )}
//           </button>
//         </div>
//       </div>

//       <div className="bg-white rounded-lg shadow-md p-6">
//         <form onSubmit={handleSubmit}>
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div className="md:col-span-2 space-y-6">
//               {/* Personal Information */}
//               <div>
//                 <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Personal Information</h2>
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                   <div>
//                     <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
//                       First Name
//                     </label>
//                     <input
//                       type="text"
//                       id="firstName"
//                       name="firstName"
//                       value={formData.firstName}
//                       onChange={handleChange}
//                       className={`w-full px-4 py-2 border ${
//                         errors.firstName ? "border-red-500" : "border-gray-300"
//                       } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                       placeholder="Enter first name"
//                     />
//                     {errors.firstName && <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>}
//                   </div>
//                   <div>
//                     <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
//                       Last Name
//                     </label>
//                     <input
//                       type="text"
//                       id="lastName"
//                       name="lastName"
//                       value={formData.lastName}
//                       onChange={handleChange}
//                       className={`w-full px-4 py-2 border ${
//                         errors.lastName ? "border-red-500" : "border-gray-300"
//                       } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                       placeholder="Enter last name"
//                     />
//                     {errors.lastName && <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>}
//                   </div>
//                 </div>
//               </div>

//               {/* Account Information */}
//               <div>
//                 <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Account Information</h2>
//                 <div>
//                   <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
//                     Email Address
//                   </label>
//                   <input
//                     type="email"
//                     id="email"
//                     name="email"
//                     value={formData.email}
//                     onChange={handleChange}
//                     className={`w-full px-4 py-2 border ${
//                       errors.email ? "border-red-500" : "border-gray-300"
//                     } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                     placeholder="<EMAIL>"
//                   />
//                   {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
//                 </div>
//               </div>
//             </div>

//             <div className="space-y-6">
//               {/* Avatar */}
//               <div>
//                 <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Profile Picture</h2>
//                 <FileUploadComponent
//                   onFileChange={handleFileChange}
//                   previewImage={previewImage}
//                   setPreviewImage={setPreviewImage}
//                 />
//               </div>

//               {/* Role */}
//               <div>
//                 <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Role & Status</h2>
//                 <div className="space-y-4">
//                   <div>
//                     <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
//                       Role
//                     </label>
//                     <select
//                       id="role"
//                       name="role"
//                       value={formData.role}
//                       onChange={handleChange}
//                       className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                     >
//                       <option value="admin">Admin</option>
//                       <option value="manager">Manager</option>
//                       <option value="staff">Staff</option>
//                     </select>
//                   </div>

//                   {/* Status */}
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
//                     <div className="mt-2 space-y-2">
//                       <div className="flex items-center">
//                         <input
//                           id="status-active"
//                           name="status"
//                           type="radio"
//                           value="active"
//                           checked={formData.status === "active"}
//                           onChange={handleChange}
//                           className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                         />
//                         <label htmlFor="status-active" className="ml-3 block text-sm font-medium text-gray-700">
//                           Active
//                         </label>
//                       </div>
//                       <div className="flex items-center">
//                         <input
//                           id="status-inactive"
//                           name="status"
//                           type="radio"
//                           value="inactive"
//                           checked={formData.status === "inactive"}
//                           onChange={handleChange}
//                           className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                         />
//                         <label htmlFor="status-inactive" className="ml-3 block text-sm font-medium text-gray-700">
//                           Inactive
//                         </label>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </form>
//       </div>

//       {/* Delete Confirmation Modal */}
//       {isDeleteModalOpen && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
//             <h3 className="text-lg font-bold text-[#2C3E50] mb-4">Confirm Delete</h3>
//             <p className="mb-6">Are you sure you want to delete this user? This action cannot be undone.</p>
//             <div className="flex justify-end space-x-3">
//               <button
//                 onClick={() => setIsDeleteModalOpen(false)}
//                 className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
//               >
//                 Cancel
//               </button>
//               <button onClick={handleDelete} className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
//                 Delete
//               </button>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   )
// }

// export default EditUser

