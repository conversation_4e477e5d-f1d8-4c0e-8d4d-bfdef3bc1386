import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const Pagination = React.forwardRef(
  ({ currentPage = 1, totalPages = 1, onPageChange, className = '', ...props }, ref) => {
    const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
    const showEllipsis = totalPages > 7;

    const getVisiblePages = () => {
      if (!showEllipsis) return pages;

      if (currentPage <= 4) {
        return [...pages.slice(0, 5), '...', totalPages];
      }

      if (currentPage >= totalPages - 3) {
        return [1, '...', ...pages.slice(totalPages - 5)];
      }

      return [1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages];
    };

    return (
      <nav
        ref={ref}
        className={`flex items-center justify-center space-x-1 ${className}`}
        aria-label="Pagination"
        {...props}
      >
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Previous page"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>

        {getVisiblePages().map((page, index) => (
          <React.Fragment key={index}>
            {page === '...' ? (
              <span className="px-4 py-2 text-gray-500 dark:text-gray-400">...</span>
            ) : (
              <button
                onClick={() => onPageChange(page)}
                className={`px-4 py-2 rounded-lg ${
                  currentPage === page
                    ? 'bg-[#FF6B00] text-white'
                    : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
                }`}
                aria-current={currentPage === page ? 'page' : undefined}
              >
                {page}
              </button>
            )}
          </React.Fragment>
        ))}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Next page"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </nav>
    );
  }
);

Pagination.displayName = 'Pagination';

export default Pagination;
