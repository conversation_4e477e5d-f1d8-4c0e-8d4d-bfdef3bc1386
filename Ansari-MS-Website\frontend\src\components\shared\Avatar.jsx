import React from 'react';
import { User } from 'lucide-react';

const Avatar = React.forwardRef(({ src, alt, size = 'md', variant = 'circle', className = '', ...props }, ref) => {
  const sizes = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-14 w-14',
    '2xl': 'h-16 w-16',
  };

  const variants = {
    circle: 'rounded-full',
    square: 'rounded-lg',
  };

  const baseStyles = 'inline-block overflow-hidden bg-gray-100 dark:bg-gray-800';

  if (src) {
    return (
      <img
        ref={ref}
        src={src}
        alt={alt}
        className={`${baseStyles} ${sizes[size]} ${variants[variant]} ${className}`}
        {...props}
      />
    );
  }

  return (
    <div
      ref={ref}
      className={`${baseStyles} ${sizes[size]} ${variants[variant]} flex items-center justify-center ${className}`}
      {...props}
    >
      <User className="h-1/2 w-1/2 text-gray-400 dark:text-gray-500" />
    </div>
  );
});

Avatar.displayName = 'Avatar';

export default Avatar;
