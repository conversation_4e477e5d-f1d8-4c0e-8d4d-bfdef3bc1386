"use client"

import { useState, useEffect, useMemo } from "react"
import { useParams, Link, useNavigate } from "react-router-dom"
import { useServices } from "../../contexts/ServicesContext"
import { ArrowLeft, Check } from "lucide-react"
import "slick-carousel/slick/slick.css"
import "slick-carousel/slick/slick-theme.css"

// Preload slick fonts
const preloadFonts = () => {
  const fonts = [
    "/node_modules/slick-carousel/slick/fonts/slick.woff",
    "/node_modules/slick-carousel/slick/fonts/slick.ttf",
    "/node_modules/slick-carousel/slick/fonts/slick.svg",
    "/node_modules/slick-carousel/slick/fonts/slick.eot",
  ]

  fonts.forEach((font) => {
    const link = document.createElement("link")
    link.rel = "preload"
    link.href = font
    link.as = "font"
    link.type = "font/woff"
    link.crossOrigin = "anonymous"
    document.head.appendChild(link)
  })
}

const ServiceDetails = () => {
  const { id } = useParams()
  const { services, getServiceById } = useServices()
  const [imageError, setImageError] = useState(false)
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)

  // Find service by ID
  const service = useMemo(() => {
    // Try to get the service using the context function
    const foundService = getServiceById(id)

    // If service is found, return it
    if (foundService) {
      setLoading(false)
      return foundService
    }

    // If not found, set loading to false and return null
    setLoading(false)
    return null
  }, [id, getServiceById])

  // Default placeholder as a data URI - a simple gray background with a camera icon
  const DEFAULT_PLACEHOLDER =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjE1MCIgaGVpZ2h0PSIxNTAiIGZpbGw9IiNFNUU3RUIiLz48cGF0aCBkPSJNNjUgNTVINzVMODAgNjBIOTBDOTIuNzYxNCA2MCA5NSA2Mi4yMzg2IDk1IDY1VjkwQzk1IDkyLjc2MTQgOTIuNzYxNCA5NSA5MCA5NUg2MEM1Ny4yMzg2IDk1IDU1IDkyLjc2MTQgNTUgOTBWNjVDNTUgNjIuMjM4NiA1Ny4yMzg2IDYwIDYwIDYwSDYyLjVMNjUgNTVaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZVdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48Y2lyY2xlIGN4PSI3NSIgY3k9Ijc1IiByPSIxMCIgc3Ryb2tlPSIjOUNBM0FGIiBzdHJva2VXaWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+"

  const getImageUrl = (image) => {
    if (!image || imageError) {
      return DEFAULT_PLACEHOLDER
    }

    // If it's a backend image path
    if (typeof image === "string" && !image.startsWith("data:") && !image.startsWith("http")) {
      return `/images/services/${image}`
    }

    if (typeof image === "string" && image.startsWith("data:image")) {
      return image
    }

    if (typeof image === "string" && (image.startsWith("http") || image.startsWith("/"))) {
      return image
    }

    return DEFAULT_PLACEHOLDER
  }

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0)
  }, [id])

  useEffect(() => {
    preloadFonts()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-muted to-white pt-20">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-muted to-white pt-20">
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center bg-card rounded-2xl shadow-card p-12 border border-border-color">
            <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47.881-6.08 2.33" />
              </svg>
            </div>
            <h1 className="text-3xl font-headingEn font-bold text-secondary mb-4">Service Not Found</h1>
            <p className="text-textprimary/70 font-bodyEn text-lg mb-8">The service you're looking for doesn't exist or has been removed.</p>
            <Link to="/services" className="inline-flex items-center text-primary hover:text-primary-hover font-bodyEn font-medium bg-primary/10 px-6 py-3 rounded-lg transition-colors">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Services
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Get related services from the same category
  const getRelatedServices = () => {
    if (!service || !service.Category) return []

    // Filter services from the same category, excluding the current service
    const categoryServices = services.filter((s) => s.Category === service.Category && s.SR_Id !== service.SR_Id)

    return categoryServices.slice(0, 2)
  }

  const relatedServices = getRelatedServices()

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted to-white pt-20">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Back to services link */}
        <Link to="/services" className="inline-flex items-center text-primary hover:text-primary-hover mb-8 font-bodyEn font-medium bg-card px-4 py-2 rounded-lg shadow-sm border border-border-color transition-colors">
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Services
        </Link>

        {/* Main Content Card */}
        <div className="bg-card rounded-2xl shadow-card-hover overflow-hidden mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
            {/* Service Image */}
            <div className="relative h-[400px] lg:h-[500px] bg-muted">
              <img
                src={`http://localhost:5432/public/images/services/${service.Image}`|| "/placeholder.svg"}
                alt={service.Title}
                className="w-full h-full object-cover transition-opacity duration-300"
                onError={() => setImageError(true)}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            {/* Service Details */}
            <div className="p-8 lg:p-12">
              <div className="mb-4">
                <span className="inline-block px-4 py-2 bg-primary text-white rounded-lg text-sm font-headingEn font-semibold uppercase tracking-wide">
                  {service.Category}
                </span>
              </div>

              <h1 className="text-3xl lg:text-4xl font-headingEn font-bold text-secondary mb-6">{service.Title}</h1>

              <div className="mb-6">
                <span className="text-3xl font-headingEn font-bold text-primary">${service.Price}</span>
              </div>

              <p className="text-textprimary font-bodyEn text-lg leading-relaxed mb-8">{service.Description}</p>

              {service.Features && service.Features.length > 0 && (
                <div className="mb-8">
                  <h2 className="text-xl font-headingEn font-semibold text-secondary mb-4">Key Features</h2>
                  <ul className="space-y-3">
                    {service.Features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="h-5 w-5 text-primary mr-3 mt-1 flex-shrink-0" />
                        <span className="text-textprimary font-bodyEn">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {service.Status === "active" && (
                <div className="mb-8">
                  <h2 className="text-xl font-headingEn font-semibold text-secondary mb-4">Availability</h2>
                  <div className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bodyEn font-medium bg-success/10 text-success border border-success/20">
                    ✓ Available Now
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="flex-1 bg-primary text-white px-6 py-3 rounded-lg font-headingEn font-semibold text-center hover:bg-primary-hover transition-colors shadow-card"
                >
                  Get Quote
                </Link>
                <Link
                  to="/contact"
                  className="flex-1 bg-card border border-border-color text-textprimary px-6 py-3 rounded-lg font-headingEn font-semibold text-center hover:bg-muted transition-colors"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Related Services */}
        {relatedServices.length > 0 && (
          <div className="mb-16">
            <h2 className="text-3xl font-headingEn font-bold text-secondary mb-8 text-center">Related Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {relatedServices.map((relatedService) => (
                <div
                  key={relatedService.SR_Id}
                  className="bg-card rounded-xl shadow-card hover:shadow-card-hover cursor-pointer transition-all duration-300 transform hover:-translate-y-1 border border-border-color overflow-hidden"
                  onClick={() => navigate(`/services/${relatedService.SR_Id}`)}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={`http://localhost:5432/public/images/services/${relatedService.Image}` || "/placeholder.svg"}
                      alt={relatedService.Title}
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      onError={() => setImageError(true)}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-primary text-white rounded-lg text-xs font-headingEn font-semibold">
                        {relatedService.Category}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-headingEn font-semibold text-secondary mb-2">{relatedService.Title}</h3>
                    <p className="text-textprimary/70 font-bodyEn text-sm mb-4 line-clamp-2">{relatedService.Description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-primary font-headingEn font-bold text-lg">${relatedService.Price}</span>
                      <button className="text-primary hover:text-primary-hover text-sm font-bodyEn font-medium">View Details →</button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Contact Information */}
          <div className="bg-card p-6 rounded-xl shadow-card border border-border-color hover:shadow-card-hover transition-shadow">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-headingEn font-semibold text-secondary mb-3">Contact Us</h3>
            <p className="text-textprimary/70 font-bodyEn mb-4">For inquiries about this service, please contact our customer service team.</p>
            <Link to="/contact" className="inline-flex items-center text-primary hover:text-primary-hover font-bodyEn font-medium">
              Get in Touch →
            </Link>
          </div>

          {/* Delivery Information */}
          <div className="bg-card p-6 rounded-xl shadow-card border border-border-color hover:shadow-card-hover transition-shadow">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="text-lg font-headingEn font-semibold text-secondary mb-3">Delivery</h3>
            <p className="text-textprimary/70 font-bodyEn">
              We offer delivery services within Kandahar. Contact us for delivery options and rates.
            </p>
          </div>

          {/* Support Information */}
          <div className="bg-card p-6 rounded-xl shadow-card border border-border-color hover:shadow-card-hover transition-shadow">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 11-9.75 9.75A9.75 9.75 0 0112 2.25z" />
              </svg>
            </div>
            <h3 className="text-lg font-headingEn font-semibold text-secondary mb-3">Support</h3>
            <p className="text-textprimary/70 font-bodyEn mb-4">Get professional support and guidance for all your poultry needs.</p>
            <Link to="/contact" className="inline-flex items-center text-primary hover:text-primary-hover font-bodyEn font-medium">
              Get Support →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ServiceDetails



///////////////////////// 'use client';

// import { useState, useEffect, useMemo } from 'react';
// import { useParams, Link, useNavigate } from 'react-router-dom';
// import { useServices } from '../../contexts/ServicesContext';
// import { ArrowLeft, Check } from 'lucide-react';
// import 'slick-carousel/slick/slick.css';
// import 'slick-carousel/slick/slick-theme.css';

// // Preload slick fonts
// const preloadFonts = () => {
//   const fonts = [
//     '/node_modules/slick-carousel/slick/fonts/slick.woff',
//     '/node_modules/slick-carousel/slick/fonts/slick.ttf',
//     '/node_modules/slick-carousel/slick/fonts/slick.svg',
//     '/node_modules/slick-carousel/slick/fonts/slick.eot',
//   ];

//   fonts.forEach((font) => {
//     const link = document.createElement('link');
//     link.rel = 'preload';
//     link.href = font;
//     link.as = 'font';
//     link.type = 'font/woff';
//     link.crossOrigin = 'anonymous';
//     document.head.appendChild(link);
//   });
// };

// const ServiceDetails = () => {
//   const { id } = useParams();
//   const { services } = useServices();
//   const [imageError, setImageError] = useState(false);
//   const navigate = useNavigate();

//   // Find service by ID
//   const service = useMemo(() => {
//     // First try to find by exact ID match
//     const foundService = services.find((s) => s.id === id);
//     if (foundService) return foundService;

//     // If not found, try to parse the ID as a number (for backward compatibility)
//     const numericId = parseInt(id);
//     if (!isNaN(numericId)) {
//       return services.find((s) => s.id === numericId);
//     }

//     return null;
//   }, [services, id]);

//   // Default placeholder as a data URI - a simple gray background with a camera icon
//   const DEFAULT_PLACEHOLDER =
//     'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjE1MCIgaGVpZ2h0PSIxNTAiIGZpbGw9IiNFNUU3RUIiLz48cGF0aCBkPSJNNjUgNTVINzVMODAgNjBIOTBDOTIuNzYxNCA2MCA5NSA2Mi4yMzg2IDk1IDY1VjkwQzk1IDkyLjc2MTQgOTIuNzYxNCA5NSA5MCA5NUg2MEM1Ny4yMzg2IDk1IDU1IDkyLjc2MTQgNTUgOTBWNjVDNTUgNjIuMjM4NiA1Ny4yMzg2IDYwIDYwIDYwSDYyLjVMNjUgNTVaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZVdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48Y2lyY2xlIGN4PSI3NSIgY3k9Ijc1IiByPSIxMCIgc3Ryb2tlPSIjOUNBM0FGIiBzdHJva2VXaWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+';

//   const getImageUrl = (image) => {
//     if (!image || imageError) {
//       return DEFAULT_PLACEHOLDER;
//     }

//     if (typeof image === 'string' && image.startsWith('data:image')) {
//       return image;
//     }

//     if (typeof image === 'string' && (image.startsWith('http') || image.startsWith('/'))) {
//       return image;
//     }

//     return DEFAULT_PLACEHOLDER;
//   };

//   useEffect(() => {
//     // Scroll to top when component mounts
//     window.scrollTo(0, 0);
//   }, [id]);

//   useEffect(() => {
//     preloadFonts();
//   }, []);

//   if (!service) {
//     return (
//       <div className="container mx-auto px-4 py-16">
//         <div className="text-center">
//           <h1 className="text-2xl font-bold text-gray-800 mb-4">Service Not Found</h1>
//           <p className="text-gray-600 mb-8">The service you're looking for doesn't exist or has been removed.</p>
//           <Link to="/services" className="inline-flex items-center text-[#FF6B00] hover:text-[#FF8533]">
//             <ArrowLeft className="h-5 w-5 mr-2" />
//             Back to Services
//           </Link>
//         </div>
//       </div>
//     );
//   }

//   // Get related services from the same category
//   const getRelatedServices = () => {
//     if (!service || !service.category) return [];

//     // Filter services from the same category, excluding the current service
//     const categoryServices = services.filter((s) => s.category === service.category && s.id !== service.id);

//     return categoryServices.slice(0, 2);
//   };

//   const relatedServices = getRelatedServices();

//   return (
//     <div className="container mx-auto px-4 py-16">
//       {/* Back to services link */}
//       <Link to="/services" className="inline-flex items-center text-[#FF6B00] hover:text-[#FF8533] mb-8">
//         <ArrowLeft className="h-5 w-5 mr-2" />
//         Back to Services
//       </Link>

//       <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
//         {/* Service Image */}
//         <div className="relative h-[400px] rounded-lg overflow-hidden bg-gray-100">
//           <img
//             src={getImageUrl(service.image) || '/placeholder.svg'}
//             alt={service.title}
//             className="w-full h-full object-cover transition-opacity duration-300"
//             onError={() => setImageError(true)}
//           />
//         </div>

//         {/* Service Details */}
//         <div>
//           <h1 className="text-3xl font-bold text-gray-900 mb-4">{service.title}</h1>
//           <div className="bg-[#FF6B00] text-white px-4 py-2 rounded-full inline-block mb-6">{service.price}</div>

//           <p className="text-gray-600 mb-8">{service.description}</p>

//           {service.features && service.features.length > 0 && (
//             <div>
//               <h2 className="text-xl font-semibold text-gray-800 mb-4">Key Features</h2>
//               <ul className="space-y-3">
//                 {service.features.map((feature, index) => (
//                   <li key={index} className="flex items-start">
//                     <Check className="h-5 w-5 text-[#FF6B00] mr-3 mt-0.5 flex-shrink-0" />
//                     <span className="text-gray-600">{feature}</span>
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           )}

//           {service.status === 'active' && (
//             <div className="mt-8">
//               <h2 className="text-xl font-semibold text-gray-800 mb-4">Availability</h2>
//               <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
//                 Available Now
//               </div>
//             </div>
//           )}

//           {service.featured && (
//             <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
//               <p className="text-yellow-800 text-sm">⭐ This is a featured service with special benefits</p>
//             </div>
//           )}
//         </div>
//       </div>

//       {/* Related Services */}
//       {relatedServices.length > 0 && (
//         <div className="mt-16">
//           <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Services</h2>
//           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
//             {relatedServices.map((relatedService) => (
//               <div
//                 key={relatedService.id}
//                 className="bg-white p-6 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-shadow"
//                 onClick={() => navigate(`/services/${relatedService.id}`)}
//               >
//                 <div className="h-48 mb-4 overflow-hidden rounded-lg">
//                   <img
//                     src={getImageUrl(relatedService.image) || '/placeholder.svg'}
//                     alt={relatedService.title}
//                     className="w-full h-full object-cover"
//                     onError={() => setImageError(true)}
//                   />
//                 </div>
//                 <h3 className="text-lg font-semibold text-gray-800 mb-2">{relatedService.title}</h3>
//                 <p className="text-gray-600 text-sm mb-4 line-clamp-2">{relatedService.description}</p>
//                 <div className="flex justify-between items-center">
//                   <span className="text-[#FF6B00] font-medium">{relatedService.price}</span>
//                   <button className="text-[#FF6B00] hover:text-[#FF8533] text-sm font-medium">View Details →</button>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}

//       {/* Additional Information */}
//       <div className="mt-16">
//         <h2 className="text-2xl font-bold text-gray-900 mb-8">Additional Information</h2>
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
//           {/* Contact Information */}
//           <div className="bg-white p-6 rounded-lg shadow-md">
//             <h3 className="text-lg font-semibold text-gray-800 mb-4">Contact Us</h3>
//             <p className="text-gray-600">For inquiries about this service, please contact our customer service team.</p>
//             <Link to="/contact" className="mt-4 inline-block text-[#FF6B00] hover:text-[#FF8533]">
//               Get in Touch →
//             </Link>
//           </div>

//           {/* Delivery Information */}
//           <div className="bg-white p-6 rounded-lg shadow-md">
//             <h3 className="text-lg font-semibold text-gray-800 mb-4">Delivery</h3>
//             <p className="text-gray-600">
//               We offer delivery services within Kandahar. Contact us for delivery options and rates.
//             </p>
//           </div>

//           {/* Terms & Conditions */}
//           <div className="bg-white p-6 rounded-lg shadow-md">
//             <h3 className="text-lg font-semibold text-gray-800 mb-4">Terms & Conditions</h3>
//             <p className="text-gray-600">Please review our terms and conditions before purchasing this service.</p>
//             <Link to="/terms" className="mt-4 inline-block text-[#FF6B00] hover:text-[#FF8533]">
//               Learn More →
//             </Link>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ServiceDetails;

// // import React, { useState } from 'react';
// // import { useParams, Link } from 'react-router-dom';
// // import { useServices } from '../contexts/ServicesContext';
// // import { ArrowLeft, Check } from 'lucide-react';

// // const ServiceDetails = () => {
// //   const { id } = useParams();
// //   const { services } = useServices();
// //   const [imageError, setImageError] = useState(false);
// //   const service = services.find(s => s.id === parseInt(id));

// //   // Default placeholder as a data URI - a simple gray background with a camera icon
// //   const DEFAULT_PLACEHOLDER = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjE1MCIgaGVpZ2h0PSIxNTAiIGZpbGw9IiNFNUU3RUIiLz48cGF0aCBkPSJNNjUgNTVINzVMODAgNjBIOTBDOTIuNzYxNCA2MCA5NSA2Mi4yMzg2IDk1IDY1VjkwQzk1IDkyLjc2MTQgOTIuNzYxNCA5NSA5MCA5NUg2MEM1Ny4yMzg2IDk1IDU1IDkyLjc2MTQgNTUgOTBWNjVDNTUgNjIuMjM4NiA1Ny4yMzg2IDYwIDYwIDYwSDYyLjVMNjUgNTVaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZVdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz48Y2lyY2xlIGN4PSI3NSIgY3k9Ijc1IiByPSIxMCIgc3Ryb2tlPSIjOUNBM0FGIiBzdHJva2VXaWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+';

// //   const getImageUrl = (image) => {
// //     if (!image || imageError) {
// //       return DEFAULT_PLACEHOLDER;
// //     }

// //     if (typeof image === 'string' && image.startsWith('data:image')) {
// //       return image;
// //     }

// //     if (typeof image === 'string' && (image.startsWith('http') || image.startsWith('/'))) {
// //       return image;
// //     }

// //     return DEFAULT_PLACEHOLDER;
// //   };

// //   if (!service) {
// //     return (
// //       <div className="container mx-auto px-4 py-16">
// //         <div className="text-center">
// //           <h1 className="text-2xl font-bold text-gray-800 mb-4">Service Not Found</h1>
// //           <p className="text-gray-600 mb-8">The service you're looking for doesn't exist or has been removed.</p>
// //           <Link
// //             to="/services"
// //             className="inline-flex items-center text-[#FF6B00] hover:text-[#FF8533]"
// //           >
// //             <ArrowLeft className="h-5 w-5 mr-2" />
// //             Back to Services
// //           </Link>
// //         </div>
// //       </div>
// //     );
// //   }

// //   return (
// //     <div className="container mx-auto px-4 py-16">
// //       {/* Back to services link */}
// //       <Link
// //         to="/services"
// //         className="inline-flex items-center text-[#FF6B00] hover:text-[#FF8533] mb-8"
// //       >
// //         <ArrowLeft className="h-5 w-5 mr-2" />
// //         Back to Services
// //       </Link>

// //       <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
// //         {/* Service Image */}
// //         <div className="relative h-[400px] rounded-lg overflow-hidden bg-gray-100">
// //           <img
// //             src={getImageUrl(service.image)}
// //             alt={service.title}
// //             className="w-full h-full object-cover transition-opacity duration-300"
// //             onError={() => setImageError(true)}
// //           />
// //         </div>

// //         {/* Service Details */}
// //         <div>
// //           <h1 className="text-3xl font-bold text-gray-900 mb-4">{service.title}</h1>
// //           <div className="bg-[#FF6B00] text-white px-4 py-2 rounded-full inline-block mb-6">
// //             {service.price}
// //           </div>

// //           <p className="text-gray-600 mb-8">{service.description}</p>

// //           {service.features && service.features.length > 0 && (
// //             <div>
// //               <h2 className="text-xl font-semibold text-gray-800 mb-4">Key Features</h2>
// //               <ul className="space-y-3">
// //                 {service.features.map((feature, index) => (
// //                   <li key={index} className="flex items-start">
// //                     <Check className="h-5 w-5 text-[#FF6B00] mr-3 mt-0.5 flex-shrink-0" />
// //                     <span className="text-gray-600">{feature}</span>
// //                   </li>
// //                 ))}
// //               </ul>
// //             </div>
// //           )}

// //           {service.status === 'active' && (
// //             <div className="mt-8">
// //               <h2 className="text-xl font-semibold text-gray-800 mb-4">Availability</h2>
// //               <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
// //                 Available Now
// //               </div>
// //             </div>
// //           )}

// //           {service.featured && (
// //             <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
// //               <p className="text-yellow-800 text-sm">
// //                 ⭐ This is a featured service with special benefits
// //               </p>
// //             </div>
// //           )}
// //         </div>
// //       </div>

// //       {/* Additional Information */}
// //       <div className="mt-16">
// //         <h2 className="text-2xl font-bold text-gray-900 mb-8">Additional Information</h2>
// //         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
// //           {/* Contact Information */}
// //           <div className="bg-white p-6 rounded-lg shadow-md">
// //             <h3 className="text-lg font-semibold text-gray-800 mb-4">Contact Us</h3>
// //             <p className="text-gray-600">
// //               For inquiries about this service, please contact our customer service team.
// //             </p>
// //             <Link
// //               to="/contact"
// //               className="mt-4 inline-block text-[#FF6B00] hover:text-[#FF8533]"
// //             >
// //               Get in Touch →
// //             </Link>
// //           </div>

// //           {/* Delivery Information */}
// //           <div className="bg-white p-6 rounded-lg shadow-md">
// //             <h3 className="text-lg font-semibold text-gray-800 mb-4">Delivery</h3>
// //             <p className="text-gray-600">
// //               We offer delivery services within Kandahar. Contact us for delivery options and rates.
// //             </p>
// //           </div>

// //           {/* Terms & Conditions */}
// //           <div className="bg-white p-6 rounded-lg shadow-md">
// //             <h3 className="text-lg font-semibold text-gray-800 mb-4">Terms & Conditions</h3>
// //             <p className="text-gray-600">
// //               Please review our terms and conditions before purchasing this service.
// //             </p>
// //             <Link
// //               to="/terms"
// //               className="mt-4 inline-block text-[#FF6B00] hover:text-[#FF8533]"
// //             >
// //               Learn More →
// //             </Link>
// //           </div>
// //         </div>
// //       </div>
// //     </div>
// //   );
// // };

// // export default ServiceDetails;
