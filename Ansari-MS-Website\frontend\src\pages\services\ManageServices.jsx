

"use client"

import React, { useState, useEffect, useRef, useMemo, useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { Link } from "react-router-dom"
import { useServices } from "../../contexts/ServicesContext"
import { Edit, Trash2, Search, Plus, Eye, MoreVertical, Tag, DollarSign } from "lucide-react"

const SERVICES_CATEGORIES = ["Premium Hens", "Baby Chickens", "Wholesale"]

const ManageServices = () => {
  const navigate = useNavigate()
  const { services, deleteService, loading } = useServices()
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [serviceToDelete, setServiceToDelete] = useState(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [activeMenu, setActiveMenu] = useState(null)
  const [feedback, setFeedback] = useState({ type: "", message: "" })
  const [filterCategory, setFilterCategory] = useState("all")

  // Get unique categories for filter
  const categories = useMemo(() => {
    return ["all", ...SERVICES_CATEGORIES]
  }, [])

  // Handle search
  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }, [])

  // Handle category filter
  const handleCategoryFilter = useCallback((category) => {
    setFilterCategory(category)
    setCurrentPage(1)
  }, [])

  // Handle view
  const handleView = useCallback(
    (serviceId) => {
      try {
        navigate(`/services/${serviceId}`)
        setActiveMenu(null)
      } catch (error) {
        console.error("Error navigating to view:", error)
        setFeedback({
          type: "error",
          message: "Error viewing service. Please try again.",
        })
      }
    },
    [navigate],
  )

  // Handle edit
  const handleEdit = useCallback(
    (serviceId) => {
      try {
        navigate(`/admin/services/edit/${serviceId}`)
        setActiveMenu(null)
      } catch (error) {
        console.error("Error navigating to edit:", error)
        setFeedback({
          type: "error",
          message: "Error editing service. Please try again.",
        })
      }
    },
    [navigate],
  )

  // Handle delete
  const handleDelete = useCallback(
    async (id) => {
      try {
        setIsDeleting(true)
        await deleteService(id)
        setFeedback({
          type: "success",
          message: "Service deleted successfully",
        })
        setIsDeleteModalOpen(false)
        setServiceToDelete(null)
        setTimeout(() => setFeedback({ type: "", message: "" }), 3000)
      } catch (error) {
        setFeedback({ type: "error", message: "Failed to delete service" })
        setTimeout(() => setFeedback({ type: "", message: "" }), 3000)
      } finally {
        setIsDeleting(false)
      }
    },
    [deleteService],
  )

  // Toggle menu
  const toggleMenu = useCallback(
    (serviceId) => {
      setActiveMenu(activeMenu === serviceId ? null : serviceId)
    },
    [activeMenu],
  )

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close menu if clicking outside any dropdown
      if (activeMenu && !event.target.closest('.relative')) {
        setActiveMenu(null)
      }
    }

    if (activeMenu) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [activeMenu])

  // Memoize filtered services
  const filteredServices = useMemo(() => {
    return services.filter((item) => {
      const matchesSearch =
        item.Title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.Description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.Category?.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesCategory = filterCategory === "all" || item.Category?.toLowerCase() === filterCategory.toLowerCase()

      return matchesSearch && matchesCategory
    })
  }, [services, searchTerm, filterCategory])

  // Memoize paginated services
  const paginatedServices = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredServices.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredServices, currentPage, itemsPerPage])

  // Memoize total pages
  const totalPages = useMemo(
    () => Math.ceil(filteredServices.length / itemsPerPage),
    [filteredServices.length, itemsPerPage],
  )

  // Memoize handlePageChange
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page)
  }, [])

  if (loading && services.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-muted dark:bg-gray-900 p-2 sm:p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
          <h1 className="text-xl sm:text-2xl font-headingEn font-bold text-secondary dark:text-white">Manage Services</h1>
          <Link
            to="/admin/services/add"
            className="w-full sm:w-auto bg-primary text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-primary-hover transition-colors shadow-card font-bodyEn"
          >
            <Plus size={18} className="mr-2" />
            Add Service
          </Link>
        </div>

        {/* Feedback Message */}
        {feedback.message && (
          <div
            className={`mb-4 p-4 rounded-lg ${
              feedback.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
            }`}
          >
            {feedback.message}
          </div>
        )}

        {/* Search and Filter */}
        <div className="bg-card dark:bg-gray-800 p-4 rounded-lg shadow-card mb-4 sm:mb-6 border border-border-color">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search services..."
                className="w-full pl-10 pr-4 py-2 text-sm border border-border-color dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary dark:bg-gray-700 dark:text-white font-bodyEn"
                value={searchTerm}
                onChange={handleSearch}
              />
              <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-textprimary/50" />
            </div>

            <div className="md:col-span-2">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm text-textprimary/70 dark:text-gray-400 self-center font-bodyEn">Filter by category:</span>
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => handleCategoryFilter(category)}
                    className={`px-3 py-1 text-xs rounded-lg font-bodyEn transition-colors ${
                      filterCategory === category
                        ? "bg-primary text-white shadow-sm"
                        : "bg-muted text-textprimary border border-border-color hover:bg-primary hover:text-white dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    }`}
                  >
                    {category === "all" ? "All Categories" : category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Services Table */}
        <div className="bg-card dark:bg-gray-800 rounded-lg shadow-card border border-border-color">
          <div className="w-full">
            <table className="w-full table-fixed">
              <thead className="bg-muted dark:bg-gray-700">
                <tr>
                  <th className="w-1/3 px-4 sm:px-6 py-3 text-left text-xs font-headingEn font-medium text-textprimary/70 dark:text-gray-300 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-headingEn font-medium text-textprimary/70 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Category
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-headingEn font-medium text-textprimary/70 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Price
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-headingEn font-medium text-textprimary/70 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
                    Status
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-right text-xs font-headingEn font-medium text-textprimary/70 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card dark:bg-gray-800 divide-y divide-border-color dark:divide-gray-700">
                {paginatedServices.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="px-4 sm:px-6 py-4 text-center text-textprimary/70 dark:text-gray-400 font-bodyEn">
                      No services found
                    </td>
                  </tr>
                ) : (
                  paginatedServices.map((item) => (
                    <tr key={item.SR_Id} className="hover:bg-muted dark:hover:bg-gray-700 transition-colors">
                      <td className="px-4 sm:px-6 py-4">
                        <div className="flex items-center min-w-0">
                          <div className="h-10 w-10 flex-shrink-0">
                            <img
                              className="h-10 w-10 rounded-lg object-cover border border-border-color"
                              src={item.Image ?`http://localhost:5432/public/images/services/${item.Image}` : "/placeholder.svg"}
                              alt={item.Title}
                              onError={(e) => {
                                e.target.onerror = null
                                e.target.src = "/placeholder.svg"
                              }}
                            />
                          </div>
                          <div className="ml-4 min-w-0">
                            <div className="text-sm font-headingEn font-medium text-secondary dark:text-white truncate">
                              {item.Title}
                            </div>
                            <div className="text-xs text-textprimary/70 dark:text-gray-400 font-bodyEn truncate">
                              {item.Description?.substring(0, 50)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                        <div className="flex items-center">
                          <Tag size={14} className="text-primary mr-2" />
                          <span className="text-sm text-textprimary dark:text-gray-400 font-bodyEn">{item.Category}</span>
                        </div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                        <div className="flex items-center">
                          <DollarSign size={14} className="text-primary mr-1" />
                          <span className="text-sm text-textprimary dark:text-gray-400 font-bodyEn font-semibold">{item.Price}</span>
                        </div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 hidden md:table-cell">
                        <span
                          className={`px-3 py-1 inline-flex text-xs leading-5 font-bodyEn font-semibold rounded-lg ${
                            item.Status === "active"
                              ? "bg-success/10 text-success border border-success/20"
                              : "bg-muted text-textprimary/70 border border-border-color"
                          }`}
                        >
                          {item.Status === "active" ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
                        <div className="relative">
                          <button
                            onClick={() => toggleMenu(item.SR_Id)}
                            className="text-textprimary/70 hover:text-primary dark:text-gray-400 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-muted transition-colors"
                          >
                            <MoreVertical size={18} />
                          </button>
                          {activeMenu === item.SR_Id && (
                            <div className="absolute right-0 mt-2 w-48 bg-card dark:bg-gray-800 rounded-lg shadow-card-hover border border-border-color py-1 z-50">
                              <button
                                onClick={() => handleView(item.SR_Id)}
                                className="flex items-center w-full px-4 py-2 text-sm text-textprimary dark:text-gray-300 hover:bg-muted dark:hover:bg-gray-700 font-bodyEn transition-colors"
                              >
                                <Eye size={16} className="mr-2 text-primary" />
                                View
                              </button>
                              <button
                                onClick={() => handleEdit(item.SR_Id)}
                                className="flex items-center w-full px-4 py-2 text-sm text-textprimary dark:text-gray-300 hover:bg-muted dark:hover:bg-gray-700 font-bodyEn transition-colors"
                              >
                                <Edit size={16} className="mr-2 text-primary" />
                                Edit
                              </button>
                              <button
                                onClick={() => {
                                  setServiceToDelete(item)
                                  setIsDeleteModalOpen(true)
                                  setActiveMenu(null)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-destructive hover:bg-destructive/10 dark:hover:bg-gray-700 font-bodyEn transition-colors"
                              >
                                <Trash2 size={16} className="mr-2" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                <p>
                  Showing <span className="font-medium">{currentPage * itemsPerPage - itemsPerPage + 1}</span> to{" "}
                  <span className="font-medium">
                    {currentPage * itemsPerPage > filteredServices.length
                      ? filteredServices.length
                      : currentPage * itemsPerPage}
                  </span>{" "}
                  of <span className="font-medium">{filteredServices.length}</span> results
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-4 py-2 rounded-lg text-sm font-bodyEn transition-colors ${
                    currentPage === 1
                      ? "bg-muted text-textprimary/50 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed border border-border-color"
                      : "bg-primary text-white hover:bg-primary-hover shadow-sm"
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-4 py-2 rounded-lg text-sm font-bodyEn transition-colors ${
                    currentPage === totalPages
                      ? "bg-muted text-textprimary/50 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed border border-border-color"
                      : "bg-primary text-white hover:bg-primary-hover shadow-sm"
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {isDeleteModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Are you sure you want to delete "{serviceToDelete?.Title}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setIsDeleteModalOpen(false)
                    setServiceToDelete(null)
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(serviceToDelete.SR_Id)}
                  disabled={isDeleting}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center"
                >
                  {isDeleting ? (
                    <>
                      <span className="animate-spin mr-2">⟳</span>
                      Deleting...
                    </>
                  ) : (
                    "Delete"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default React.memo(ManageServices)


///////////////////////////////

// 'use client';

// import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { Link } from 'react-router-dom';
// import { useServices } from '../../contexts/ServicesContext';
// import { Edit, Trash2, Search, Plus, Eye, MoreVertical, Tag, DollarSign } from 'lucide-react';

// const SERVICES_CATEGORIES = ['Premium Hens', 'Baby Chickens', 'Wholesale'];

// const ManageServices = () => {
//   const navigate = useNavigate();
//   const { services, deleteService } = useServices();
//   const [searchTerm, setSearchTerm] = useState('');
//   const [currentPage, setCurrentPage] = useState(1);
//   const [itemsPerPage] = useState(10);
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
//   const [serviceToDelete, setServiceToDelete] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [activeMenu, setActiveMenu] = useState(null);
//   const menuRef = useRef(null);
//   const [feedback, setFeedback] = useState({ type: '', message: '' });
//   const [filterCategory, setFilterCategory] = useState('all');

//   // Get unique categories for filter
//   const categories = useMemo(() => {
//     return ['all', ...SERVICES_CATEGORIES];
//   }, []);

//   // Handle search
//   const handleSearch = useCallback((e) => {
//     setSearchTerm(e.target.value);
//     setCurrentPage(1);
//   }, []);

//   // Handle category filter
//   const handleCategoryFilter = useCallback((category) => {
//     setFilterCategory(category);
//     setCurrentPage(1);
//   }, []);

//   // Handle view
//   const handleView = useCallback(
//     (serviceId) => {
//       try {
//         navigate(`/services/${serviceId}`);
//         setActiveMenu(null);
//       } catch (error) {
//         console.error('Error navigating to view:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error viewing service. Please try again.',
//         });
//       }
//     },
//     [navigate]
//   );

//   // Handle edit
//   const handleEdit = useCallback(
//     (serviceId) => {
//       try {
//         navigate(`/admin/services/edit/${serviceId}`);
//         setActiveMenu(null);
//       } catch (error) {
//         console.error('Error navigating to edit:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error editing service. Please try again.',
//         });
//       }
//     },
//     [navigate]
//   );

//   // Handle delete
//   const handleDelete = useCallback(
//     async (id) => {
//       try {
//         setLoading(true);
//         await deleteService(id);
//         setFeedback({
//           type: 'success',
//           message: 'Service deleted successfully',
//         });
//         setIsDeleteModalOpen(false);
//         setServiceToDelete(null);
//         setTimeout(() => setFeedback({ type: '', message: '' }), 3000);
//       } catch (error) {
//         setFeedback({ type: 'error', message: 'Failed to delete service' });
//         setTimeout(() => setFeedback({ type: '', message: '' }), 3000);
//       } finally {
//         setLoading(false);
//       }
//     },
//     [deleteService]
//   );

//   // Toggle menu
//   const toggleMenu = useCallback(
//     (serviceId) => {
//       setActiveMenu(activeMenu === serviceId ? null : serviceId);
//     },
//     [activeMenu]
//   );

//   // Add click outside handler
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (menuRef.current && !menuRef.current.contains(event.target)) {
//         setActiveMenu(null);
//       }
//     };

//     document.addEventListener('mousedown', handleClickOutside);
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, []);

//   // Memoize filtered services
//   const filteredServices = useMemo(() => {
//     return services.filter((item) => {
//       const matchesSearch =
//         item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         item.category?.toLowerCase().includes(searchTerm.toLowerCase());

//       const matchesCategory = filterCategory === 'all' || item.category?.toLowerCase() === filterCategory.toLowerCase();

//       return matchesSearch && matchesCategory;
//     });
//   }, [services, searchTerm, filterCategory]);

//   // Memoize paginated services
//   const paginatedServices = useMemo(() => {
//     const startIndex = (currentPage - 1) * itemsPerPage;
//     return filteredServices.slice(startIndex, startIndex + itemsPerPage);
//   }, [filteredServices, currentPage, itemsPerPage]);

//   // Memoize total pages
//   const totalPages = useMemo(
//     () => Math.ceil(filteredServices.length / itemsPerPage),
//     [filteredServices.length, itemsPerPage]
//   );

//   // Memoize handlePageChange
//   const handlePageChange = useCallback((page) => {
//     setCurrentPage(page);
//   }, []);

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
//           <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage Services</h1>
//           <Link
//             to="/admin/services/add"
//             className="w-full sm:w-auto bg-[#FF6B00] text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
//           >
//             <Plus size={18} className="mr-2" />
//             Add Service
//           </Link>
//         </div>

//         {/* Feedback Message */}
//         {feedback.message && (
//           <div
//             className={`mb-4 p-4 rounded-lg ${
//               feedback.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
//             }`}
//           >
//             {feedback.message}
//           </div>
//         )}

//         {/* Search and Filter */}
//         <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//             <div className="relative">
//               <input
//                 type="text"
//                 placeholder="Search services..."
//                 className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 value={searchTerm}
//                 onChange={handleSearch}
//               />
//               <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
//             </div>

//             <div className="md:col-span-2">
//               <div className="flex flex-wrap gap-2">
//                 <span className="text-sm text-gray-500 dark:text-gray-400 self-center">Filter by category:</span>
//                 {categories.map((category) => (
//                   <button
//                     key={category}
//                     onClick={() => handleCategoryFilter(category)}
//                     className={`px-3 py-1 text-xs rounded-full ${
//                       filterCategory === category
//                         ? 'bg-[#FF6B00] text-white'
//                         : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
//                     }`}
//                   >
//                     {category === 'all' ? 'All Categories' : category}
//                   </button>
//                 ))}
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Services Table */}
//         <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
//           <div className="w-full">
//             <table className="w-full table-fixed">
//               <thead className="bg-gray-50 dark:bg-gray-700">
//                 <tr>
//                   <th className="w-1/3 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     Service
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Category
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Price
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
//                     Status
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
//                 {paginatedServices.length === 0 ? (
//                   <tr>
//                     <td colSpan={5} className="px-4 sm:px-6 py-4 text-center text-gray-500 dark:text-gray-400">
//                       No services found
//                     </td>
//                   </tr>
//                 ) : (
//                   paginatedServices.map((item) => (
//                     <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
//                       <td className="px-4 sm:px-6 py-4">
//                         <div className="flex items-center min-w-0">
//                           <div className="h-10 w-10 flex-shrink-0">
//                             <img
//                               className="h-10 w-10 rounded-full object-cover"
//                               src={item.image || '/placeholder.svg'}
//                               alt={item.title}
//                               onError={(e) => {
//                                 e.target.onerror = null;
//                                 e.target.src = '/placeholder.svg';
//                               }}
//                             />
//                           </div>
//                           <div className="ml-4 min-w-0">
//                             <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
//                               {item.title}
//                             </div>
//                             <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
//                               {item.description?.substring(0, 50)}...
//                             </div>
//                           </div>
//                         </div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                         <div className="flex items-center">
//                           <Tag size={14} className="text-gray-400 mr-2" />
//                           <span className="text-sm text-gray-500 dark:text-gray-400">{item.category}</span>
//                         </div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                         <div className="flex items-center">
//                           <DollarSign size={14} className="text-gray-400 mr-1" />
//                           <span className="text-sm text-gray-500 dark:text-gray-400">{item.price}</span>
//                         </div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 hidden md:table-cell">
//                         <span
//                           className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                             item.status === 'active'
//                               ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
//                               : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
//                           }`}
//                         >
//                           {item.status === 'active' ? 'Active' : 'Inactive'}
//                         </span>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
//                         <div className="relative" ref={menuRef}>
//                           <button
//                             onClick={() => toggleMenu(item.id)}
//                             className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
//                           >
//                             <MoreVertical size={18} />
//                           </button>
//                           {activeMenu === item.id && (
//                             <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10">
//                               <button
//                                 onClick={() => handleView(item.id)}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Eye size={16} className="mr-2" />
//                                 View
//                               </button>
//                               <button
//                                 onClick={() => handleEdit(item.id)}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Edit size={16} className="mr-2" />
//                                 Edit
//                               </button>
//                               <button
//                                 onClick={() => {
//                                   setServiceToDelete(item);
//                                   setIsDeleteModalOpen(true);
//                                 }}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Trash2 size={16} className="mr-2" />
//                                 Delete
//                               </button>
//                             </div>
//                           )}
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 )}
//               </tbody>
//             </table>
//           </div>

//           {/* Pagination */}
//           {totalPages > 1 && (
//             <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
//               <div className="text-sm text-gray-700 dark:text-gray-300">
//                 <p>
//                   Showing <span className="font-medium">{currentPage * itemsPerPage - itemsPerPage + 1}</span> to{' '}
//                   <span className="font-medium">
//                     {currentPage * itemsPerPage > filteredServices.length
//                       ? filteredServices.length
//                       : currentPage * itemsPerPage}
//                   </span>{' '}
//                   of <span className="font-medium">{filteredServices.length}</span> results
//                 </p>
//               </div>
//               <div className="flex space-x-2">
//                 <button
//                   onClick={() => handlePageChange(currentPage - 1)}
//                   disabled={currentPage === 1}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === 1
//                       ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
//                       : 'bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90'
//                   }`}
//                 >
//                   Previous
//                 </button>
//                 <button
//                   onClick={() => handlePageChange(currentPage + 1)}
//                   disabled={currentPage === totalPages}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === totalPages
//                       ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
//                       : 'bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90'
//                   }`}
//                 >
//                   Next
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>

//         {/* Delete Confirmation Modal */}
//         {isDeleteModalOpen && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full">
//               <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
//               <p className="mb-6 text-gray-600 dark:text-gray-300">
//                 Are you sure you want to delete "{serviceToDelete?.title}"? This action cannot be undone.
//               </p>
//               <div className="flex justify-end space-x-3">
//                 <button
//                   onClick={() => {
//                     setIsDeleteModalOpen(false);
//                     setServiceToDelete(null);
//                   }}
//                   className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={() => handleDelete(serviceToDelete.id)}
//                   className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
//                 >
//                   Delete
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default React.memo(ManageServices);
