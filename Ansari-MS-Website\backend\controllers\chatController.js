import asyncHandler from "../middlewares/asyncHandler.js";
import Email from "../utils/email.js";
import ChatModel from "../models/ChatModel.js";
import UserModel from "../models/userModel.js";

const ChatController = {
  startChat: asyncHandler(async (req, res) => {
    const user1_id = req.user.u_Id; // Authenticated user
    const { user2_id } = req.body; // The other participant

    // Get both users
    const user1 = await ChatModel.getUserById(user1_id);
    const user2 = await ChatModel.getUserById(user2_id);

    if (!user1 || !user2) {
      return res
        .status(404)
        .json({ success: false, message: "User(s) not found." });
    }

    const roles = [user1.Role, user2.Role];

    // Only allow chats where one is admin and the other is shopper or farmer
    if (
      !roles.includes("admin") ||
      (roles.includes("shopper") && roles.includes("farmer")) || // prevent shopper <-> farmer
      (roles[0] === roles[1] && roles[0] !== "admin") // prevent same non-admin roles
    ) {
      return res.status(403).json({
        success: false,
        message:
          "Only admin can chat with shoppers or farmers. Others can't chat with each other.",
      });
    }

    // Prevent duplicate rooms
    const existingRoom = await ChatModel.getRoom(user1_id, user2_id);
    if (existingRoom) {
      return res.status(200).json({
        success: true,
        message: "Chat room already exists. Please use that.",
        roomId: existingRoom.room_id,
      });
    }

    // Create chat room
    const roomId = await ChatModel.createRoom(user1_id, user2_id);
    res.status(201).json({
      success: true,
      message: "Chat room created successfully.",
      roomId,
    });
  }),

  sendMessage: asyncHandler(async (req, res) => {
    const { room_id, message_type, message_content, image_url } = req.body;

    const sender_id = req.user.u_Id;

    const messageId = await ChatModel.createMessage(
      room_id,
      sender_id,
      message_type,
      message_content,
      image_url || "defaudefault-chat-image.jpg",
    );

    // Get the complete message with user info for real-time update
    const newMessage = await ChatModel.getMessageById(messageId);

    // Emit the new message to all users in the room
    if (global.io) {
      global.io.to(room_id.toString()).emit("new_message", newMessage);
    }

    res.status(201).json({
      success: true,
      message: "Message sent successfully.",
      messageId,
    });
  }),

  deleteMessage: asyncHandler(async (req, res) => {
    const { messageId } = req.params;
    const userId = req.user.u_Id;

    const deletedMessage = await ChatModel.deleteMessage(messageId, userId);

    if (!deletedMessage) {
      return res.status(404).json({
        success: false,
        message: "Message not found or you don't have permission to delete it.",
      });
    }

    // Emit the message deletion to all users in the room
    if (global.io) {
      global.io.to(deletedMessage.room_id.toString()).emit("message_deleted", {
        messageId: parseInt(messageId),
        roomId: deletedMessage.room_id,
      });
    }

    res.status(200).json({
      success: true,
      message: "Message deleted successfully.",
      data: { messageId, roomId: deletedMessage.room_id },
    });
  }),

  editMessage: asyncHandler(async (req, res) => {
    const { messageId } = req.params;
    const { message_content } = req.body;
    const userId = req.user.u_Id;

    if (!message_content || !message_content.trim()) {
      return res.status(400).json({
        success: false,
        message: "Message content cannot be empty.",
      });
    }

    const editedMessage = await ChatModel.editMessage(messageId, userId, message_content.trim());

    if (!editedMessage) {
      return res.status(404).json({
        success: false,
        message: "Message not found or you don't have permission to edit it.",
      });
    }

    // Emit the message edit to all users in the room
    if (global.io) {
      global.io.to(editedMessage.room_id.toString()).emit("message_edited", editedMessage);
    }

    res.status(200).json({
      success: true,
      message: "Message edited successfully.",
      data: editedMessage,
    });
  }),

  // In your ChatController.js
  getMessages: asyncHandler(async (req, res) => {
    const { room_id } = req.params;

    // Retrieve chat messages for the given room
    const messages = await ChatModel.getMessagesByRoomId(room_id);

    // Retrieve admin user info
    const adminUser = await ChatModel.getAdminUser();

    res.status(200).json({
      success: true,
      length: messages.length,
      data: messages,
      admin: adminUser, // Include admin info in the response
    });
  }),

  getAdminChatsWithFarmers: asyncHandler(async (req, res) => {
    const adminId = req.user.u_Id;

    const rooms = await ChatModel.getAdminChatRoomsByRole(adminId, "farmer");

    res.status(200).json({
      success: true,
      count: rooms.length,
      rooms,
    });
  }),
  getAdminChatsWithShoppers: asyncHandler(async (req, res) => {
    const adminId = req.user.u_Id;

    const rooms = await ChatModel.getAdminChatRoomsByRole(adminId, "shopper");

    res.status(200).json({
      success: true,
      count: rooms.length,
      rooms,
    });
  }),

  sendMessageToAllFarmers: asyncHandler(async (req, res) => {
    const adminId = req.user.u_Id;
    const { message_content } = req.body;

    //// to find adin name
    const { U_FirstName, U_LastName } = await UserModel.getUserById(adminId);

    const farmers = await ChatModel.getUsersByRole("farmer");

    let count = 0;
    for (const farmer of farmers) {
      let room = await ChatModel.getRoom(adminId, farmer.u_Id);
      let roomId;
      if (!room) {
        roomId = await ChatModel.createRoom(adminId, farmer.u_Id);
      } else {
        roomId = room.room_id;
      }

      await ChatModel.createMessage(roomId, adminId, "text", message_content);
      count++;

      // Send email to the farmer
      try {
        console.log(
          `Sending email to: ${farmer.U_Email} : (${farmer.U_FirstName})`,
        );

        const chatUrl = `http://localhost:3000/farmer/chat?roomId=${roomId}`;
        const email = new Email(farmer, chatUrl);

        await email.send(
          "chatBroadcast", // Optional: use your actual template or pass null
          `📩 New Message from ${U_FirstName} ${U_LastName} (Admin)`,
          `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
    <div style="background-color: #4CAF50; padding: 16px; color: white; text-align: center;">
      <h2 style="margin: 0;">💬 New Chat Message</h2>
    </div>
    <div style="padding: 20px; background-color: #ffffff;">
      <p style="font-size: 16px;">Hello <strong>${farmer.U_FirstName}</strong>,</p>
      <p style="font-size: 15px; color: #555;">
        You’ve received a new message from the admin <strong>${U_FirstName} ${U_LastName}</strong>:
      </p>
      <div style="margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-left: 4px solid #4CAF50;">
        <p style="font-size: 16px; margin: 0; color: #333;">${message_content}</p>
      </div>
      <p style="font-size: 15px;">Click the button below to view and reply to the message:</p>
      <div style="text-align: center; margin-top: 20px;">
        <a href="${chatUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-size: 16px;">
          👉 Open Chat
        </a>
      </div>
    </div>
    <div style="background-color: #f1f1f1; padding: 12px; text-align: center; font-size: 12px; color: #888;">
      This is an automated message. Please do not reply to this email.
    </div>
  </div>
  `,
        );
      } catch (err) {
        console.error(
          `Failed to send email to ${farmer.U_Email}:`,
          err.message,
        );
      }
    }

    res.status(200).json({
      success: true,
      message: `Message sent to ${count} farmers and their email inboxes.`,
    });
  }),

  sendMessageToAllShoppers: asyncHandler(async (req, res) => {
    const adminId = req.user.u_Id;
    const { message_content } = req.body;
    const { U_FirstName, U_LastName } = await UserModel.getUserById(adminId);

    const shoppers = await ChatModel.getUsersByRole("shopper");

    let count = 0;
    for (const shopper of shoppers) {
      let room = await ChatModel.getRoom(adminId, shopper.u_Id);
      let roomId;
      if (!room) {
        roomId = await ChatModel.createRoom(adminId, shopper.u_Id);
      } else {
        roomId = room.room_id;
      }
      await ChatModel.createMessage(roomId, adminId, "text", message_content);
      count++;

      // Send email to the farmer
      try {
        console.log(
          `Sending email to: ${shopper.U_Email} : (${shopper.U_FirstName})`,
        );

        const chatUrl = `http://localhost:3000/shopper/chat?roomId=${roomId}`;
        const email = new Email(shopper, chatUrl);

        await email.send(
          "chatBroadcast", // Optional: use your actual template or pass null
          `📩 New Message from ${U_FirstName} ${U_LastName} (Admin)`,
          `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.05);">
    <div style="background-color: #4CAF50; padding: 16px; color: white; text-align: center;">
      <h2 style="margin: 0;">💬 New Chat Message</h2>
    </div>
    <div style="padding: 20px; background-color: #ffffff;">
      <p style="font-size: 16px;">Hello <strong>${shopper.U_FirstName}</strong>,</p>
      <p style="font-size: 15px; color: #555;">
        You’ve received a new message from the admin <strong>${U_FirstName} ${U_LastName}</strong>:
      </p>
      <div style="margin: 20px 0; padding: 15px; background-color: #f9f9f9; border-left: 4px solid #4CAF50;">
        <p style="font-size: 16px; margin: 0; color: #333;">${message_content}</p>
      </div>
      <p style="font-size: 15px;">Click the button below to view and reply to the message:</p>
      <div style="text-align: center; margin-top: 20px;">
        <a href="${chatUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-size: 16px;">
          👉 Open Chat
        </a>
      </div>
    </div>
    <div style="background-color: #f1f1f1; padding: 12px; text-align: center; font-size: 12px; color: #888;">
      This is an automated message. Please do not reply to this email.
    </div>
  </div>
  `,
        );
      } catch (err) {
        console.error(
          `Failed to send email to ${shopper.U_Email}:`,
          err.message,
        );
      }
    }

    res.status(200).json({
      success: true,
      message: `Message sent to ${count} shoppers and their email inboxes.`,
    });
  }),
};

export default ChatController;
