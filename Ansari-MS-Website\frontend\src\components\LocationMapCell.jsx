import { useState, useEffect, useRef } from 'react';
import { MapP<PERSON>, Eye } from 'lucide-react';

const LocationMapCell = ({ location }) => {
  const mapRef = useRef(null);
  const [showMap, setShowMap] = useState(false);
  const [coordinates, setCoordinates] = useState({ lat: 34.5553, lng: 69.2075 });
  const [map, setMap] = useState(null);
  const [marker, setMarker] = useState(null);

  // Parse location coordinates
  useEffect(() => {
    if (location) {
      const coords = location.split(' ');
      if (coords.length === 2) {
        const lat = parseFloat(coords[0]);
        const lng = parseFloat(coords[1]);
        if (!isNaN(lat) && !isNaN(lng)) {
          setCoordinates({ lat, lng });
        }
      }
    }
  }, [location]);

  // Initialize map when shown
  useEffect(() => {
    if (!showMap || !mapRef.current || map) return;

    const initializeMap = async () => {
      try {
        const leaflet = await import('leaflet');

        // Create custom icon to avoid default icon issues
        const customIcon = leaflet.icon({
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41]
        });

        // Create small map
        const mapInstance = leaflet.map(mapRef.current, {
          zoomControl: false,
          attributionControl: false,
          dragging: false,
          touchZoom: false,
          doubleClickZoom: false,
          scrollWheelZoom: false,
          boxZoom: false,
          keyboard: false,
        }).setView([coordinates.lat, coordinates.lng], 13);

        leaflet
          .tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png')
          .addTo(mapInstance);

        // Add marker with custom icon
        const farmMarker = leaflet.marker([coordinates.lat, coordinates.lng], { icon: customIcon })
          .addTo(mapInstance)
          .bindPopup(`Farm Location: ${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)}`);

        setMap(mapInstance);
        setMarker(farmMarker);

      } catch (error) {
        console.error('Error initializing small map:', error);
      }
    };

    const timer = setTimeout(initializeMap, 100);

    return () => {
      clearTimeout(timer);
      if (map) {
        map.remove();
        setMap(null);
        setMarker(null);
      }
    };
  }, [showMap, coordinates.lat, coordinates.lng]);

  // Cleanup map when hiding
  useEffect(() => {
    if (!showMap && map) {
      map.remove();
      setMap(null);
      setMarker(null);
    }
  }, [showMap, map]);

  const formatCoordinates = (location) => {
    if (!location) return 'No location';
    const coords = location.split(' ');
    if (coords.length === 2) {
      const lat = parseFloat(coords[0]);
      const lng = parseFloat(coords[1]);
      if (!isNaN(lat) && !isNaN(lng)) {
        return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
      }
    }
    return location;
  };

  return (
    <div className="relative">
      {/* Coordinates display with view button */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <MapPin className="h-3 w-3" />
          <span className="font-mono text-xs">
            {formatCoordinates(location)}
          </span>
        </div>
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setShowMap(!showMap);
          }}
          className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
          title={showMap ? 'Hide map' : 'Show map'}
        >
          <Eye className="h-3 w-3" />
        </button>
      </div>

      {/* Small map popup */}
      {showMap && (
        <div className="absolute top-8 left-0 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-2">
          <div className="w-48 h-32 relative">
            <div ref={mapRef} className="w-full h-full rounded"></div>
            {!map && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded">
                <div className="text-xs text-gray-500">Loading map...</div>
              </div>
            )}
          </div>
          <div className="mt-2 text-xs text-gray-600 text-center">
            Farm Location
          </div>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setShowMap(false);
            }}
            className="absolute top-1 right-1 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};

export default LocationMapCell;
