import Jo<PERSON> from "joi";
import asyncHandler from "../middlewares/asyncHandler.js";
import seedsModel from "../models/seedsModel.js";

const seedSchema = Joi.object({
  S_Name: Joi.string().min(3).required(),
  S_Price: Joi.number().min(1).required(),
  S_StartDate: Joi.date().optional(),
  S_ExpireDate: Joi.date().min(Joi.ref("S_StartDate")).required(),
});

const seedsController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = seedSchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    const seed = await seedsModel.create(value);
    res
      .status(201)
      .json({ success: true, message: "Seed added successfully", seed });
  }),

  getAll: asyncHandler(async (req, res) => {
    const seeds = await seedsModel.getAll();
    res.json({
      success: true,
      total: seeds.length,
      message: "Seeds fetched successfully",
      data: seeds,
    });
  }),

  getById: asyncHandler(async (req, res) => {
    const seed = await seedsModel.getById(req.params.id);
    if (!seed) {
      return res.status(404).json({ success: false, error: "Seed not found" });
    }
    res.json({ success: true, data: seed });
  }),

  update: asyncHandler(async (req, res) => {
    const existing = await seedsModel.getById(req.params.id);
    if (!existing) {
      return res.status(404).json({ success: false, error: "Seed not found" });
    }

    const updatedData = {
      S_Name: req.body.S_Name ?? existing.S_Name,
      S_Price: req.body.S_Price ?? existing.S_Price,
      S_StartDate: req.body.S_StartDate ?? existing.S_StartDate,
      S_ExpireDate: req.body.S_ExpireDate ?? existing.S_ExpireDate,
    };

    const { error } = seedSchema.validate(updatedData);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    const updated = await seedsModel.update(req.params.id, updatedData);
    res.json({
      success: true,
      message: "Seed updated successfully",
      seed: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await seedsModel.delete(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, error: "Seed not found" });
    }
    res.json({ success: true, message: "Seed deleted successfully" });
  }),
};

export default seedsController;
