import db from "../config/db.js";

const seedsModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Seeds (S_Name, S_Price, S_StartDate, S_ExpireDate)
       VALUES (?, ?, ?,?)`,
      [
        data.S_Name,
        data.S_Price,
        data.S_StartDate,
        data.S_ExpireDate,
      ],
    );

    const [newSeed] = await db.query("SELECT * FROM Seeds WHERE S_Id = ?", [
      result.insertId,
    ]);
    return newSeed[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM Seeds");
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM Seeds WHERE S_Id = ?", [id]);
    return rows[0];
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Seeds SET S_Name = ?, S_Price = ?,  S_StartDate = ?, S_ExpireDate = ?
       WHERE S_Id = ?`,
      [
        data.S_Name,
        data.S_Price,
        data.S_StartDate,
        data.S_ExpireDate,
        id,
      ],
    );
    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Seeds WHERE S_Id = ?", [id]);
    return result;
  },
};

export default seedsModel;
