import mysql from "mysql2/promise";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";

// Load environment variables
dotenv.config();

async function runProfileFieldsMigration() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASS,
      database: process.env.DB_NAME,
    });

    console.log("Connected to database successfully!");

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'migrations', 'add_user_profile_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`Running ${statements.length} migration statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        await connection.execute(statement);
        console.log(`✓ Statement ${i + 1} executed successfully`);
      } catch (error) {
        // Check if it's a "column already exists" error
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`⚠ Statement ${i + 1} skipped - column already exists`);
        } else {
          throw error;
        }
      }
    }

    console.log("✅ Profile fields migration completed successfully!");

  } catch (error) {
    console.error("❌ Migration failed:", error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log("Database connection closed.");
    }
  }
}

// Run the migration
runProfileFieldsMigration();
