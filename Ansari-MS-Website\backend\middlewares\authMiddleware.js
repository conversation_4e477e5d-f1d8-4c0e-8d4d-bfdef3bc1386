import jwt from "jsonwebtoken";
import UserModel from "../models/userModel.js";
import asyncHandler from "./asyncHandler.js";

const authenticate = asyncHandler(async (req, res, next) => {
  let token = req.headers.authorization?.startsWith("Bearer")
    ? req.headers.authorization.split(" ")[1]
    : null;

  if (!token) {
    return res
      .status(401)
      .json({ success: false, message: "Not authorized, no token." });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const currentUser = await UserModel.getUserById(decoded.userId); // Ensure `getUserById` exists

    if (!currentUser) {
      return res
        .status(401)
        .json({ success: false, message: "User not found." });
    }

    req.user = currentUser;
    next();
  } catch (error) {
    return res
      .status(401)
      .json({ success: false, message: "Not authorized, token failed." });
  }
});

const authorizeAdmin = (req, res, next) => {
  if (req.user && req.user.Role === "admin") {
    next();
  } else {
    return res.status(403).json({
      success: false,
      message: "Access denied: Admin authorization required.",
    });
  }
};
const authorizeShoperAndFarmer = (req, res, next) => {
  if (
    (req.user && req.user.Role === "shopper") ||
    req.user.Role === "farmer" ||
    req.user.Role === "admin"
  ) {
    next();
  } else {
    return res.status(403).json({
      success: false,
      message: "Access denied: shoper ,admin or farmer authorization required.",
    });
  }
};

export { authenticate, authorizeAdmin, authorizeShoperAndFarmer };
