
'use client';

import { useEffect, useState } from 'react';
import { Star } from 'lucide-react';
import Button from '../Button';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const RatingForm = ({ onSubmit }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    message: '',
  });
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [alreadyReviewed, setAlreadyReviewed] = useState(false);
  const [showForm, setShowForm] = useState(true);
  const navigate = useNavigate();

  // Get user data from localStorage
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      // Set name and email based on available properties in user object
      const fullName = `${user.U_FirstName || user.firstName || ''} ${user.U_LastName || user.lastName || ''}`.trim();
      setName(fullName);
      setEmail(user.U_Email || user.email || '');
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleRatingClick = (selectedRating) => {
    setRating(selectedRating);
  };

  const handleRatingHover = (hoveredRating) => {
    setHoveredRating(hoveredRating);
  };

  const handleRatingLeave = () => {
    setHoveredRating(0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.message || rating === 0) {
      setError('Please provide both a rating and a message');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Get token from localStorage
      const token = localStorage.getItem('authToken');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        navigate('/signin');
        setLoading(false);
        return;
      }

      // Prepare request data
      const reviewData = {
        R_Message: formData.message,
        R_Number: rating,
      };

      // Make API request
      const response = await axios.post('http://localhost:5432/api/v1/reviews/', reviewData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // Handle successful response
      if (response.data.success) {
        setSuccess(true);
        setSubmitted(true);
        setShowForm(false);

        // Generate Gravatar URL
        const gravatarUrl = email
          ? `https://www.gravatar.com/avatar/${md5(email.trim().toLowerCase())}?d=identicon`
          : null;

        // Add the new testimonial using the onSubmit prop
        onSubmit &&
          onSubmit(
            {
              name,
              email,
              message: formData.message,
            },
            rating,
            gravatarUrl || './imgs/avatar-placeholder.jpg'
          );

        // Reset form
        setFormData({ message: '' });
        setRating(0);
      }
    } catch (err) {
      // Handle error responses
      if (err.response && err.response.data) {
        setError(err.response.data.error || 'An error occurred while submitting your review');

        // Check if the user has already submitted a review
        if (err.response.data.error === 'You have already submitted a review.') {
          setAlreadyReviewed(true);
          setShowForm(false); // Hide the form if already reviewed
        }
      } else {
        setError('Network error. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // MD5 hash function for Gravatar
  function md5(string) {
    function rotateLeft(lValue, iShiftBits) {
      return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }

    function addUnsigned(lX, lY) {
      const lX8 = lX & 0x80000000;
      const lY8 = lY & 0x80000000;
      const lX4 = lX & 0x40000000;
      const lY4 = lY & 0x40000000;
      const lResult = (lX & 0x3fffffff) + (lY & 0x3fffffff);
      if (lX4 & lY4) return lResult ^ 0x80000000 ^ lX8 ^ lY8;
      if (lX4 | lY4) {
        if (lResult & 0x40000000) return lResult ^ 0xc0000000 ^ lX8 ^ lY8;
        else return lResult ^ 0x40000000 ^ lX8 ^ lY8;
      } else {
        return lResult ^ lX8 ^ lY8;
      }
    }

    function F(x, y, z) {
      return (x & y) | (~x & z);
    }

    function G(x, y, z) {
      return (x & z) | (y & ~z);
    }

    function H(x, y, z) {
      return x ^ y ^ z;
    }

    function I(x, y, z) {
      return y ^ (x | ~z);
    }

    function FF(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function GG(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function HH(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function II(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function convertToWordArray(string) {
      let lWordCount;
      const lMessageLength = string.length;
      const lNumberOfWords_temp1 = lMessageLength + 8;
      const lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
      const lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
      const lWordArray = Array(lNumberOfWords - 1);
      let lBytePosition = 0;
      let lByteCount = 0;
      while (lByteCount < lMessageLength) {
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition);
        lByteCount++;
      }
      lWordCount = (lByteCount - (lByteCount % 4)) / 4;
      lBytePosition = (lByteCount % 4) * 8;
      lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
      lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
      lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
      return lWordArray;
    }

    function wordToHex(lValue) {
      let WordToHexValue = '',
        WordToHexValue_temp = '',
        lByte,
        lCount;
      for (lCount = 0; lCount <= 3; lCount++) {
        lByte = (lValue >>> (lCount * 8)) & 255;
        WordToHexValue_temp = '0' + lByte.toString(16);
        WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2);
      }
      return WordToHexValue;
    }

    let x = [];
    let k, AA, BB, CC, DD, a, b, c, d;
    const S11 = 7,
      S12 = 12,
      S13 = 17,
      S14 = 22;
    const S21 = 5,
      S22 = 9,
      S23 = 14,
      S24 = 20;
    const S31 = 4,
      S32 = 11,
      S33 = 16,
      S34 = 23;
    const S41 = 6,
      S42 = 10,
      S43 = 15,
      S44 = 21;

    string = utf8Encode(string);
    x = convertToWordArray(string);
    a = 0x67452301;
    b = 0xefcdab89;
    c = 0x98badcfe;
    d = 0x10325476;

    for (k = 0; k < x.length; k += 16) {
      AA = a;
      BB = b;
      CC = c;
      DD = d;
      a = FF(a, b, c, d, x[k + 0], S11, 0xd76aa478);
      d = FF(d, a, b, c, x[k + 1], S12, 0xe8c7b756);
      c = FF(c, d, a, b, x[k + 2], S13, 0x242070db);
      b = FF(b, c, d, a, x[k + 3], S14, 0xc1bdceee);
      a = FF(a, b, c, d, x[k + 4], S11, 0xf57c0faf);
      d = FF(d, a, b, c, x[k + 5], S12, 0x4787c62a);
      c = FF(c, d, a, b, x[k + 6], S13, 0xa8304613);
      b = FF(b, c, d, a, x[k + 7], S14, 0xfd469501);
      a = FF(a, b, c, d, x[k + 8], S11, 0x698098d8);
      d = FF(d, a, b, c, x[k + 9], S12, 0x8b44f7af);
      c = FF(c, d, a, b, x[k + 10], S13, 0xffff5bb1);
      b = FF(b, c, d, a, x[k + 11], S14, 0x895cd7be);
      a = FF(a, b, c, d, x[k + 12], S11, 0x6b901122);
      d = FF(d, a, b, c, x[k + 13], S12, 0xfd987193);
      c = FF(c, d, a, b, x[k + 14], S13, 0xa679438e);
      b = FF(b, c, d, a, x[k + 15], S14, 0x49b40821);
      a = GG(a, b, c, d, x[k + 1], S21, 0xf61e2562);
      d = GG(d, a, b, c, x[k + 6], S22, 0xc040b340);
      c = GG(c, d, a, b, x[k + 11], S23, 0x265e5a51);
      b = GG(b, c, d, a, x[k + 0], S24, 0xe9b6c7aa);
      a = GG(a, b, c, d, x[k + 5], S21, 0xd62f105d);
      d = GG(d, a, b, c, x[k + 10], S22, 0x2441453);
      c = GG(c, d, a, b, x[k + 15], S23, 0xd8a1e681);
      b = GG(b, c, d, a, x[k + 4], S24, 0xe7d3fbc8);
      a = GG(a, b, c, d, x[k + 9], S21, 0x21e1cde6);
      d = GG(d, a, b, c, x[k + 14], S22, 0xc33707d6);
      c = GG(c, d, a, b, x[k + 3], S23, 0xf4d50d87);
      b = GG(b, c, d, a, x[k + 8], S24, 0x455a14ed);
      a = GG(a, b, c, d, x[k + 13], S21, 0xa9e3e905);
      d = GG(d, a, b, c, x[k + 2], S22, 0xfcefa3f8);
      c = GG(c, d, a, b, x[k + 7], S23, 0x676f02d9);
      b = GG(b, c, d, a, x[k + 12], S24, 0x8d2a4c8a);
      a = HH(a, b, c, d, x[k + 5], S31, 0xfffa3942);
      d = HH(d, a, b, c, x[k + 8], S32, 0x8771f681);
      c = HH(c, d, a, b, x[k + 11], S33, 0x6d9d6122);
      b = HH(b, c, d, a, x[k + 14], S34, 0xfde5380c);
      a = HH(a, b, c, d, x[k + 1], S31, 0xa4beea44);
      d = HH(d, a, b, c, x[k + 4], S32, 0x4bdecfa9);
      c = HH(c, d, a, b, x[k + 7], S33, 0xf6bb4b60);
      b = HH(b, c, d, a, x[k + 10], S34, 0xbebfbc70);
      a = HH(a, b, c, d, x[k + 13], S31, 0x289b7ec6);
      d = HH(d, a, b, c, x[k + 0], S32, 0xeaa127fa);
      c = HH(c, d, a, b, x[k + 3], S33, 0xd4ef3085);
      b = HH(b, c, d, a, x[k + 6], S34, 0x4881d05);
      a = HH(a, b, c, d, x[k + 9], S31, 0xd9d4d039);
      d = HH(d, a, b, c, x[k + 12], S32, 0xe6db99e5);
      c = HH(c, d, a, b, x[k + 15], S33, 0x1fa27cf8);
      b = HH(b, c, d, a, x[k + 2], S34, 0xc4ac5665);
      a = II(a, b, c, d, x[k + 0], S41, 0xf4292244);
      d = II(d, a, b, c, x[k + 7], S42, 0x432aff97);
      c = II(c, d, a, b, x[k + 14], S43, 0xab9423a7);
      b = II(b, c, d, a, x[k + 5], S44, 0xfc93a039);
      a = II(a, b, c, d, x[k + 12], S41, 0x655b59c3);
      d = II(d, a, b, c, x[k + 3], S42, 0x8f0ccc92);
      c = II(c, d, a, b, x[k + 10], S43, 0xffeff47d);
      b = II(b, c, d, a, x[k + 1], S44, 0x85845dd1);
      a = II(a, b, c, d, x[k + 8], S41, 0x6fa87e4f);
      d = II(d, a, b, c, x[k + 15], S42, 0xfe2ce6e0);
      c = II(c, d, a, b, x[k + 6], S43, 0xa3014314);
      b = II(b, c, d, a, x[k + 13], S44, 0x4e0811a1);
      a = II(a, b, c, d, x[k + 4], S41, 0xf7537e82);
      d = II(d, a, b, c, x[k + 11], S42, 0xbd3af235);
      c = II(c, d, a, b, x[k + 2], S43, 0x2ad7d2bb);
      b = II(b, c, d, a, x[k + 9], S44, 0xeb86d391);
      a = addUnsigned(a, AA);
      b = addUnsigned(b, BB);
      c = addUnsigned(c, CC);
      d = addUnsigned(d, DD);
    }

    const temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
    return temp.toLowerCase();

    function utf8Encode(string) {
      string = string.replace(/\r\n/g, '\n');
      let utftext = '';
      for (let n = 0; n < string.length; n++) {
        const c = string.charCodeAt(n);
        if (c < 128) {
          utftext += String.fromCharCode(c);
        } else if (c > 127 && c < 2048) {
          utftext += String.fromCharCode((c >> 6) | 192);
          utftext += String.fromCharCode((c & 63) | 128);
        } else {
          utftext += String.fromCharCode((c >> 12) | 224);
          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
          utftext += String.fromCharCode((c & 63) | 128);
        }
      }
      return utftext;
    }
  }

  const gravatarUrl = email ? `https://www.gravatar.com/avatar/${md5(email.trim().toLowerCase())}?d=identicon` : null;

  // If form should be hidden, show appropriate message
  if (!showForm) {
    return (
      <div className="text-center">
        {success && (
          <div className="p-6 bg-green-50 rounded-lg border border-green-100">
            <h3 className="text-xl font-bold text-green-700 mb-2">{t("review.thankYouTitle")}</h3>
            <p className="text-green-600">
             {t("review.thankYouMessage")}
            </p>
          </div>
        )}

        {alreadyReviewed && (
          <div className="p-6 bg-amber-50 rounded-lg border border-amber-100">
            <h3 className="text-xl font-bold text-amber-700 mb-2">{t('home.review.alreadyReviewedTitle')}</h3>
            <p className="text-amber-600">{t('home.review.alreadyReviewedMessage')}</p>
          </div>
        )}
      </div>
    );
  }

  // Otherwise show the form
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Display error message if any */}
      {error && <div className="p-3 text-sm text-red-700 bg-red-100 rounded-lg">{t('home.review.errorMessage')}</div>}

      {/* User info with name and email fields */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
          {t('home.review.yourName')}
        </label>
        <input
          type="text"
          id="name"
          name="name"
          readOnly
          value={name}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
          placeholder={t("review.placeholderName")}
        />
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          {t('home.review.yourEmail')}
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={email}
          readOnly
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
          placeholder={t('home.review.placeholderEmail')}
        />
      </div>

      {/* Rating stars */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">{t('home.review.yourRating')}</label>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRatingClick(star)}
              onMouseEnter={() => handleRatingHover(star)}
              onMouseLeave={handleRatingLeave}
              className="focus:outline-none"
            >
              <Star
                size={24}
                className={`${
                  (hoveredRating ? star <= hoveredRating : star <= rating)
                    ? 'text-yellow-500 fill-yellow-500'
                    : 'text-gray-300'
                } transition-colors duration-200`}
              />
            </button>
          ))}
        </div>
      </div>

      {/* Review message textarea */}
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
          {t('home.review.yourReview')}
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          required
          rows="4"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
          placeholder={t('home.review.placeholderMessage')}
        ></textarea>
      </div>

      {/* Submit button */}
      <div className="flex items-center justify-center">
        <Button variant="primary" type="submit" disabled={submitted || rating === 0 || !formData.message || loading}>
          {loading ? t('home.review.submitting') : t('home.review.submitButton')}
        </Button>
      </div>
    </form>
  );
};

export default RatingForm;

// // /* eslint-disable react/prop-types */
// // "use client"

// // import { useEffect, useState } from "react"
// // import { Star } from "lucide-react"
// // import Button from "../Button"
// // import md5 from "blueimp-md5" // Use this npm package instead of a long local function
// // ////
// // const RatingForm = ({ onSubmit }) => {
// //   const [formData, setFormData] = useState({
// //     name: "",
// //     email: "",
// //     message: "",
// //   })

// //   const [rating, setRating] = useState(0)
// //   const [hoveredRating, setHoveredRating] = useState(0)
// //   const [submitted, setSubmitted] = useState(false)

// //   useEffect(() => {
// //     const userData = localStorage.getItem("user")
// //     if (userData) {
// //       const user = JSON.parse(userData)
// //       setFormData((prev) => ({
// //         ...prev,
// //         name: `${user.firstName }`.trim(),
// //         email: user.email || "",
// //       }))
// //     }
// //   }, [])

// //   const handleChange = (e) => {
// //     const { name, value } = e.target
// //     setFormData((prev) => ({
// //       ...prev,
// //       [name]: value,
// //     }))
// //   }

// //   const handleRatingClick = (selectedRating) => {
// //     setRating(selectedRating)
// //   }

// //   const handleRatingHover = (hoveredRating) => {
// //     setHoveredRating(hoveredRating)
// //   }

// //   const handleRatingLeave = () => {
// //     setHoveredRating(0)
// //   }

// //   const handleSubmit = (e) => {
// //     e.preventDefault()
// //     if (!formData.message || rating === 0) return
// //     onSubmit({ ...formData, rating })
// //     setSubmitted(true)
// //   }

// //   const gravatarUrl = `https://www.gravatar.com/avatar/${md5(formData.email.trim().toLowerCase())}?d=identicon`

// //   return (
// //     <form onSubmit={handleSubmit} className="space-y-4">
// //       <div className="flex items-center gap-4">
// //         <img
// //           src={gravatarUrl}
// //           alt="Gravatar"
// //           className="w-10 h-10 rounded-full"
// //         />
// //         <div>
// //           <p className="font-semibold">{formData.name}</p>
// //           <p className="text-sm text-gray-500">{formData.email}</p>
// //         </div>
// //       </div>

// //       <div className="flex items-center space-x-1">
// //         {[1, 2, 3, 4, 5].map((star) => (
// //           <Star
// //             key={star}
// //             className={`w-6 h-6 cursor-pointer transition-colors ${
// //               (hoveredRating || rating) >= star
// //                 ? "text-yellow-500"
// //                 : "text-gray-300"
// //             }`}
// //             onClick={() => handleRatingClick(star)}
// //             onMouseEnter={() => handleRatingHover(star)}
// //             onMouseLeave={handleRatingLeave}
// //           />
// //         ))}
// //       </div>

// //       <textarea
// //         name="message"
// //         value={formData.message}
// //         onChange={handleChange}
// //         placeholder="Write your review..."
// //         rows={4}
// //         className="w-full p-2 border rounded"
// //       />

// //       <Button type="submit" disabled={submitted || rating === 0}>
// //         {submitted ? "Submitted" : "Submit Rating"}
// //       </Button>
// //     </form>
// //   )
// // }

// // export default RatingForm

// ///////////////////
//  /* eslint-disable react/prop-types */
// "use client"

// import { useEffect, useState } from "react"
// import { Star } from "lucide-react"
// import Button from "../Button"

// const RatingForm = ({ onSubmit }) => {
//   // Update the formData state to include email
//   const [formData, setFormData] = useState({
//     name: "",
//     email: "",
//     message: "",
//   })
//   const [name, setName] = useState('');
//   const [email, setEmail] = useState('');
// useEffect(() => {
//   const userData = localStorage.getItem("user")
//   if (userData) {
//     const user = JSON.parse(userData)
//     setFormData((prev) => ({
//       ...prev,
//       name: `${user.firstName }`.trim(),
//       email: user.email || "",
//     }))
//   }
// }, [])
//   const [rating, setRating] = useState(0)
//   const [hoveredRating, setHoveredRating] = useState(0)
//   const [submitted, setSubmitted] = useState(false)

//   const handleChange = (e) => {
//     const { name, value } = e.target
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))
//   }
//   useEffect(() => {
//     const userData = localStorage.getItem('user');
//     if (userData) {
//       const user = JSON.parse(userData);
//       const fullName = `${user.U_FirstName || ''} ${user.U_LastName || ''}`.trim();
//       setName(fullName);
//       setEmail(user.U_Email || '');
//     }
//   }, []);

//   const handleRatingClick = (selectedRating) => {
//     setRating(selectedRating)
//   }

//   const handleRatingHover = (hoveredRating) => {
//     setHoveredRating(hoveredRating)
//   }

//   const handleRatingLeave = () => {
//     setHoveredRating(0)
//   }

//   // Add this function at the top of your component (before the return statement)
//   // MD5 hash function for Gravatar
//   function md5(string) {
//     function rotateLeft(lValue, iShiftBits) {
//       return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits))
//     }

//     function addUnsigned(lX, lY) {
//       const lX8 = lX & 0x80000000
//       const lY8 = lY & 0x80000000
//       const lX4 = lX & 0x40000000
//       const lY4 = lY & 0x40000000
//       const lResult = (lX & 0x3fffffff) + (lY & 0x3fffffff)
//       if (lX4 & lY4) return lResult ^ 0x80000000 ^ lX8 ^ lY8
//       if (lX4 | lY4) {
//         if (lResult & 0x40000000) return lResult ^ 0xc0000000 ^ lX8 ^ lY8
//         else return lResult ^ 0x40000000 ^ lX8 ^ lY8
//       } else {
//         return lResult ^ lX8 ^ lY8
//       }
//     }

//     function F(x, y, z) {
//       return (x & y) | (~x & z)
//     }

//     function G(x, y, z) {
//       return (x & z) | (y & ~z)
//     }

//     function H(x, y, z) {
//       return x ^ y ^ z
//     }

//     function I(x, y, z) {
//       return y ^ (x | ~z)
//     }

//     function FF(a, b, c, d, x, s, ac) {
//       a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac))
//       return addUnsigned(rotateLeft(a, s), b)
//     }

//     function GG(a, b, c, d, x, s, ac) {
//       a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac))
//       return addUnsigned(rotateLeft(a, s), b)
//     }

//     function HH(a, b, c, d, x, s, ac) {
//       a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac))
//       return addUnsigned(rotateLeft(a, s), b)
//     }

//     function II(a, b, c, d, x, s, ac) {
//       a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac))
//       return addUnsigned(rotateLeft(a, s), b)
//     }

//     function convertToWordArray(string) {
//       let lWordCount
//       const lMessageLength = string.length
//       const lNumberOfWords_temp1 = lMessageLength + 8
//       const lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64
//       const lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16
//       const lWordArray = Array(lNumberOfWords - 1)
//       let lBytePosition = 0
//       let lByteCount = 0
//       while (lByteCount < lMessageLength) {
//         lWordCount = (lByteCount - (lByteCount % 4)) / 4
//         lBytePosition = (lByteCount % 4) * 8
//         lWordArray[lWordCount] = lWordArray[lWordCount] | (string.charCodeAt(lByteCount) << lBytePosition)
//         lByteCount++
//       }
//       lWordCount = (lByteCount - (lByteCount % 4)) / 4
//       lBytePosition = (lByteCount % 4) * 8
//       lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition)
//       lWordArray[lNumberOfWords - 2] = lMessageLength << 3
//       lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29
//       return lWordArray
//     }

//     function wordToHex(lValue) {
//       let WordToHexValue = "",
//         WordToHexValue_temp = "",
//         lByte,
//         lCount
//       for (lCount = 0; lCount <= 3; lCount++) {
//         lByte = (lValue >>> (lCount * 8)) & 255
//         WordToHexValue_temp = "0" + lByte.toString(16)
//         WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length - 2, 2)
//       }
//       return WordToHexValue
//     }

//     let x = []
//     let k, AA, BB, CC, DD, a, b, c, d
//     const S11 = 7,
//       S12 = 12,
//       S13 = 17,
//       S14 = 22
//     const S21 = 5,
//       S22 = 9,
//       S23 = 14,
//       S24 = 20
//     const S31 = 4,
//       S32 = 11,
//       S33 = 16,
//       S34 = 23
//     const S41 = 6,
//       S42 = 10,
//       S43 = 15,
//       S44 = 21

//     string = utf8Encode(string)
//     x = convertToWordArray(string)
//     a = 0x67452301
//     b = 0xefcdab89
//     c = 0x98badcfe
//     d = 0x10325476

//     for (k = 0; k < x.length; k += 16) {
//       AA = a
//       BB = b
//       CC = c
//       DD = d
//       a = FF(a, b, c, d, x[k + 0], S11, 0xd76aa478)
//       d = FF(d, a, b, c, x[k + 1], S12, 0xe8c7b756)
//       c = FF(c, d, a, b, x[k + 2], S13, 0x242070db)
//       b = FF(b, c, d, a, x[k + 3], S14, 0xc1bdceee)
//       a = FF(a, b, c, d, x[k + 4], S11, 0xf57c0faf)
//       d = FF(d, a, b, c, x[k + 5], S12, 0x4787c62a)
//       c = FF(c, d, a, b, x[k + 6], S13, 0xa8304613)
//       b = FF(b, c, d, a, x[k + 7], S14, 0xfd469501)
//       a = FF(a, b, c, d, x[k + 8], S11, 0x698098d8)
//       d = FF(d, a, b, c, x[k + 9], S12, 0x8b44f7af)
//       c = FF(c, d, a, b, x[k + 10], S13, 0xffff5bb1)
//       b = FF(b, c, d, a, x[k + 11], S14, 0x895cd7be)
//       a = FF(a, b, c, d, x[k + 12], S11, 0x6b901122)
//       d = FF(d, a, b, c, x[k + 13], S12, 0xfd987193)
//       c = FF(c, d, a, b, x[k + 14], S13, 0xa679438e)
//       b = FF(b, c, d, a, x[k + 15], S14, 0x49b40821)
//       a = GG(a, b, c, d, x[k + 1], S21, 0xf61e2562)
//       d = GG(d, a, b, c, x[k + 6], S22, 0xc040b340)
//       c = GG(c, d, a, b, x[k + 11], S23, 0x265e5a51)
//       b = GG(b, c, d, a, x[k + 0], S24, 0xe9b6c7aa)
//       a = GG(a, b, c, d, x[k + 5], S21, 0xd62f105d)
//       d = GG(d, a, b, c, x[k + 10], S22, 0x2441453)
//       c = GG(c, d, a, b, x[k + 15], S23, 0xd8a1e681)
//       b = GG(b, c, d, a, x[k + 4], S24, 0xe7d3fbc8)
//       a = GG(a, b, c, d, x[k + 9], S21, 0x21e1cde6)
//       d = GG(d, a, b, c, x[k + 14], S22, 0xc33707d6)
//       c = GG(c, d, a, b, x[k + 3], S23, 0xf4d50d87)
//       b = GG(b, c, d, a, x[k + 8], S24, 0x455a14ed)
//       a = GG(a, b, c, d, x[k + 13], S21, 0xa9e3e905)
//       d = GG(d, a, b, c, x[k + 2], S22, 0xfcefa3f8)
//       c = GG(c, d, a, b, x[k + 7], S23, 0x676f02d9)
//       b = GG(b, c, d, a, x[k + 12], S24, 0x8d2a4c8a)
//       a = HH(a, b, c, d, x[k + 5], S31, 0xfffa3942)
//       d = HH(d, a, b, c, x[k + 8], S32, 0x8771f681)
//       c = HH(c, d, a, b, x[k + 11], S33, 0x6d9d6122)
//       b = HH(b, c, d, a, x[k + 14], S34, 0xfde5380c)
//       a = HH(a, b, c, d, x[k + 1], S31, 0xa4beea44)
//       d = HH(d, a, b, c, x[k + 4], S32, 0x4bdecfa9)
//       c = HH(c, d, a, b, x[k + 7], S33, 0xf6bb4b60)
//       b = HH(b, c, d, a, x[k + 10], S34, 0xbebfbc70)
//       a = HH(a, b, c, d, x[k + 13], S31, 0x289b7ec6)
//       d = HH(d, a, b, c, x[k + 0], S32, 0xeaa127fa)
//       c = HH(c, d, a, b, x[k + 3], S33, 0xd4ef3085)
//       b = HH(b, c, d, a, x[k + 6], S34, 0x4881d05)
//       a = HH(a, b, c, d, x[k + 9], S31, 0xd9d4d039)
//       d = HH(d, a, b, c, x[k + 12], S32, 0xe6db99e5)
//       c = HH(c, d, a, b, x[k + 15], S33, 0x1fa27cf8)
//       b = HH(b, c, d, a, x[k + 2], S34, 0xc4ac5665)
//       a = II(a, b, c, d, x[k + 0], S41, 0xf4292244)
//       d = II(d, a, b, c, x[k + 7], S42, 0x432aff97)
//       c = II(c, d, a, b, x[k + 14], S43, 0xab9423a7)
//       b = II(b, c, d, a, x[k + 5], S44, 0xfc93a039)
//       a = II(a, b, c, d, x[k + 12], S41, 0x655b59c3)
//       d = II(d, a, b, c, x[k + 3], S42, 0x8f0ccc92)
//       c = II(c, d, a, b, x[k + 10], S43, 0xffeff47d)
//       b = II(b, c, d, a, x[k + 1], S44, 0x85845dd1)
//       a = II(a, b, c, d, x[k + 8], S41, 0x6fa87e4f)
//       d = II(d, a, b, c, x[k + 15], S42, 0xfe2ce6e0)
//       c = II(c, d, a, b, x[k + 6], S43, 0xa3014314)
//       b = II(b, c, d, a, x[k + 13], S44, 0x4e0811a1)
//       a = II(a, b, c, d, x[k + 4], S41, 0xf7537e82)
//       d = II(d, a, b, c, x[k + 11], S42, 0xbd3af235)
//       c = II(c, d, a, b, x[k + 2], S43, 0x2ad7d2bb)
//       b = II(b, c, d, a, x[k + 9], S44, 0xeb86d391)
//       a = addUnsigned(a, AA)
//       b = addUnsigned(b, BB)
//       c = addUnsigned(c, CC)
//       d = addUnsigned(d, DD)
//     }

//     const temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d)
//     return temp.toLowerCase()

//     function utf8Encode(string) {
//       string = string.replace(/\r\n/g, "\n")
//       let utftext = ""
//       for (let n = 0; n < string.length; n++) {
//         const c = string.charCodeAt(n)
//         if (c < 128) {
//           utftext += String.fromCharCode(c)
//         } else if (c > 127 && c < 2048) {
//           utftext += String.fromCharCode((c >> 6) | 192)
//           utftext += String.fromCharCode((c & 63) | 128)
//         } else {
//           utftext += String.fromCharCode((c >> 12) | 224)
//           utftext += String.fromCharCode(((c >> 6) & 63) | 128)
//           utftext += String.fromCharCode((c & 63) | 128)
//         }
//       }
//       return utftext
//     }
//   }

//   // Replace the handleSubmit function with this updated version
//   const handleSubmit = (e) => {
//     e.preventDefault()

//     if (formData.name && formData.email && formData.message && rating > 0) {
//       // Generate Gravatar URL from email
//       const emailHash = md5(formData.email.trim().toLowerCase())
//       const avatarUrl = `https://www.gravatar.com/avatar/${emailHash}?d=identicon&s=200`

//       // Call the parent component's onSubmit function
//       onSubmit(formData, rating, avatarUrl)

//       // Reset the form
//       setFormData({
//         name: "",
//         email: "",
//         message: "",
//       })
//       setRating(0)

//       // Show success message
//       setSubmitted(true)
//       setTimeout(() => setSubmitted(false), 3000)
//     }
//   }

//   return (
//     <form onSubmit={handleSubmit} className="space-y-6">
//       {submitted && (
//         <div className="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg">
//           Thank you for your feedback! Your review has been submitted.
//         </div>
//       )}

//       <div>
//         <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
//           Your Name
//         </label>
//         <input
//           type="text"
//           id="name"
//           name="name"
//           readOnly
//           value={formData.name}
//           onChange={handleChange}
//           required
//           className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//           placeholder="John Doe"
//         />
//       </div>

//       {/* Add the email field to the form (place this after the name field) */}
//       <div>
//         <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
//           Your Email
//         </label>
//         <input
//           type="email"
//           id="email"
//           name="email"
//           value={formData.email}
//           readOnly
//           onChange={handleChange}
//           required
//           className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//           placeholder="<EMAIL>"
//         />
//       </div>

//       <div>
//         <label className="block text-sm font-medium text-gray-700 mb-1">Your Rating</label>
//         <div className="flex gap-1">
//           {[1, 2, 3, 4, 5].map((star) => (
//             <button
//               key={star}
//               type="button"
//               onClick={() => handleRatingClick(star)}
//               onMouseEnter={() => handleRatingHover(star)}
//               onMouseLeave={handleRatingLeave}
//               className="focus:outline-none"
//             >
//               <Star
//                 size={24}
//                 className={`${
//                   (hoveredRating ? star <= hoveredRating : star <= rating)
//                     ? "text-yellow-500 fill-yellow-500"
//                     : "text-gray-300"
//                 } transition-colors duration-200`}
//               />
//             </button>
//           ))}
//         </div>
//       </div>

//       <div>
//         <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
//           Your Review
//         </label>
//         <textarea
//           id="message"
//           name="message"
//           value={formData.message}
//           onChange={handleChange}
//           required
//           rows="4"
//           className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//           placeholder="Share your experience with us..."
//         ></textarea>
//       </div>

//       <div className="flex items-center justify-center">

//         <Button variant="primary"
//           type="submit"
//           // className="w-full bg-[#FF6B00] text-white py-3 rounded-lg hover:bg-[#D32F2F] transition-colors"
//           >
//           Submit Your Review
//         </Button>
//       </div>
//     </form>
//   )
// }

// export default RatingForm
