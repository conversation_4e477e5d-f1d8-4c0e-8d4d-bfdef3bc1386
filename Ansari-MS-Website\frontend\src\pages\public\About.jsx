import { motion } from 'framer-motion';
import { Check, Users, Award, Leaf, TrendingUp, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const About = () => {
  const { t } = useTranslation();
  // const people = t('about.team.people', { returnObjects: true });

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="min-h-screen pt-15 pb-10">
      {/* Hero Section */}
      <section className="relative h-[90vh] top-16 flex items-center justify-center overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0">
          <img src="/imgs/about.jpg" alt="About background" className="w-full h-full" />
          <div className="absolute inset-0 bg-gradient-to-r from-textSecondary/90 to-textprimary/80 backdrop-blur-0"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay:0.2 }}>
            <div>
              <h1 className="text-4xl md:text-6xl font-extrabold   text-white mb-6 leading-tight flex items-center justify-center rtl:flex-row-reverse gap-2">
                {t('about.title')} <strong className="text-primary">{t('about.Stitle')}</strong>
              </h1>
            </div>
            <p className="  text-xl  md:text-2xl text-white/90 max-w-3xl mx-auto mb-4 leading-relaxed">
              {t('about.subtitle')}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="relative top-16 py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <img
                src="/imgs/chickens.jpg"
                alt="Our farm"
                className="w-full h-full object-cover  rounded-2xl shadow-2xl z-10 relative"
              />
              <div className="absolute -bottom-3 -right-3  w-24 md:w-32 h-24 md:h-32 bg-primary rounded-2xl -z-10"></div>
              <div className="absolute -top-3 -left-3 w-24 md:w-32 h-24 md:h-32 border-2 border-[#2C3E50] rounded-2xl -z-10"></div>
            </motion.div>

            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="text-primary   text-lg uppercase tracking-wider">
                {t('about.story.title')}
              </span>
              <h2 className="text-2xl md:text-3xl font-bold  text-textprimary">{t('about.story.subtitle')}</h2>
              <div className="w-full h-1 bg-primary rounded-full"></div>
              <p className="  text-lg  text-textprimary/80 leading-relaxed">{t('about.story.p1')}</p>
              <p className="  text-lg  text-textprimary/80 leading-relaxed">{t('about.story.p2')}</p>
              <p className="  text-lg  text-textprimary/80 leading-relaxed">{t('about.story.p3')}</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="relative top-16 py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-destructive   text-lg uppercase tracking-wider">
              {t('about.values.title')}
            </span>
            <h2 className="text-2xl md:text-4xl font-bold  text-textprimary mt-2">{t('about.values.subtitle')}</h2>
            <div className="w-full md:w-2/5 h-1 bg-destructive mx-auto mt-6 rounded-full"></div>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.div
              className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-6">
                <Leaf className="text-destructive w-8 h-8" />
              </div>
              <h3 className="text-xl  font-bold text-textSecondary mb-4">{t('about.values.sustainability.title')}</h3>
              <p className="  text-textprimary/80">{t('about.values.sustainability.description')}</p>
            </motion.div>

            <motion.div
              className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-6">
                <Award className="text-primary w-8 h-8" />
              </div>
              <h3 className="text-xl  font-bold text-textSecondary mb-4">{t('about.values.quality.title')}</h3>
              <p className="  text-textprimary/80 ">{t('about.values.quality.description')}</p>
            </motion.div>

            <motion.div
              className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-shadow"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-full bg-[#D32F2F]/10 flex items-center justify-center mb-6">
                <Users className="text-[#D32F2F] w-8 h-8" />
              </div>
              <h3 className="text-xl  font-bold text-textSecondary mb-4">{t('about.values.community.title')}</h3>
              <p className="   text-textprimary/80 ">
                {t('about.values.community.description')}
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="relative top-16 py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              className="space-y-5 order-2 md:order-1"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <span className="text-destructive  text-lg uppercase tracking-wider">
                {t('about.approach.title')}
              </span>
              <h2 className="text-2xl md:text-4xl font-bold  text-textprimary">{t('about.approach.subtitle')}</h2>
              <div className="w-full md:w-10/12 h-1 bg-destructive rounded-full"></div>
              <p className="  text-lg  text-textprimary/80 leading-relaxed">
                {t('about.approach.description')}
              </p>

              <ul className="space-y-3">
                {t('about.approach.practices', { returnObjects: true }).map((item, index) => (
                  <motion.li
                    key={index}
                    className="flex items-center gap-3 text-lg text-textprimary"
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                  >
                    <div className="w-6 h-6 rounded-full bg-destructive flex items-center justify-center text-white">
                      <Check size={14} />
                    </div>
                    {item}
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              className="relative order-1 md:order-2"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <img
                src="/imgs/fresh-hens.jpg"
                alt="Ethical farming"
                className="w-full h-auto rounded-2xl shadow-2xl z-10 relative"
              />
              <div className="absolute -bottom-3 md:-bottom-6 -left-3 md:-left-4.5 w-24 md:w-32 h-24 md:h-32 bg-destructive rounded-2xl -z-10"></div>
              <div className="absolute -top-3 md:-top-6 -right-3 md:-right-4.5 w-24 md:w-32 h-24 md:h-32 border-2 border-secondary rounded-2xl -z-10"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative top-16 py-16 px-4 bg-secondary text-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary   text-base md:text-lg uppercase tracking-wider">
              {t('about.impact.title')}
            </span>
            <h2 className="text-2xl md:text-4xl font-bold  text-white mt-2">{t('about.impact.subtitle')}</h2>
            <div className="w-56 md:w-1/3 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              {
                value: '18+',
                label: t('about.impact.experience'),
                icon: <TrendingUp className="text-primary w-8 h-8" />,
              },
              {
                value: '5,000+',
                label: t('about.impact.customers'),
                icon: <Users className="text-primary w-8 h-8" />,
              },
              {
                value: '20+',
                label: t('about.impact.breeds'),
                icon: <Award className="text-primary w-8 h-8" />,
              },
              {
                value: '100%',
                label: t('about.impact.satisfaction'),
                icon: <Check className="text-primary w-8 h-8" />,
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="mx-auto mb-4 flex justify-center">{stat.icon}</div>
                <div className="text-2xl md:text-4xl font-bold mb-2">{stat.value}</div>
                <div className=" text-sm text-white/80">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="relative top-16 py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-destructive   text-lg uppercase tracking-wider">
              {t('about.team.title')}
            </span>
            <h2 className="text-3xl md:text-4xl font-bold  text-textprimary mt-2">{t('about.team.subtitle')}</h2>
            <div className="w-7/12 h-1 bg-destructive mx-auto mt-6 rounded-full"></div>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {t('about.team.people', { returnObjects: true }).map((member, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                variants={fadeIn}
              >
                <div className="h-64 overflow-hidden">
                  <img
                    src={member.image || '/placeholder.svg'}
                    alt={member.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-textSecondary mb-1">{member.name}</h3>
                  <p className="  text-primary  mb-4">{member.role}</p>
                  <p className="  text-textprimary/80">{member.bio}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative top-16 py-16 px-4 bg-primary">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl md:text-4xl  font-bold text-white mb-6">{t('about.cta.title')}</h2>
            <p className="  text-white/90  text-base md:text-lg mb-8 max-w-2xl mx-auto">
              {t('about.cta.description')}
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center bg-white text-primary font-bold px-4 md:px-8 py-2 md:py-4 rounded-sm hover:bg-secondary rtl:flex-row-reverse hover:text-white transition-all duration-350 shadow-lg"
            >
              {t('about.cta.button')} <ArrowRight className="ml-2 " />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default About;
