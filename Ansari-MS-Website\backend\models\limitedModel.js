import pool from "../config/db.js";

const LimitedModel = {
  createLimited: async (data) => {
    const {
      Lname,
      LDescription,
      Ladd,
      logo = "default-logo.jpg",
      LEamil,
      LPhone1,
      LPhone2,
      userId,
    } = data;

    const query = `
      INSERT INTO Limited (Lname, LDescription, Ladd, logo, LEamil, LPhone1, LPhone2, userId)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const [result] = await pool.query(query, [
      Lname,
      LDescription,
      Ladd,
      logo,
      LEamil,
      LPhone1,
      LPhone2,
      userId,
    ]);
    return { Lid: result.insertId, ...data };
  },

  getAllLimited: async () => {
    const [rows] = await pool.query(`SELECT * FROM Limited`);
    return rows;
  },

  getLimitedById: async (id) => {
    const [rows] = await pool.query(`SELECT * FROM Limited WHERE Lid = ?`, [
      id,
    ]);
    return rows.length > 0 ? rows[0] : null;
  },

  updateLimited: async (id, data) => {
    const {
      Lname,
      LDescription,
      Ladd,
      logo,
      LEamil,
      LPhone1,
      LPhone2,
      userId,
    } = data;

    const query = `
      UPDATE Limited
      SET Lname = ?, LDescription = ?, Ladd = ?, logo = ?, LEamil = ?, LPhone1 = ?, LPhone2 = ?, userId = ?
      WHERE Lid = ?
    `;
    const [result] = await pool.query(query, [
      Lname,
      LDescription,
      Ladd,
      logo,
      LEamil,
      LPhone1,
      LPhone2,
      userId,
      id,
    ]);
    return result;
  },

  deleteLimited: async (id) => {
    const [result] = await pool.query(`DELETE FROM Limited WHERE Lid = ?`, [
      id,
    ]);
    return result;
  },
  // In limitedModel.js
  isEmailTaken: async (email) => {
    const [rows] = await pool.query(
      `SELECT Lid FROM Limited WHERE LEamil = ?`,
      [email],
    );
    return rows.length > 0;
  },

  isPhone1Taken: async (phone) => {
    const [rows] = await pool.query(
      `SELECT Lid FROM Limited WHERE LPhone1 = ?`,
      [phone],
    );
    return rows.length > 0;
  },

  isPhone2Taken: async (phone) => {
    const [rows] = await pool.query(
      `SELECT Lid FROM Limited WHERE LPhone2 = ?`,
      [phone],
    );
    return rows.length > 0;
  },
};

export default LimitedModel;
