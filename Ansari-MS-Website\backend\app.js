import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import path from "path";
import rateLimit from "express-rate-limit";
import helmet from "helmet";
import mongoSanitize from "express-mongo-sanitize";
import xss from "xss-clean";
import morgan from "morgan";

import userRoutes from "./routes/userRoutes.js";
import farmRoutes from "./routes/farmRoutes.js";
import limitedRoutes from "./routes/limitedRoutes.js";
import contactUsRoutes from "./routes/contactUsRoutes.js";
import articalsRoutes from "./routes/articleRoutes.js";
import reviewsRoutes from "./routes/reviewsRoutes.js";
import drugsRoute from "./routes/drugsRoute.js";
import seedsRoute from "./routes/seedsRoute.js";
import servicesRoute from "./routes/servicesRoute.js";
import shopRoutes from "./routes/shopRoute.js";
import chickenRoutes from "./routes/chickenRoutes.js";
import shopperChickenRoutes from "./routes/shopperChickenRoutes.js";
import chatRoutes from "./routes/chatRoutes.js";

const app = express();
dotenv.config();

// GLOBAL MIDDLEWARES
// Serving static files
app.use("/public", express.static(path.join(process.cwd(), "public")));

// Set security HTTP headers
app.use(helmet());
// console.log("Current NODE_ENV:", process.env.NODE_ENV);
// Development logging
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}
// Configure CORS middleware
app.use(
  cors({
    origin: "http://localhost:3000", // Frontend URL
    credentials: true, // Allow credentials (cookies, authorization headers)
  })
);

// Limit requests from same API
const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: 180,
  message: "Too many requests. Please slow down.",
});

// const limiter = rateLimit({
//   windowMs: 60 * 1000, // 1 دقیقه
//   max: 180, // هر IP ته تر 180 requests اجازه
//   message: "Too many requests. Please slow down.",
//   skip: (req, res) => req.method === "OPTIONS", // ✅ د preflight غوښتنې بای پاس کوي
// });
app.use("/api", limiter);

app.use(express.json({ limit: "2mb" }));
app.use(express.urlencoded({ extended: true, limit: "10kb" }));
app.use(cookieParser());

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Data sanitization against XSS
app.use(xss());

// Routes
app.use("/api/v1/users", userRoutes);
app.use("/api/v1/farms", farmRoutes);
app.use("/api/v1/limited", limitedRoutes);
app.use("/api/v1/contact-us", contactUsRoutes);
app.use("/api/v1/news", articalsRoutes);
app.use("/api/v1/reviews", reviewsRoutes);
app.use("/api/v1/drugs", drugsRoute);
app.use("/api/v1/seeds", seedsRoute);
app.use("/api/v1/services", servicesRoute);
app.use("/api/v1/shops", shopRoutes);
app.use("/api/v1/chickens", chickenRoutes);
app.use("/api/v1/shopper/chickens", shopperChickenRoutes);
app.use("/api/v1/chats", chatRoutes);

app.all("*", (req, res, next) => {
  const error = new Error(`Can't find ${req.originalUrl} on this server!`);
  error.statusCode = 404;
  next(error);
});

export default app;
