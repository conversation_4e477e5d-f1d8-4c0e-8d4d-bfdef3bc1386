-- Migration to add message editing and deletion functionality
-- Add new columns to chat_messages table

ALTER TABLE chat_messages 
ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN is_edited BOOLEAN DEFAULT FALSE,
ADD COLUMN edited_at TIMESTAMP NULL,
ADD COLUMN original_content TEXT NULL;

-- Add indexes for better performance
CREATE INDEX idx_chat_messages_is_deleted ON chat_messages(is_deleted);
CREATE INDEX idx_chat_messages_is_edited ON chat_messages(is_edited);

-- Update existing messages to have default values
UPDATE chat_messages 
SET is_deleted = FALSE, is_edited = FALSE 
WHERE is_deleted IS NULL OR is_edited IS NULL;
