import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON>ontainer, <PERSON>, Tooltip } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from './chart';

export function MedicineDistributionChart({ data }) {
  // Group medicines by type
  const typeGroups = data.reduce((acc, medicine) => {
    if (!acc[medicine.type]) {
      acc[medicine.type] = {
        name: medicine.type,
        value: 0,
        count: 0,
      };
    }
    acc[medicine.type].value += medicine.quantity;
    acc[medicine.type].count += 1;
    return acc;
  }, {});

  const chartData = Object.values(typeGroups);

  const COLORS = ['#FF6B00', '#2C3E50', '#388E3C', '#D32F2F', '#9C27B0', '#1976D2'];

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                return (
                  <ChartTooltip>
                    <ChartTooltipContent>
                      <div className="font-medium">{payload[0].name}</div>
                      <div className="flex flex-col gap-1 mt-2">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full" style={{ backgroundColor: payload[0].payload.fill }} />
                          <div>Quantity: {payload[0].value}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full" style={{ backgroundColor: payload[0].payload.fill }} />
                          <div>Count: {payload[0].payload.count} items</div>
                        </div>
                      </div>
                    </ChartTooltipContent>
                  </ChartTooltip>
                );
              }
              return null;
            }}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}
