"use client"

import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { useServices } from "../../contexts/ServicesContext"
import { ArrowLeft, Upload, X, Plus, Minus } from "lucide-react"

const SERVICES_CATEGORIES = ["Premium Hens", "Baby Chickens", "Wholesale"]

const EditService = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { getServiceById, updateService, loading: contextLoading } = useServices()
  const [formData, setFormData] = useState({
    title: "",
    category: "",
    description: "",
    price: "",
    status: "active",
    features: [""],
  })
  const [imageFile, setImageFile] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [notFound, setNotFound] = useState(false)

  useEffect(() => {
    const fetchService = async () => {
      try {
        setLoading(true)
        const service = getServiceById(id)

        if (!service) {
          setNotFound(true)
          setError("Service not found")
          return
        }

        console.log("Fetched service:", service) // Add this debug log

        // Map backend field names to frontend field names
        setFormData({
          title: service.Title || "",
          category: service.Category || "",
          description: service.Description || "",
          price: service.Price || "",
          status: service.Status || "active",
          features: Array.isArray(service.Features) ? service.Features : [""],
        })

        // Set image preview if exists
        if (service.Image) {
          // Construct the full image URL based on your backend structure
          const imageUrl = `http://localhost:5432/public/images/services/${service.Image}`
          setImagePreview(imageUrl)
        }
      } catch (error) {
        console.error("Error fetching service:", error)
        setError("Failed to load service data")
      } finally {
        setLoading(false)
      }
    }

    fetchService()
  }, [id, getServiceById])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleFeatureChange = (index, value) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures[index] = value
    setFormData((prev) => ({
      ...prev,
      features: updatedFeatures,
    }))
  }

  const addFeature = () => {
    setFormData((prev) => ({
      ...prev,
      features: [...prev.features, ""],
    }))
  }

  const removeFeature = (index) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures.splice(index, 1)
    setFormData((prev) => ({
      ...prev,
      features: updatedFeatures,
    }))
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    if (file.size > 5 * 1024 * 1024) {
      setError("Image size should be less than 5MB")
      return
    }

    const reader = new FileReader()
    reader.onloadend = () => {
      setImagePreview(reader.result)
      setFormData((prev) => ({
        ...prev,
        image: reader.result,
      }))
    }
    reader.readAsDataURL(file)
  }

  const removeImage = () => {
    setImagePreview(null)
    setImageFile(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError("")

    // Validate title
    if (!formData.title.trim()) {
      setError("Title is required")
      return
    }
    if (formData.title.trim().length < 3) {
      setError("Title must be at least 3 characters long")
      return
    }

    // Validate category
    const validCategories = ["Premium Hens", "Baby Chickens", "Wholesale"]
    if (!formData.category.trim() || !validCategories.includes(formData.category)) {
      setError("Valid category is required")
      return
    }

    // Validate price
    if (formData.price === "" || formData.price === null) {
      setError("Price is required")
      return
    }
    const priceValue = Number.parseFloat(formData.price)
    if (isNaN(priceValue) || priceValue < 0) {
      setError("Price must be a valid number equal or greater than 0")
      return
    }

    // Validate status
    const validStatuses = ["active", "inactive"]
    if (formData.status && !validStatuses.includes(formData.status)) {
      setError("Status must be either active or inactive")
      return
    }

    // Validate description
    if (!formData.description.trim()) {
      setError("Description is required")
      return
    }
    if (formData.description.trim().length < 5) {
      setError("Description must be at least 5 characters long")
      return
    }

    // Filter out empty features
    const filteredFeatures = formData.features.filter((feature) => feature.trim() !== "")

    if (filteredFeatures.length > 30) {
      setError("You can only add up to 30 features")
      return
    }

    try {
      setLoading(true)
      setError("")
      setSuccess("")

      // Prepare data for API
      const serviceData = {
        ...formData,
        price: priceValue,
        features: filteredFeatures.length > 0 ? filteredFeatures : [],
      }

      console.log("Updating service with data:", serviceData) // Add this debug log

      await updateService(id, serviceData)

      // Show success message
      setSuccess("Service updated successfully!")

      // Auto-redirect after 2 seconds
      setTimeout(() => {
        navigate("/admin/services")
      }, 2000)

    } catch (error) {
      console.error("Error updating service:", error)
      setError("Failed to update service. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const isSubmitting = loading || contextLoading

  if (notFound) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Service Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The service you're trying to edit doesn't exist or has been removed.
          </p>
          <button
            onClick={() => navigate("/admin/services")}
            className="px-4 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#FF6B00]/90"
          >
            Back to Services
          </button>
        </div>
      </div>
    )
  }

  if (loading && !formData.title) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-muted dark:bg-gray-900 p-4 sm:p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6 flex items-center">
          <button
            onClick={() => navigate("/admin/services")}
            className="mr-4 p-2 rounded-lg hover:bg-card dark:hover:bg-gray-700 transition-colors border border-border-color"
          >
            <ArrowLeft size={20} className="text-textprimary dark:text-gray-300" />
          </button>
          <h1 className="text-2xl font-headingEn font-bold text-secondary dark:text-white">Edit Service</h1>
        </div>

        {error && <div className="mb-6 p-4 bg-destructive/10 text-destructive rounded-lg border border-destructive/20 font-bodyEn">{error}</div>}
        {success && <div className="mb-6 p-4 bg-success/10 text-success rounded-lg border border-success/20 font-bodyEn">{success}</div>}

        <form onSubmit={handleSubmit} className="bg-card dark:bg-gray-800 rounded-lg shadow-card border border-border-color p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                placeholder="Enter service title"
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select a category</option>
                {SERVICES_CATEGORIES.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Price *
              </label>
              <input
                type="text"
                id="price"
                name="price"
                value={formData.price}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                placeholder="Enter price (e.g., 450)"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows="4"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              placeholder="Enter service description"
            ></textarea>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Features
              <button
                type="button"
                onClick={addFeature}
                className="ml-2 inline-flex items-center p-1 bg-[#FF6B00] text-white rounded-full hover:bg-[#FF6B00]/90"
              >
                <Plus size={16} />
              </button>
            </label>
            {formData.features.map((feature, index) => (
              <div key={index} className="flex items-center mb-2">
                <input
                  type="text"
                  value={feature}
                  onChange={(e) => handleFeatureChange(index, e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder={`Feature ${index + 1}`}
                />
                {formData.features.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeFeature(index)}
                    className="ml-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <Minus size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Service Image</label>
            {!imagePreview ? (
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600 dark:text-gray-400">
                    <label
                      htmlFor="image-upload"
                      className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#FF6B00] hover:text-[#FF6B00]/80 focus-within:outline-none"
                    >
                      <span>Upload an image</span>
                      <input
                        id="image-upload"
                        name="image-upload"
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 5MB</p>
                </div>
              </div>
            ) : (
              <div className="mt-1 relative">
                <img
                  src={imagePreview || "/placeholder.svg"}
                  alt="Preview"
                  className="h-64 w-full object-cover rounded-lg"
                />
                <button
                  type="button"
                  onClick={removeImage}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate("/admin/services")}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] flex items-center"
            >
              {isSubmitting ? (
                <>
                  <span className="animate-spin mr-2">⟳</span>
                  Saving...
                </>
              ) : (
                "Update Service"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditService



////////////////////

// 'use client';

// import { useState, useEffect } from 'react';
// import { useNavigate, useParams } from 'react-router-dom';
// import { useServices } from '../../contexts/ServicesContext';
// import { ArrowLeft, Upload, X, Plus, Minus } from 'lucide-react';

// const EditService = () => {
//   const navigate = useNavigate();
//   const { id } = useParams();
//   const { getServiceById, updateService } = useServices();
//   const [formData, setFormData] = useState({
//     title: '',
//     category: '',
//     description: '',
//     price: '',
//     status: 'active',
//     image: '',
//     features: [''],
//   });
//   const [imagePreview, setImagePreview] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState('');
//   const [notFound, setNotFound] = useState(false);

//   useEffect(() => {
//     const fetchService = async () => {
//       try {
//         setLoading(true);
//         const service = getServiceById(id);

//         if (!service) {
//           setNotFound(true);
//           setError('Service not found');
//           return;
//         }

//         setFormData({
//           title: service.title || '',
//           category: service.category || '',
//           description: service.description || '',
//           price: service.price || '',
//           status: service.status || 'active',
//           image: service.image || '',
//           features: service.features && service.features.length > 0 ? service.features : [''],
//         });

//         if (service.image) {
//           setImagePreview(service.image);
//         }
//       } catch (error) {
//         console.error('Error fetching service:', error);
//         setError('Failed to load service data');
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchService();
//   }, [id, getServiceById]);

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }));
//   };

//   const handleFeatureChange = (index, value) => {
//     const updatedFeatures = [...formData.features];
//     updatedFeatures[index] = value;
//     setFormData((prev) => ({
//       ...prev,
//       features: updatedFeatures,
//     }));
//   };

//   const addFeature = () => {
//     setFormData((prev) => ({
//       ...prev,
//       features: [...prev.features, ''],
//     }));
//   };

//   const removeFeature = (index) => {
//     const updatedFeatures = [...formData.features];
//     updatedFeatures.splice(index, 1);
//     setFormData((prev) => ({
//       ...prev,
//       features: updatedFeatures,
//     }));
//   };

//   const handleImageChange = (e) => {
//     const file = e.target.files[0];
//     if (!file) return;

//     if (file.size > 5 * 1024 * 1024) {
//       setError('Image size should be less than 5MB');
//       return;
//     }

//     const reader = new FileReader();
//     reader.onloadend = () => {
//       setImagePreview(reader.result);
//       setFormData((prev) => ({
//         ...prev,
//         image: reader.result,
//       }));
//     };
//     reader.readAsDataURL(file);
//   };

//   const removeImage = () => {
//     setImagePreview(null);
//     setFormData((prev) => ({
//       ...prev,
//       image: '',
//     }));
//   };

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setError('');

//     // Validate form
//     if (!formData.title.trim()) {
//       setError('Title is required');
//       return;
//     }

//     if (!formData.category.trim()) {
//       setError('Category is required');
//       return;
//     }

//     if (!formData.description.trim()) {
//       setError('Description is required');
//       return;
//     }

//     if (!formData.price.trim()) {
//       setError('Price is required');
//       return;
//     }

//     // Filter out empty features
//     const filteredFeatures = formData.features.filter((feature) => feature.trim() !== '');

//     try {
//       setLoading(true);
//       await updateService(id, {
//         ...formData,
//         features: filteredFeatures,
//         updatedAt: new Date().toISOString(),
//       });
//       navigate('/admin/services');
//     } catch (error) {
//       console.error('Error updating service:', error);
//       setError('Failed to update service. Please try again.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   if (notFound) {
//     return (
//       <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
//         <div className="max-w-4xl mx-auto text-center">
//           <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Service Not Found</h1>
//           <p className="text-gray-600 dark:text-gray-400 mb-6">
//             The service you're trying to edit doesn't exist or has been removed.
//           </p>
//           <button
//             onClick={() => navigate('/admin/services')}
//             className="px-4 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#FF6B00]/90"
//           >
//             Back to Services
//           </button>
//         </div>
//       </div>
//     );
//   }

//   if (loading && !formData.title) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
//       <div className="max-w-4xl mx-auto">
//         <div className="mb-6 flex items-center">
//           <button
//             onClick={() => navigate('/admin/services')}
//             className="mr-4 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
//           >
//             <ArrowLeft size={20} className="text-gray-600 dark:text-gray-300" />
//           </button>
//           <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Service</h1>
//         </div>

//         {error && <div className="mb-6 p-4 bg-red-100 text-red-800 rounded-lg">{error}</div>}

//         <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
//             <div>
//               <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Title *
//               </label>
//               <input
//                 type="text"
//                 id="title"
//                 name="title"
//                 value={formData.title}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 placeholder="Enter service title"
//               />
//             </div>

//             <div>
//               <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Category *
//               </label>
//               <input
//                 type="text"
//                 id="category"
//                 name="category"
//                 value={formData.category}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 placeholder="Enter category (e.g., Premium Hens, Baby Chickens)"
//               />
//             </div>

//             <div>
//               <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Price *
//               </label>
//               <input
//                 type="text"
//                 id="price"
//                 name="price"
//                 value={formData.price}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 placeholder="Enter price (e.g., 450 AF per hen)"
//               />
//             </div>

//             <div>
//               <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Status
//               </label>
//               <select
//                 id="status"
//                 name="status"
//                 value={formData.status}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               >
//                 <option value="active">Active</option>
//                 <option value="inactive">Inactive</option>
//               </select>
//             </div>
//           </div>

//           <div className="mb-6">
//             <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//               Description *
//             </label>
//             <textarea
//               id="description"
//               name="description"
//               value={formData.description}
//               onChange={handleChange}
//               rows="4"
//               className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               placeholder="Enter service description"
//             ></textarea>
//           </div>

//           <div className="mb-6">
//             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//               Features
//               <button
//                 type="button"
//                 onClick={addFeature}
//                 className="ml-2 inline-flex items-center p-1 bg-[#FF6B00] text-white rounded-full hover:bg-[#FF6B00]/90"
//               >
//                 <Plus size={16} />
//               </button>
//             </label>
//             {formData.features.map((feature, index) => (
//               <div key={index} className="flex items-center mb-2">
//                 <input
//                   type="text"
//                   value={feature}
//                   onChange={(e) => handleFeatureChange(index, e.target.value)}
//                   className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                   placeholder={`Feature ${index + 1}`}
//                 />
//                 {formData.features.length > 1 && (
//                   <button
//                     type="button"
//                     onClick={() => removeFeature(index)}
//                     className="ml-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
//                   >
//                     <Minus size={16} />
//                   </button>
//                 )}
//               </div>
//             ))}
//           </div>

//           <div className="mb-6">
//             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Service Image</label>
//             {!imagePreview ? (
//               <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg">
//                 <div className="space-y-1 text-center">
//                   <Upload className="mx-auto h-12 w-12 text-gray-400" />
//                   <div className="flex text-sm text-gray-600 dark:text-gray-400">
//                     <label
//                       htmlFor="image-upload"
//                       className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#FF6B00] hover:text-[#FF6B00]/80 focus-within:outline-none"
//                     >
//                       <span>Upload an image</span>
//                       <input
//                         id="image-upload"
//                         name="image-upload"
//                         type="file"
//                         className="sr-only"
//                         accept="image/*"
//                         onChange={handleImageChange}
//                       />
//                     </label>
//                     <p className="pl-1">or drag and drop</p>
//                   </div>
//                   <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 5MB</p>
//                 </div>
//               </div>
//             ) : (
//               <div className="mt-1 relative">
//                 <img
//                   src={imagePreview || '/placeholder.svg'}
//                   alt="Preview"
//                   className="h-64 w-full object-cover rounded-lg"
//                 />
//                 <button
//                   type="button"
//                   onClick={removeImage}
//                   className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
//                 >
//                   <X size={16} />
//                 </button>
//               </div>
//             )}
//           </div>

//           <div className="flex justify-end space-x-4">
//             <button
//               type="button"
//               onClick={() => navigate('/admin/services')}
//               className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
//             >
//               Cancel
//             </button>
//             <button
//               type="submit"
//               disabled={loading}
//               className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] flex items-center"
//             >
//               {loading ? (
//                 <>
//                   <span className="animate-spin mr-2">⟳</span>
//                   Saving...
//                 </>
//               ) : (
//                 'Update Service'
//               )}
//             </button>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default EditService;

// // "use client"

// // import React, { useState, useEffect } from "react"
// // import { useNavigate, useParams } from "react-router-dom"
// // import { useServices } from "../../contexts/ServicesContext"
// // import { ArrowLeft } from "lucide-react"

// // const EditService = () => {
// //   const navigate = useNavigate()
// //   const { id } = useParams()
// //   const { getServiceById, updateService } = useServices()
// //   const [loading, setLoading] = useState(false)
// //   const [feedback, setFeedback] = useState({ type: "", message: "" })
// //   const [formData, setFormData] = useState({
// //     title: "",
// //     category: "",
// //     description: "",
// //     excerpt: "",
// //     features: [""],
// //     price: "",
// //     status: "active",
// //     image: null,
// //   })

// //   useEffect(() => {
// //     const loadService = () => {
// //       try {
// //         const service = getServiceById(id)
// //         if (service) {
// //           setFormData({
// //             ...service,
// //             features: service.features?.length > 0 ? service.features : [""],
// //           })
// //         } else {
// //           setFeedback({
// //             type: "error",
// //             message: "Service not found",
// //           })
// //           setTimeout(() => navigate("/admin/services"), 2000)
// //         }
// //       } catch (error) {
// //         console.error("Error loading service:", error)
// //         setFeedback({
// //           type: "error",
// //           message: "Error loading service. Please try again.",
// //         })
// //       }
// //     }

// //     loadService()
// //   }, [id, getServiceById, navigate])

// //   const handleInputChange = (e) => {
// //     const { name, value } = e.target
// //     setFormData((prev) => ({
// //       ...prev,
// //       [name]: value,
// //     }))
// //   }

// //   const handleFeatureChange = (index, value) => {
// //     const newFeatures = [...formData.features]
// //     newFeatures[index] = value
// //     setFormData((prev) => ({
// //       ...prev,
// //       features: newFeatures,
// //     }))
// //   }

// //   const addFeature = () => {
// //     setFormData((prev) => ({
// //       ...prev,
// //       features: [...prev.features, ""],
// //     }))
// //   }

// //   const removeFeature = (index) => {
// //     setFormData((prev) => ({
// //       ...prev,
// //       features: prev.features.filter((_, i) => i !== index),
// //     }))
// //   }

// //   const handleImageChange = (e) => {
// //     const file = e.target.files[0]
// //     if (file) {
// //       const reader = new FileReader()
// //       reader.onloadend = () => {
// //         setFormData((prev) => ({
// //           ...prev,
// //           image: reader.result,
// //         }))
// //       }
// //       reader.readAsDataURL(file)
// //     }
// //   }

// //   const handleSubmit = async (e) => {
// //     e.preventDefault()
// //     setLoading(true)

// //     try {
// //       // Filter out empty features
// //       const cleanedFeatures = formData.features.filter((feature) => feature.trim() !== "")

// //       await updateService(id, {
// //         ...formData,
// //         features: cleanedFeatures,
// //       })

// //       setFeedback({
// //         type: "success",
// //         message: "Service updated successfully!",
// //       })

// //       // Navigate back after successful update
// //       setTimeout(() => {
// //         navigate("/admin/services")
// //       }, 2000)
// //     } catch (error) {
// //       console.error("Error updating service:", error)
// //       setFeedback({
// //         type: "error",
// //         message: "Failed to update service. Please try again.",
// //       })
// //     } finally {
// //       setLoading(false)
// //     }
// //   }

// //   return (
// //     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
// //       <div className="max-w-4xl mx-auto">
// //         {/* Header */}
// //         <div className="flex items-center mb-6">
// //           <button
// //             onClick={() => navigate("/admin/services")}
// //             className="mr-4 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
// //           >
// //             <ArrowLeft size={24} className="text-gray-500 dark:text-gray-400" />
// //           </button>
// //           <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Service</h1>
// //         </div>

// //         {/* Feedback Message */}
// //         {feedback.message && (
// //           <div
// //             className={`mb-6 p-4 rounded-lg ${
// //               feedback.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
// //             }`}
// //           >
// //             {feedback.message}
// //           </div>
// //         )}

// //         {/* Form */}
// //         <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
// //           {/* Title */}
// //           <div className="mb-6">
// //             <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Title
// //             </label>
// //             <input
// //               type="text"
// //               id="title"
// //               name="title"
// //               required
// //               value={formData.title}
// //               onChange={handleInputChange}
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             />
// //           </div>

// //           {/* Category */}
// //           <div className="mb-6">
// //             <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Category
// //             </label>
// //             <select
// //               id="category"
// //               name="category"
// //               required
// //               value={formData.category}
// //               onChange={handleInputChange}
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             >
// //               <option value="Premium Hens">Premium Hens</option>
// //               <option value="Baby Chickens">Baby Chickens</option>
// //               <option value="Wholesale">Wholesale</option>
// //             </select>
// //           </div>

// //           {/* Description */}
// //           <div className="mb-6">
// //             <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Description
// //             </label>
// //             <textarea
// //               id="description"
// //               name="description"
// //               required
// //               value={formData.description}
// //               onChange={handleInputChange}
// //               rows={4}
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             />
// //           </div>

// //           {/* Excerpt */}
// //           <div className="mb-6">
// //             <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Excerpt (Short Description)
// //             </label>
// //             <textarea
// //               id="excerpt"
// //               name="excerpt"
// //               value={formData.excerpt}
// //               onChange={handleInputChange}
// //               rows={2}
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             />
// //           </div>

// //           {/* Features */}
// //           <div className="mb-6">
// //             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Features</label>
// //             {formData.features.map((feature, index) => (
// //               <div key={index} className="flex gap-2 mb-2">
// //                 <input
// //                   type="text"
// //                   value={feature}
// //                   onChange={(e) => handleFeatureChange(index, e.target.value)}
// //                   placeholder={`Feature ${index + 1}`}
// //                   className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //                 />
// //                 <button
// //                   type="button"
// //                   onClick={() => removeFeature(index)}
// //                   className="px-3 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg"
// //                 >
// //                   Remove
// //                 </button>
// //               </div>
// //             ))}
// //             <button
// //               type="button"
// //               onClick={addFeature}
// //               className="mt-2 px-4 py-2 text-sm text-[#FF6B00] hover:bg-orange-50 dark:hover:bg-orange-900 rounded-lg"
// //             >
// //               + Add Feature
// //             </button>
// //           </div>

// //           {/* Price */}
// //           <div className="mb-6">
// //             <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Price
// //             </label>
// //             <input
// //               type="text"
// //               id="price"
// //               name="price"
// //               value={formData.price}
// //               onChange={handleInputChange}
// //               placeholder="Enter price or leave blank for 'Contact for Price'"
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             />
// //           </div>

// //           {/* Status */}
// //           <div className="mb-6">
// //             <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
// //               Status
// //             </label>
// //             <select
// //               id="status"
// //               name="status"
// //               required
// //               value={formData.status}
// //               onChange={handleInputChange}
// //               className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
// //             >
// //               <option value="active">Active</option>
// //               <option value="inactive">Inactive</option>
// //             </select>
// //           </div>

// //           {/* Image Upload */}
// //           <div className="mb-6">
// //             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Image</label>
// //             <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg">
// //               <div className="space-y-1 text-center">
// //                 {formData.image ? (
// //                   <div className="mb-4">
// //                     <img
// //                       src={formData.image}
// //                       alt="Preview"
// //                       className="mx-auto h-32 w-32 object-cover rounded-lg"
// //                     />
// //                   </div>
// //                 ) : (
// //                   <svg
// //                     className="mx-auto h-12 w-12 text-gray-400"
// //                     stroke="currentColor"
// //                     fill="none"
// //                     viewBox="0 0 48 48"
// //                   >
// //                     <path
// //                       d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
// //                       strokeWidth={2}
// //                       strokeLinecap="round"
// //                       strokeLinejoin="round"
// //                     />
// //                   </svg>
// //                 )}
// //                 <div className="flex text-sm text-gray-600 dark:text-gray-400">
// //                   <label
// //                     htmlFor="image-upload"
// //                     className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#FF6B00] hover:text-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-[#FF6B00]"
// //                   >
// //                     <span>Upload a file</span>
// //                     <input
// //                       id="image-upload"
// //                       name="image-upload"
// //                       type="file"
// //                       accept="image/*"
// //                       className="sr-only"
// //                       onChange={handleImageChange}
// //                     />
// //                   </label>
// //                   <p className="pl-1">or drag and drop</p>
// //                 </div>
// //                 <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 10MB</p>
// //               </div>
// //             </div>
// //           </div>

// //           {/* Submit Button */}
// //           <div className="flex justify-end">
// //             <button
// //               type="submit"
// //               disabled={loading}
// //               className={`px-6 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#FF6B00]/90 transition-colors ${
// //                 loading ? "opacity-50 cursor-not-allowed" : ""
// //               }`}
// //             >
// //               {loading ? "Updating..." : "Update Service"}
// //             </button>
// //           </div>
// //         </form>
// //       </div>
// //     </div>
// //   )
// // }

// // export default EditService
