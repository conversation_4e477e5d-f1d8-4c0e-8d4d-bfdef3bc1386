import express from "express";
import ContactUsController from "../controllers/contactUsController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/", ContactUsController.create);
router.get("/", authenticate, authorizeAdmin, ContactUsController.getAll);
router.get("/:id", authenticate, authorizeAdmin, ContactUsController.getById);
router.put("/:id", authenticate, authorizeAdmin, ContactUsController.update);
router.delete("/:id", authenticate, authorizeAdmin, ContactUsController.delete);

export default router;
