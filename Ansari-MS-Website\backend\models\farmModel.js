import pool from "../config/db.js";

const FarmModel = {
  createFarm: async (data) => {
    try {
      const { FName, F_Email, owner, F_Phone, F_Location, User_Id } = data;

      console.log('Creating farm with data:', data);

      // Parse location coordinates (format: "latitude longitude")
      const coords = F_Location.split(' ');
      if (coords.length !== 2) {
        throw new Error(`Invalid location format: ${F_Location}. Expected "latitude longitude"`);
      }

      const longitude = parseFloat(coords[1]);
      const latitude = parseFloat(coords[0]);

      if (isNaN(latitude) || isNaN(longitude)) {
        throw new Error(`Invalid coordinates: latitude=${latitude}, longitude=${longitude}`);
      }

      console.log(`Parsed coordinates: lat=${latitude}, lng=${longitude}`);

      const query = `
              INSERT INTO Farms (FName, F_Email, owner, F_Phone, F_Location, User_Id)
              VALUES (?, ?, ?, ?, ST_PointFromText(?), ?)
          `;

      const pointText = `POINT(${longitude} ${latitude})`;
      console.log('Point text:', pointText);

      const [result] = await pool.query(query, [
        FName,
        F_Email,
        owner,
        F_Phone,
        pointText, // MySQL POINT format: longitude latitude
        User_Id,
      ]);

      console.log('Farm created successfully with ID:', result.insertId);
      return { F_Id: result.insertId, ...data };
    } catch (error) {
      console.error('Error in createFarm:', error);
      throw error;
    }
  },

  getAllFarms: async () => {
    const [farms] = await pool.query(
      `SELECT F_Id, FName, F_Email, owner, F_Phone, ST_AsText(F_Location) AS F_Location, User_Id FROM Farms`,
    );
    return farms;
  },

  getFarmById: async (id) => {
    const [result] = await pool.query(
      `SELECT F_Id, FName, F_Email, owner, F_Phone, ST_AsText(F_Location) AS F_Location, User_Id FROM Farms WHERE F_Id = ?`,
      [id],
    );
    return result.length > 0 ? result[0] : null;
  },

  updateFarm: async (id, data) => {
    const { FName, F_Email, owner, F_Phone, F_Location, User_Id } = data;

    // Parse location coordinates (format: "latitude longitude")
    const coords = F_Location.split(' ');
    const longitude = parseFloat(coords[1]);
    const latitude = parseFloat(coords[0]);

    const query = `
            UPDATE Farms SET FName = ?, F_Email = ?, owner = ?, F_Phone = ?, F_Location = ST_PointFromText(?), User_Id = ?
            WHERE F_Id = ?
        `;
    const [result] = await pool.query(query, [
      FName,
      F_Email,
      owner,
      F_Phone,
      `POINT(${longitude} ${latitude})`, // MySQL POINT format: longitude latitude
      User_Id,
      id,
    ]);
    return result;
  },

  deleteFarm: async (id) => {
    const [result] = await pool.query(`DELETE FROM Farms WHERE F_Id = ?`, [id]);
    return result;
  },
  // FarmModel.js
  isEmailTaken: async (email) => {
    const [rows] = await pool.query(
      `SELECT F_Id FROM Farms WHERE F_Email = ?`,
      [email],
    );
    return rows.length > 0;
  },

  isPhoneTaken: async (phone) => {
    const [rows] = await pool.query(
      `SELECT F_Id FROM Farms WHERE F_Phone = ?`,
      [phone],
    );
    return rows.length > 0;
  },
};

export default FarmModel;
