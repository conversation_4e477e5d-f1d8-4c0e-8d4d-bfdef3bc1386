import * as React from 'react';
import { cn } from '../../lib/utils';
import { Link as RouterLink } from 'react-router-dom';

const Link = React.forwardRef(({ className, variant = 'default', ...props }, ref) => {
  const baseStyles =
    'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';

  const variants = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'underline-offset-4 hover:underline text-primary',
  };

  return <RouterLink ref={ref} className={cn(baseStyles, variants[variant], className)} {...props} />;
});
Link.displayName = 'Link';

export default Link;
