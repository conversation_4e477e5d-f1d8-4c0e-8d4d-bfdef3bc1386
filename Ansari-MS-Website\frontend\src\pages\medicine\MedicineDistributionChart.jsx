import { Bar } from 'react-chartjs-2';
import 'chart.js/auto';
import PropTypes from 'prop-types';

function MedicineDistributionChart({ medicineDistribution }) {
  const data = {
    labels: medicineDistribution.map((item) => item.farm),
    datasets: [
      {
        label: 'Medicine Distribution (%)',
        data: medicineDistribution.map((item) => item.percentage),
        backgroundColor: ['#FF6B00', '#4CAF50', '#FFEB3B', '#2196F3'],
        borderColor: '#000000',
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="p-4 bg-white rounded-md shadow-md">
      <h2 className="text-lg font-semibold mb-2">Medicine Distribution Graph</h2>
      <Bar data={data} />
    </div>
  );
}

MedicineDistributionChart.propTypes = {
  medicineDistribution: PropTypes.array.isRequired,
};

export default MedicineDistributionChart;
