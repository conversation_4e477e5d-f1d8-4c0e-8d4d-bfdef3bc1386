import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.jsx';

import {
  WebsiteLanguageProvider,
  i18n, // 👈 i18n comes from WebsiteLanguageContext now
} from './contexts/WebsiteLanguageContext';

import { I18nextProvider } from 'react-i18next';
import { AuthProvider } from './contexts/AuthContext';
import { ManagementProvider } from './contexts/ManagementContext';

const setDirection = (lng) => {
  document.documentElement.setAttribute('dir', lng === 'ps' ? 'rtl' : 'ltr');
  document.documentElement.setAttribute('lang', lng);
};

setDirection(i18n.language);

i18n.on('languageChanged', (lng) => {
  setDirection(lng);
});

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <WebsiteLanguageProvider>
      <I18nextProvider i18n={i18n}>
        <AuthProvider>
          <ManagementProvider>
            <App />
          </ManagementProvider>
        </AuthProvider>
      </I18nextProvider>
    </WebsiteLanguageProvider>
  </StrictMode>
);
