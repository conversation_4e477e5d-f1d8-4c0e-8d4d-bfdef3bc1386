import db from "../config/db.js";

const ArticleModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Articles (A_Title, A_Body, A_Image, u_Id, Excerpt, Category, Status, FeaturedNews)
   VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.A_Title,
        data.A_Body,
        data.A_Image,
        data.u_Id,
        data.Excerpt || null,
        data.Category,
        data.Status || "Published",
        data.FeaturedNews || false,
      ]
    );

    const [article] = await db.query(`SELECT * FROM Articles WHERE A_Id = ?`, [
      result.insertId,
    ]);
    return article[0];
  },

  getAll: async () => {
    const [rows] = await db.execute(`
    SELECT
      a.*,
      u.U_FirstName,
      u.U_LastName,
      a.A_Id as id,
      DATE_SUB(NOW(), INTERVAL (1000 - a.A_Id) DAY) as createdAt,
      DATE_SUB(NOW(), INTERVAL (1000 - a.A_Id) DAY) as updatedAt
    FROM Articles a
    JOIN users u ON a.u_Id = u.u_Id
    ORDER BY a.A_Id DESC
  `);
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute(
      `
    SELECT
      a.*,
      u.U_FirstName,
      u.U_LastName,
      a.A_Id as id,
      DATE_SUB(NOW(), INTERVAL (1000 - a.A_Id) DAY) as createdAt,
      DATE_SUB(NOW(), INTERVAL (1000 - a.A_Id) DAY) as updatedAt
    FROM Articles a
    JOIN users u ON a.u_Id = u.u_Id
    WHERE a.A_Id = ?
  `,
      [id]
    );

    return rows[0];
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Articles
   SET A_Title = ?, A_Body = ?, A_Image = ?, u_Id = ?, Excerpt = ?, Category = ?, Status = ?, FeaturedNews = ?
   WHERE A_Id = ?`,
      [
        data.A_Title,
        data.A_Body,
        data.A_Image,
        data.u_Id,
        data.Excerpt || null,
        data.Category,
        data.Status || "Published",
        data.FeaturedNews || false,
        id,
      ]
    );

    const [updated] = await db.query(`SELECT * FROM Articles WHERE A_Id = ?`, [
      id,
    ]);
    return updated[0];
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Articles WHERE A_Id = ?", [
      id,
    ]);
    return result;
  },
};

export default ArticleModel;
