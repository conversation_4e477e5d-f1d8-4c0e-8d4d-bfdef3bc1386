/* eslint-disable react/prop-types */
'use client';

import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

// Role-based protected route component
export const ProtectedRoute = ({ allowedRoles, redirectPath = '/signin', children }) => {
  const { isAuthenticated, isLoading, user } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to={redirectPath} replace />;
  }

  // If roles are specified and user's role is not included, redirect to appropriate page
  if (allowedRoles && !allowedRoles.includes(user?.role)) {
    // Redirect based on user role
    const roleRedirectMap = {
      admin: '/admin',
      farmer: '/farmer',
      shopper: '/shopper',
      user: '/',
    };

    const redirectTo = roleRedirectMap[user?.role] || '/';
    return <Navigate to={redirectTo} replace />;
  }

  // If there are children, render them, otherwise render the Outlet
  return children ? children : <Outlet />;
};

export default ProtectedRoute;
