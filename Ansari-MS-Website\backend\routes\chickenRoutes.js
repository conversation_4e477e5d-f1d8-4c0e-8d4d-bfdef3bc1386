import express from 'express';
import chickenController from '../controllers/chickenController.js';
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

// All routes require authentication and admin authorization
router.use(authenticate);
router.use(authorizeAdmin);

// Purchase routes
router.post('/purchases', chickenController.createPurchase);
router.get('/purchases', chickenController.getAllPurchases);
router.get('/purchases/:id', chickenController.getPurchaseById);
router.delete('/purchases/:id', chickenController.deletePurchase);

// Farm allocation routes
router.post('/allocations', chickenController.allocateToFarm);
router.get('/allocations', chickenController.getAllAllocations);
router.get('/allocations/:id', chickenController.getAllocationById);
router.delete('/allocations/:id', chickenController.deleteAllocation);

// Buyback routes
router.post('/buybacks', chickenController.buyBackFromFarm);
router.get('/buybacks', chickenController.getAllBuybacks);
router.get('/buybacks/:id', chickenController.getBuybackById);
router.delete('/buybacks/:id', chickenController.deleteBuyback);

// Shop distribution routes
router.post('/distributions', chickenController.distributeToShop);
router.get('/distributions', chickenController.getAllDistributions);
router.delete('/distributions/:id', chickenController.deleteDistribution);

// Statistics route
router.get('/statistics', chickenController.getStatistics);

// Process Report route
router.get('/process-report', chickenController.getProcessReport);

export default router;
