import express from "express";
import servicesController, {
  uploadUserPhoto,
  resizeUserPhoto,
} from "../controllers/servicesController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post(
  "/",
  authenticate,
  uploadUserPhoto,
  resizeUserPhoto,
  servicesController.create,
);
router.get("/", servicesController.getAll);
router.get("/:id", servicesController.getById);
router.put(
  "/:id",
  authenticate,
  uploadUserPhoto,
  resizeUserPhoto,
  servicesController.update,
);
router.delete("/:id", authenticate, authorizeAdmin, servicesController.delete);

export default router;
