import pool from "../config/db.js";
import { generateOTP } from "../utils/otp.js";
import crypto from "crypto";

const UserModel = {
  createUser: async (userData) => {
    const {
      U_FirstName,
      U_LastName,
      U_Email,
      address,
      image,
      Role,
      EmailVerified,
      password,
      status,
    } = userData;
    const query = `INSERT INTO users (U_FirstName, U_LastName, U_Email, address, image, Role, EmailVerified, password,status)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)`;
    const [result] = await pool.query(query, [
      U_FirstName,
      U_LastName,
      U_Email,
      address,
      image,
      Role,
      EmailVerified,
      password,
      status,
    ]);
    return { u_Id: result.insertId, ...userData }; // د نوي یوزر معلومات بیرته ورکوي
  },

  getUserById: async (id) => {
    const query = "SELECT * FROM users WHERE u_Id = ?";
    const [result] = await pool.query(query, [id]);
    return result.length > 0 ? result[0] : null;
  },

  getUserByEmail: async (email) => {
    const query = "SELECT * FROM users WHERE U_Email = ?";
    const [result] = await pool.query(query, [email]);
    return result.length > 0 ? result[0] : null;
  },

  getAllUsers: async () => {
    const [users] = await pool.query("SELECT * FROM users");
    return users;
  },

  updateUser: async (id, userData) => {
    // Get current user data first
    const currentUser = await UserModel.getUserById(id);
    if (!currentUser) {
      throw new Error("User not found");
    }

    // Merge with existing data, only updating provided fields
    const updateData = {
      U_FirstName: userData.U_FirstName || currentUser.U_FirstName,
      U_LastName:
        userData.U_LastName !== undefined
          ? userData.U_LastName
          : currentUser.U_LastName,
      U_Email: userData.U_Email || currentUser.U_Email,
      address:
        userData.address !== undefined ? userData.address : currentUser.address,
      phone:
        userData.phone !== undefined ? userData.phone : currentUser.phone,
      bio:
        userData.bio !== undefined ? userData.bio : currentUser.bio,
      birthDate:
        userData.birthDate !== undefined ? userData.birthDate : currentUser.birthDate,
      image: userData.image || currentUser.image,
      Role: userData.Role || currentUser.Role,
      EmailVerified:
        userData.EmailVerified !== undefined
          ? userData.EmailVerified
          : currentUser.EmailVerified,
      status: userData.status || currentUser.status,
    };

    let query = `UPDATE users
                 SET U_FirstName = ?, U_LastName = ?, U_Email = ?, address = ?, phone = ?, bio = ?, birthDate = ?, image = ?, Role = ?, EmailVerified = ?, status = ?, updatedAt = CURRENT_TIMESTAMP`;
    let params = [
      updateData.U_FirstName,
      updateData.U_LastName,
      updateData.U_Email,
      updateData.address,
      updateData.phone,
      updateData.bio,
      updateData.birthDate,
      updateData.image,
      updateData.Role,
      updateData.EmailVerified,
      updateData.status,
    ];

    // Add password to query if provided
    if (userData.password) {
      query += ", password = ?";
      params.push(userData.password);
    }

    query += " WHERE u_Id = ?";
    params.push(id);

    const [result] = await pool.query(query, params);
    return result;
  },

  deleteUser: async (id) => {
    // Start a transaction to ensure data consistency
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Delete related records first to avoid foreign key constraint errors

      // First, get all chat rooms where this user is involved
      const [chatRooms] = await connection.query(
        "SELECT room_id FROM chat_rooms WHERE user1_id = ? OR user2_id = ?",
        [id, id]
      );

      // Delete all messages from those chat rooms
      for (const room of chatRooms) {
        await connection.query("DELETE FROM chat_messages WHERE room_id = ?", [
          room.room_id,
        ]);
      }

      // Delete from chat_rooms where user is either user1_id or user2_id
      await connection.query(
        "DELETE FROM chat_rooms WHERE user1_id = ? OR user2_id = ?",
        [id, id]
      );

      // Delete from articles where user is the author
      await connection.query("DELETE FROM Articles WHERE u_Id = ?", [id]);

      // Delete from reviews if there's a user reference
      await connection.query("DELETE FROM reviews WHERE u_Id = ?", [id]);

      // Delete from any other tables that might reference this user
      // You can add more delete statements here for other tables

      // Finally delete the user
      const [result] = await connection.query(
        "DELETE FROM users WHERE u_Id = ?",
        [id]
      );

      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  setResetToken: async (email) => {
    const resetToken = crypto.randomBytes(32).toString("hex");
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");
    const expires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    const query = `UPDATE users SET passwordResetToken = ?, passwordResetExpires = ? WHERE U_Email = ?`;
    await pool.query(query, [hashedToken, expires, email]);

    return resetToken; // Send unhashed token via email
  },

  getUserByResetToken: async (hashedToken) => {
    const query = `SELECT * FROM users WHERE passwordResetToken = ? AND passwordResetExpires > NOW()`;
    const [result] = await pool.query(query, [hashedToken]);
    return result.length > 0 ? result[0] : null;
  },

  clearResetToken: async (id) => {
    const query = `UPDATE users SET passwordResetToken = NULL, passwordResetExpires = NULL WHERE u_Id = ?`;
    await pool.query(query, [id]);
  },

  ///// adding otp to project

  sendEmailVerificationOTP: async (email) => {
    const otp = generateOTP();
    const expires = new Date(Date.now() + 10 * 60 * 3000); // 3 minutes

    const query = `UPDATE users SET emailOTP = ?, emailOTPExpires = ? WHERE U_Email = ?`;
    await pool.query(query, [otp, expires, email]);

    return otp;
  },

  verifyEmailOTP: async (email, otp) => {
    const query = `SELECT * FROM users WHERE U_Email = ? AND emailOTP = ? AND emailOTPExpires > NOW()`;
    const [result] = await pool.query(query, [email, otp]);

    if (result.length === 0) return false;

    await pool.query(
      `UPDATE users SET EmailVerified = true, emailOTP = NULL, emailOTPExpires = NULL WHERE U_Email = ?`,
      [email]
    );

    return true;
  },
  getAllAdmins: async () => {
    const query = "SELECT * FROM users WHERE Role = ?";
    const [admins] = await pool.query(query, ["admin"]);
    return admins;
  },

  getUsersByRole: async (role) => {
    const query = "SELECT * FROM users WHERE Role = ? AND status = 'active'";
    const [users] = await pool.query(query, [role]);
    return users;
  },
};

export default UserModel;
