import React from 'react';

export const Table = ({ children, className = '' }) => {
  return (
    <div className="w-full overflow-x-auto">
      <table className={`w-full text-left border-collapse ${className}`}>{children}</table>
    </div>
  );
};

export const TableHead = ({ children }) => {
  return <thead className="bg-gray-100 dark:bg-gray-800">{children}</thead>;
};

export const TableHeader = ({ children, className = '' }) => {
  return (
    <th
      className={`px-4 py-3 text-sm font-semibold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 ${className}`}
    >
      {children}
    </th>
  );
};

export const TableBody = ({ children }) => {
  return <tbody>{children}</tbody>;
};

export const TableRow = ({ children, className = '' }) => {
  return <tr className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition ${className}`}>{children}</tr>;
};

export const TableCell = ({ children, className = '' }) => {
  return (
    <td
      className={`px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700 ${className}`}
    >
      {children}
    </td>
  );
};
