import <PERSON><PERSON> from "joi";
import bcrypt from "bcrypt";
import crypto from "crypto";
import jwt from "jsonwebtoken";
import _ from "lodash";
import multer from "multer";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { fileURLToPath } from "url";

import UserModel from "../models/userModel.js";
import asyncHandler from "../middlewares/asyncHandler.js";
import Email from "../utils/email.js";
import pool from "../config/db.js";
import { filterUserOutput } from "../utils/helpers.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Multer Storage & Filter
const multerStorage = multer.memoryStorage();
const multerFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image")) {
    cb(null, true);
  } else {
    cb(new Error("Not an image! Please upload only images."), false);
  }
};

const upload = multer({ storage: multerStorage, fileFilter: multerFilter });
const uploadUserPhoto = upload.single("image");

// Image Processing Middleware
const resizeUserPhoto = asyncHandler(async (req, res, next) => {
  if (!req.file) return next();

  const dir = path.join(__dirname, ".././public/images/users"); // Corrected path
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const filename = `user-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}.jpeg`;
  req.file.filename = filename;

  await sharp(req.file.buffer)
    .resize(500, 500)
    .toFormat("jpeg")
    .jpeg({ quality: 90 })
    .toFile(path.join(dir, filename));

  req.body.image = filename;
  next();
});

const UserController = {
  userSchema: Joi.object({
    U_FirstName: Joi.string().min(3).max(25).required(),
    U_LastName: Joi.string().min(3).max(25).optional(),
    U_Email: Joi.string().email().max(100).required(),
    address: Joi.string().max(255).default("kandahar"),
    image: Joi.string().allow(null, "").optional().default("default-user.jpg"),
    Role: Joi.string()
      .valid("user", "admin", "farmer", "shopper")
      .default("user"),
    EmailVerified: Joi.boolean().valid(true, false).default(false),
    password: Joi.string().min(6).required(),
    status: Joi.string().valid("active", "inactive").default("active"),
  }),
  userLoginSchema: Joi.object({
    U_Email: Joi.string().email().max(100).required(),
    password: Joi.string().min(6).required(),
  }),
  userUpdateSchema: Joi.object({
    U_FirstName: Joi.string().min(3).max(25).required(),
    U_LastName: Joi.string().min(3).max(25).optional().allow(""),
    U_Email: Joi.string().email().max(100).required(),
    address: Joi.string().max(255).optional().allow(""),
    image: Joi.string().allow(null, "").optional(),
    Role: Joi.string().valid("user", "admin", "farmer", "shopper").optional(),
    EmailVerified: Joi.boolean().valid(true, false).optional(),
    password: Joi.string().min(6).optional(),
    status: Joi.string().valid("active", "inactive").optional(),
  }),

  register: asyncHandler(async (req, res) => {
    const { error, value } = UserController.userSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const existingUser = await UserModel.getUserByEmail(value.U_Email);

    if (existingUser)
      return res.status(400).json({
        success: false,
        error: "User already exists or use another Email",
      });

    if (!req.body.image) {
      req.body.image = "default-user.jpg";
    }

    const userImage = req.file ? req.file.filename : "default-user.jpg";

    const hashedPassword = await bcrypt.hash(value.password, 10);

    const userData = {
      ...value,
      password: hashedPassword,
      image: userImage,
    };

    const newUser = await UserModel.createUser(userData);
    res.status(201).json({
      success: true,
      message: "User created successfully",
      user: filterUserOutput(newUser),
    });
  }),

  login: asyncHandler(async (req, res) => {
    const { error } = UserController.userLoginSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const { U_Email, password } = req.body;

    const user = await UserModel.getUserByEmail(U_Email);
    if (!user)
      return res
        .status(400)
        .json({ success: false, error: "Invalid email or password" });

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch)
      return res
        .status(400)
        .json({ success: false, error: "Invalid email or password" });

    // ✅ Check if email is verified
    if (!user.EmailVerified) {
      return res.status(403).json({
        success: false,
        error: "Your email is not verified. Please verify your email.",
      });
    }

    const token = jwt.sign(
      { userId: user.u_Id, email: user.U_Email, role: user.Role },
      process.env.JWT_SECRET,
      { expiresIn: "15d" }
    );

    res.json({
      success: true,
      message: "Login successful",
      user: filterUserOutput(user),
      token,
    });
  }),

  getUser: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const user = await UserModel.getUserById(id);
    if (!user)
      return res.status(404).json({ success: false, error: "User not found" });
    res.json({
      success: true,
      message: "User retrieved successfully",
      user: filterUserOutput(user),
    });
  }),

  getAllUsers: asyncHandler(async (req, res) => {
    const users = await UserModel.getAllUsers();
    res.json({
      success: true,
      message: "All users retrieved successfully",
      allUser: users.length,
      users: users.map((user) => filterUserOutput(user)),
    });
  }),

  updateUser: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const userData = req.body;

    const { error } = UserController.userUpdateSchema.validate(userData);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    // Handle image upload
    if (req.file) {
      userData.image = req.file.filename;
    }

    // Hash password if provided
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    const result = await UserModel.updateUser(id, userData);
    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, error: "User not found" });

    res.json({ success: true, message: "User updated successfully" });
  }),

  deleteUser: asyncHandler(async (req, res) => {
    const id = req.params.id;

    try {
      const result = await UserModel.deleteUser(id);
      if (result.affectedRows === 0)
        return res
          .status(404)
          .json({ success: false, error: "User not found" });
      res.json({ success: true, message: "User deleted successfully" });
    } catch (error) {
      console.error("Delete user error:", error);

      // Handle specific database errors
      if (error.code === "ER_ROW_IS_REFERENCED_2") {
        return res.status(400).json({
          success: false,
          error:
            "Cannot delete user: User has related data that must be removed first",
        });
      }

      return res.status(500).json({
        success: false,
        error: "An error occurred while deleting the user",
      });
    }
  }),
  forgotPassword: asyncHandler(async (req, res) => {
    const { email } = req.body;
    const user = await UserModel.getUserByEmail(email);
    if (!user) {
      return res
        .status(404)
        .json({ success: false, message: "User not found with this email" });
    }

    const resetToken = await UserModel.setResetToken(email);
    const resetURL = `http://localhost:3000/reset-password/${resetToken}`;

    try {
      await new Email(user, resetURL).sendPasswordReset();
      res
        .status(200)
        .json({ success: true, message: "Reset token sent to email" });
    } catch (err) {
      console.error(err);
      await UserModel.clearResetToken(user.u_Id);
      res.status(500).json({ success: false, message: "Failed to send email" });
    }
  }),

  resetPassword: asyncHandler(async (req, res) => {
    const hashedToken = crypto
      .createHash("sha256")
      .update(req.params.token)
      .digest("hex");
    const user = await UserModel.getUserByResetToken(hashedToken);

    if (!user) {
      return res
        .status(400)
        .json({ success: false, message: "Token is invalid or expired" });
    }

    const { password } = req.body;
    if (!password || password.length < 6) {
      return res.status(400).json({
        success: false,
        message: "Password must be at least 6 characters",
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const query = `UPDATE users SET password = ? WHERE u_Id = ?`;
    await pool.query(query, [hashedPassword, user.u_Id]);
    await UserModel.clearResetToken(user.u_Id);

    res
      .status(200)
      .json({ success: true, message: "Password reset successful" });
  }),

  changePassword: asyncHandler(async (req, res) => {
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const userId = req.user.u_Id;

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "All password fields are required",
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: "New password must be at least 6 characters",
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "New password and confirm password do not match",
      });
    }

    // Get current user
    const user = await UserModel.getUserById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update password in database
    const result = await UserModel.updateUser(userId, { password: hashedNewPassword });
    if (result.affectedRows === 0) {
      return res.status(500).json({
        success: false,
        message: "Failed to update password",
      });
    }

    res.status(200).json({
      success: true,
      message: "Password changed successfully",
    });
  }),

  verifyEmailSendOTP: asyncHandler(async (req, res) => {
    const { email } = req.body;
    const user = await UserModel.getUserByEmail(email);
    if (!user)
      return res.status(404).json({
        success: false,
        message: "User with that email address  not found",
      });
    if (user.EmailVerified)
      return res
        .status(400)
        .json({ success: false, message: "Email already verified" });

    const otp = await UserModel.sendEmailVerificationOTP(email);

    await new Email(user, null).sendOTPVerification(otp);

    res.json({ success: true, message: "OTP sent to email" });
  }),

  verifyEmailConfirmOTP: asyncHandler(async (req, res) => {
    const { email, otp } = req.body;
    const isVerified = await UserModel.verifyEmailOTP(email, otp);

    if (!isVerified) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid or expired OTP" });
    }

    res.json({ success: true, message: "Email verified successfully" });
  }),
  logout: asyncHandler(async (req, res) => {
    res.status(200).json({
      success: true,
      message: "User logged out successfully",
    });
  }),

  getAllAdmins: asyncHandler(async (req, res) => {
    const admins = await UserModel.getAllAdmins();
    res.status(200).json({
      success: true,
      message: "All admin users retrieved successfully",
      allAdmins: admins.length,
      data: admins.map((admin) => filterUserOutput(admin)),
    });
  }),

  getAllFarmers: asyncHandler(async (req, res) => {
    const farmers = await UserModel.getUsersByRole("farmer");
    res.status(200).json({
      success: true,
      message: "All farmers retrieved successfully",
      totalFarmers: farmers.length,
      data: farmers.map((farmer) => ({
        u_Id: farmer.u_Id,
        name: `${farmer.U_FirstName} ${farmer.U_LastName || ''}`.trim(),
        email: farmer.U_Email,
        firstName: farmer.U_FirstName,
        lastName: farmer.U_LastName,
      })),
    });
  }),

  getAllShoppers: asyncHandler(async (req, res) => {
    const shoppers = await UserModel.getUsersByRole("shopper");
    res.status(200).json({
      success: true,
      message: "All shoppers retrieved successfully",
      totalShoppers: shoppers.length,
      data: shoppers.map((shopper) => ({
        u_Id: shopper.u_Id,
        name: `${shopper.U_FirstName} ${shopper.U_LastName || ''}`.trim(),
        email: shopper.U_Email,
        firstName: shopper.U_FirstName,
        lastName: shopper.U_LastName,
      })),
    });
  }),

  // Get current user's profile
  getCurrentUserProfile: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id;
    const user = await UserModel.getUserById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "User not found"
      });
    }

    // Format the user data for frontend
    const profileData = {
      ...filterUserOutput(user),
      firstName: user.U_FirstName,
      lastName: user.U_LastName,
      email: user.U_Email,
      phone: user.phone || '',
      address: user.address || '',
      bio: user.bio || '',
      birthDate: user.birthDate ? (user.birthDate instanceof Date ? user.birthDate.toISOString().split('T')[0] : user.birthDate) : '',
      joinDate: user.createdAt ? user.createdAt.toISOString().split('T')[0] : '',
      image: user.image || 'default-user.jpg'
    };

    // // Debug log to see what we're getting
    // console.log('User birthDate from DB:', user.birthDate, typeof user.birthDate);
    // console.log('Formatted birthDate:', profileData.birthDate);

    res.json({
      success: true,
      message: "User profile retrieved successfully",
      user: profileData,
    });
  }),

  // Update current user's profile
  updateCurrentUserProfile: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id;
    const userData = req.body;

    // Create validation schema for profile update (excluding joinDate)
    const profileUpdateSchema = Joi.object({
      U_FirstName: Joi.string().min(3).max(25).required(),
      U_LastName: Joi.string().min(3).max(25).optional().allow(""),
      U_Email: Joi.string().email().max(100).required(),
      address: Joi.string().max(255).optional().allow(""),
      phone: Joi.string().max(20).optional().allow(""),
      bio: Joi.string().max(500).optional().allow(""),
      birthDate: Joi.date().optional().allow(""),
      image: Joi.string().allow(null, "").optional(),
    });

    const { error } = profileUpdateSchema.validate(userData);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message
      });
    }

    // Handle image upload
    if (req.file) {
      userData.image = req.file.filename;
    }

    // Remove joinDate from update data to prevent modification
    delete userData.joinDate;
    delete userData.createdAt;

    const result = await UserModel.updateUser(userId, userData);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: "User not found"
      });
    }

    res.json({
      success: true,
      message: "Profile updated successfully"
    });
  }),
};

export default UserController;
export { uploadUserPhoto, resizeUserPhoto };
