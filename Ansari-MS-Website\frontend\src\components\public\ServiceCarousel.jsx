import Slider from 'react-slick';
import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import Button from '@/components/button'; // adjust path as needed

export default function ServiceCarousel({ services }) {
  const settings = {
    dots: true,
    arrows: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 4000,
    speed: 600,
    slidesToShow: 1,
    slidesToScroll: 1,
    pauseOnHover: true,
    cssEase: 'ease-in-out',
  };

  return (
    <motion.div
      className="w-full px-4 md:px-10"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.35 }}
    >
      <Slider {...settings}>
        {services.map((item, index) => (
          <div key={index} className="p-4">
            <motion.div
              className="bg-white rounded-2xl border overflow-hidden flex flex-col h-[520px] w-full transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.35 }}
            >
              {/* Image Section */}
              <div className="relative w-full h-[50%] overflow-hidden group">
                <img
                  src={item.img || '/placeholder.svg'}
                  alt={item.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#2C3E50]/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Text Section */}
              <div className="p-6 flex flex-col flex-grow">
                <h4 className="text-lg md:text-xl font-bold font-['Poppins',sans-serif] text-[#333333] mb-3 group-hover:text-primary transition-colors duration-300">
                  {item.title}
                </h4>
                <p className="text-[#333333]/70 mb-4 flex-grow text-sm sm:text-base md:text-lg leading-relaxed">
                  {item.description}
                </p>
                <div className="flex items-center justify-center">
                  <Button variant="primary">
                    Learn More
                    <ChevronRight className="w-5 h-5 ml-2" />
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        ))}
      </Slider>
    </motion.div>
  );
}
