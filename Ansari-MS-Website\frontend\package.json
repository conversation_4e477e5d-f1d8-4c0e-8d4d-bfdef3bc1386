{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "analyze": "vite build --mode analyze"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.10", "@mui/x-data-grid": "^6.19.4", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@react-google-maps/api": "^2.19.2", "antd": "^5.24.7", "axios": "^1.6.7", "blueimp-md5": "^2.19.0", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "date-fns": "^4.1.0", "framer-motion": "^11.0.5", "i18next": "^23.8.2", "leaflet": "^1.9.4", "lucide-react": "^0.330.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-google-charts": "^5.2.1", "react-hot-toast": "^2.5.2", "react-i18next": "^14.0.5", "react-icons": "^5.0.1", "react-leaflet": "^4.2.1", "react-router-dom": "^7.5.1", "react-slick": "^0.30.2", "recharts": "^2.15.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.6", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.4.1", "vite": "^5.1.3"}}