-- Create Chicken Management Tables

-- 1. Chicken Purchases (Company buys from suppliers)
CREATE TABLE IF NOT EXISTS ChickenPurchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchaseDate DATE NOT NULL,
    quantity INT NOT NULL,
    pricePerChicken DECIMAL(10,2) NOT NULL,
    totalPrice DECIMAL(12,2) NOT NULL,
    supplierName VARCHAR(255) NOT NULL,
    supplierContact VARCHAR(100),
    notes TEXT,
    status ENUM('purchased', 'partially_allocated', 'fully_allocated') DEFAULT 'purchased',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Chicken Farm Allocations (Give chickens to farms for 45 days)
CREATE TABLE IF NOT EXISTS ChickenFarmAllocations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchaseId INT NOT NULL,
    farmId INT NOT NULL,
    allocationDate DATE NOT NULL,
    quantity INT NOT NULL,
    pricePerChicken DECIMAL(10,2) NOT NULL,
    totalPrice DECIMAL(12,2) NOT NULL,
    expectedReturnDate DATE NOT NULL,
    actualReturnDate DATE NULL,
    status ENUM('allocated', 'completed') DEFAULT 'allocated',
    notes TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (purchaseId) REFERENCES ChickenPurchases(id) ON DELETE CASCADE,
    FOREIGN KEY (farmId) REFERENCES farms(F_Id) ON DELETE CASCADE
);

-- 3. Chicken Buybacks (Company buys back from farms after 45 days)
CREATE TABLE IF NOT EXISTS ChickenBuybacks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    allocationId INT NOT NULL,
    farmId INT NOT NULL,
    buybackDate DATE NOT NULL,
    quantity INT NOT NULL,
    pricePerChicken DECIMAL(10,2) NOT NULL,
    totalPrice DECIMAL(12,2) NOT NULL,
    daysCompleted INT NOT NULL,
    status ENUM('bought_back', 'partially_distributed', 'fully_distributed') DEFAULT 'bought_back',
    notes TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (allocationId) REFERENCES ChickenFarmAllocations(id) ON DELETE CASCADE,
    FOREIGN KEY (farmId) REFERENCES farms(F_Id) ON DELETE CASCADE
);

-- 4. Chicken Shop Distributions (Distribute chickens to shops)
CREATE TABLE IF NOT EXISTS ChickenShopDistributions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    buybackId INT NOT NULL,
    shopId INT NOT NULL,
    distributionDate DATE NOT NULL,
    quantity INT NOT NULL,
    pricePerChicken DECIMAL(10,2) NOT NULL,
    totalPrice DECIMAL(12,2) NOT NULL,
    status ENUM('distributed', 'sold') DEFAULT 'distributed',
    notes TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (buybackId) REFERENCES ChickenBuybacks(id) ON DELETE CASCADE,
    FOREIGN KEY (shopId) REFERENCES shop(SId) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_chicken_purchases_date ON ChickenPurchases(purchaseDate);
CREATE INDEX idx_chicken_purchases_status ON ChickenPurchases(status);

CREATE INDEX idx_farm_allocations_date ON ChickenFarmAllocations(allocationDate);
CREATE INDEX idx_farm_allocations_status ON ChickenFarmAllocations(status);
CREATE INDEX idx_farm_allocations_farm ON ChickenFarmAllocations(farmId);

CREATE INDEX idx_buybacks_date ON ChickenBuybacks(buybackDate);
CREATE INDEX idx_buybacks_status ON ChickenBuybacks(status);
CREATE INDEX idx_buybacks_farm ON ChickenBuybacks(farmId);

CREATE INDEX idx_distributions_date ON ChickenShopDistributions(distributionDate);
CREATE INDEX idx_distributions_shop ON ChickenShopDistributions(shopId);

-- Insert sample data for testing (optional)
-- INSERT INTO ChickenPurchases (purchaseDate, quantity, pricePerChicken, totalPrice, supplierName, supplierContact, notes) 
-- VALUES 
-- ('2024-01-15', 1000, 50.00, 50000.00, 'ABC Poultry Farm', '+93701234567', 'High quality chickens'),
-- ('2024-01-20', 500, 55.00, 27500.00, 'XYZ Chicken Supplier', '+93709876543', 'Organic chickens');

DESCRIBE ChickenPurchases;
DESCRIBE ChickenFarmAllocations;
DESCRIBE ChickenBuybacks;
DESCRIBE ChickenShopDistributions;
