import React from 'react';
import { useClickOutside } from '../../hooks/useClickOutside';

const Table = React.forwardRef(({ children, className = '', ...props }, ref) => {
  const [activeDropdown, setActiveDropdown] = React.useState(null);
  const dropdownRef = useClickOutside(() => setActiveDropdown(null));

  const handleSort = (column) => {
    if (props.onSort) {
      const direction = props.sortColumn === column && props.sortDirection === 'asc' ? 'desc' : 'asc';
      props.onSort(column, direction);
    }
  };

  return (
    <div className="w-full overflow-x-auto">
      <table ref={ref} className={`min-w-full divide-y divide-gray-200 dark:divide-gray-700 ${className}`} {...props}>
        {children}
      </table>
    </div>
  );
});

Table.Header = ({ children, className = '', ...props }) => {
  return (
    <thead className={`bg-gray-50 dark:bg-gray-800 ${className}`} {...props}>
      {children}
    </thead>
  );
};

Table.Body = ({ children, className = '', ...props }) => {
  return (
    <tbody
      className={`bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700 ${className}`}
      {...props}
    >
      {children}
    </tbody>
  );
};

Table.Footer = ({ children, className = '', ...props }) => {
  return (
    <tfoot className={`bg-gray-50 dark:bg-gray-800 ${className}`} {...props}>
      {children}
    </tfoot>
  );
};

Table.Row = ({ children, className = '', ...props }) => {
  return (
    <tr className={`hover:bg-gray-50 dark:hover:bg-gray-800 ${className}`} {...props}>
      {children}
    </tr>
  );
};

Table.Cell = ({ children, className = '', ...props }) => {
  return (
    <td className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 ${className}`} {...props}>
      {children}
    </td>
  );
};

Table.HeaderCell = ({ children, className = '', ...props }) => {
  return (
    <th
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${className}`}
      {...props}
    >
      {children}
    </th>
  );
};

Table.displayName = 'Table';
Table.Header.displayName = 'Table.Header';
Table.Body.displayName = 'Table.Body';
Table.Footer.displayName = 'Table.Footer';
Table.Row.displayName = 'Table.Row';
Table.Cell.displayName = 'Table.Cell';
Table.HeaderCell.displayName = 'Table.HeaderCell';

Table.Empty = ({ message = 'No data available' }) => (
  <div className="text-center py-12">
    <p className="text-gray-500 dark:text-gray-400">{message}</p>
  </div>
);

Table.Loading = () => (
  <div className="animate-pulse">
    <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-t-lg" />
    {[...Array(5)].map((_, i) => (
      <div key={i} className="h-16 bg-gray-100 dark:bg-gray-800" />
    ))}
  </div>
);

export default Table;
