{"name": "backend", "version": "1.0.0", "description": "This is Our Final Year Project ", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongo-sanitize": "^1.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.13.0", "nodemailer": "^6.10.1", "sharp": "^0.33.5", "socket.io": "^4.8.1", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.1.9"}}