import db from "../config/db.js";

const ReviewsModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Reviews (R_Message, R_Number, u_Id) VALUES (?, ?, ?)`,
      [data.R_Message, data.R_Number, data.u_Id],
    );

    const [newReview] = await db.query(`SELECT * FROM Reviews WHERE R_Id = ?`, [
      result.insertId,
    ]);

    return newReview[0];
  },

  getAll: async () => {
    const [rows] = await db.execute(`
      SELECT r.*, u.U_FirstName, u.U_LastName, u.image
      FROM Reviews r
      JOIN users u ON r.u_Id = u.u_Id
      ORDER BY r.R_Id DESC
    `);
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute(
      `
      SELECT r.*, u.U_FirstName, u.U_LastName, u.image
      FROM Reviews r
      JOIN users u ON r.u_Id = u.u_Id
      WHERE r.R_Id = ?
    `,
      [id],
    );
    return rows[0];
  },

  getByUserId: async (u_Id) => {
    const [rows] = await db.execute("SELECT * FROM Reviews WHERE u_Id = ?", [
      u_Id,
    ]);
    return rows[0];
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Reviews SET R_Message = ?, R_Number = ? WHERE R_Id = ?`,
      [data.R_Message, data.R_Number, id],
    );
    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Reviews WHERE R_Id = ?", [
      id,
    ]);
    return result;
  },
};

export default ReviewsModel;
