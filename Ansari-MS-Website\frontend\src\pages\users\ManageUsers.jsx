



"use client"

import { useState, useEffect, useRef } from "react"
import { Link } from "react-router-dom"
import { Edit, Trash2, Search, UserPlus, Eye, MoreVertical } from "lucide-react"
import { useUsers } from "../../contexts/UsersContext"

const ManageUsers = () => {
  const { users, loading: apiLoading, error, fetchUsers, deleteUser } = useUsers()

  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeMenu, setActiveMenu] = useState(null)
  const [viewUser, setViewUser] = useState(null)
  const modalRef = useRef(null)
  const menuRefs = useRef({})
  const deleteModalRef = useRef(null)

  // Fetch users on component mount
  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true)
      await fetchUsers()
      setLoading(false)
    }

    loadUsers()
  }, [])

  // Handle clicks outside of any dropdown menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeMenu !== null) {
        const activeMenuRef = menuRefs.current[activeMenu]
        if (activeMenuRef && !activeMenuRef.contains(event.target)) {
          setActiveMenu(null)
        }
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle clicks outside of the view user modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setViewUser(null)
      }
    }

    if (viewUser) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (deleteModalRef.current && !deleteModalRef.current.contains(event.target)) {
        setIsDeleteModalOpen(false)
      }
    }

    if (isDeleteModalOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Filter users based on search term
  const filteredUsers = users.filter(
    (user) =>
      (user.U_FirstName + " " + user.U_LastName).toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.U_Email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.Role.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentItems = filteredUsers.slice(indexOfFirstItem, indexOfLastItem)
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)

  // Handle delete
  const openDeleteModal = (user) => {
    setUserToDelete(user)
    setIsDeleteModalOpen(true)
  }

  const confirmDelete = async () => {
    if (!userToDelete || !userToDelete.u_Id) return

    setLoading(true)
    try {
      const response = await deleteUser(userToDelete.u_Id)

      if (response && response.success) {
        // Successfully deleted, refresh the users list
        await fetchUsers()
        setIsDeleteModalOpen(false)
        setUserToDelete(null)
      } else {
        console.error("Failed to delete user:", response?.error || "Unknown error")
        alert("Failed to delete user: " + (response?.error || "Unknown error"))
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      alert("Error deleting user: " + error.message)
    } finally {
      setLoading(false)
    }
  }

  // Save reference to menu element
  const setMenuRef = (id, element) => {
    menuRefs.current[id] = element
  }

  if (loading || apiLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  if (error) {
    return <div className="bg-red-100 text-red-700 p-4 rounded-lg">Error loading users: {error}</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage Users</h1>
          <Link
            to="/admin/users/add"
            className="w-full sm:w-auto bg-primary text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
          >
            <UserPlus size={18} className="mr-2" />
            Add User
          </Link>
        </div>

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search users by name, email, or role..."
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="w-full">
            <table className="w-full table-fixed">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="w-2/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="w-1/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Role
                  </th>
                  <th className="w-1/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Status
                  </th>
                  <th className="w-1/5 px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {currentItems.map((user) => (
                  <tr key={user.u_Id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 sm:px-6 py-4">
                      <div className="flex items-center min-w-0">
                        <div className="h-10 w-10 flex-shrink-0">
                          <img
                            className="h-10 w-10 rounded-full object-cover"
                            src={
                              typeof user.image === "string"
                                ? `http://localhost:5432/public/images/users/${user.image}`
                                : "/placeholder.svg?height=40&width=40"
                            }
                            alt={`${user.U_FirstName} ${user.U_LastName}`}
                          />
                        </div>
                        <div className="ml-4 min-w-0">
                          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {user.U_FirstName} {user.U_LastName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{user.U_Email}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 sm:hidden">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                user.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                              }`}
                            >
                              {user.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                      <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{user.Role}</div>
                    </td>
                    <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.status === "active"
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                        }`}
                      >
                        {user.status}
                      </span>
                    </td>
                    <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
                      <div className="relative" ref={(element) => setMenuRef(user.u_Id || 0, element)}>
                        <button
                          onClick={() => setActiveMenu(activeMenu === user.u_Id ? null : user.u_Id || null)}
                          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                        >
                          <MoreVertical size={18} />
                        </button>
                        {activeMenu === user.u_Id && (
                          <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10">
                            <button
                              onClick={() => setViewUser(user)}
                              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                              <Eye size={16} />
                              View
                            </button>
                            <Link
                              to={`/admin/users/edit/${user.u_Id}`}
                              className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                              <Edit size={16} />
                              Edit
                            </Link>
                            <button
                              onClick={() => openDeleteModal(user)}
                              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                              <Trash2 size={16} className="mr-2" />
                              Delete
                            </button>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                <p>
                  Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{" "}
                  <span className="font-medium">
                    {indexOfLastItem > filteredUsers.length ? filteredUsers.length : indexOfLastItem}
                  </span>{" "}
                  of <span className="font-medium">{filteredUsers.length}</span> results
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded text-sm ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
                      : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded text-sm ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
                      : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {isDeleteModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div
              ref={deleteModalRef}
              className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full"
            >
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Are you sure you want to delete "{userToDelete?.U_FirstName} {userToDelete?.U_LastName}"? This action
                cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* View User Modal */}
        {viewUser && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div ref={modalRef} className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md shadow-lg">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">User Details</h2>
              <div className="flex items-center gap-4 mb-4">
                <img
                  src={
                    typeof viewUser.image === "string"
                      ? `http://localhost:5432/public/images/users/${viewUser.image}`
                      : "/placeholder.svg?height=64&width=64"
                  }
                  alt={`${viewUser.U_FirstName} ${viewUser.U_LastName}`}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <div className="text-lg font-medium text-gray-900 dark:text-white">
                    {viewUser.U_FirstName} {viewUser.U_LastName}
                  </div>
                  <div className="text-sm text-gray-500">{viewUser.U_Email}</div>
                </div>
              </div>
              <div className="text-sm text-gray-700 dark:text-gray-300 space-y-2">
                <div>
                  <strong>Role:</strong> {viewUser.Role}
                </div>
                <div>
                  <strong>Status:</strong> {viewUser.status}
                </div>
                <div>
                  <strong>Address:</strong> {viewUser.address}
                </div>
                <div>
                  <strong>Email Verified:</strong> {viewUser.EmailVerified ? "Yes" : "No"}
                </div>
              </div>
              <div className="mt-6 text-right">
                <button
                  onClick={() => setViewUser(null)}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ManageUsers





////////////////////////////

// "use client"

// import { useState, useEffect, useRef } from "react"
// import { Link } from "react-router-dom"
// import { Edit, Trash2, Search, UserPlus, Eye, MoreVertical } from "lucide-react"

// const ManageUsers = () => {
//   const [users, setUsers] = useState([])
//   const [searchTerm, setSearchTerm] = useState("")
//   const [currentPage, setCurrentPage] = useState(1)
//   const [itemsPerPage] = useState(10)
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
//   const [userToDelete, setUserToDelete] = useState(null)
//   const [loading, setLoading] = useState(true)
//   const [activeMenu, setActiveMenu] = useState(null)
//   const [viewUser, setViewUser] = useState(null)
//   const modalRef = useRef(null)
//   const menuRefs = useRef({})
//   const deleteModalRef = useRef(null)

//   // Handle clicks outside of any dropdown menu
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (activeMenu !== null) {
//         const activeMenuRef = menuRefs.current[activeMenu]
//         if (activeMenuRef && !activeMenuRef.contains(event.target)) {
//           setActiveMenu(null)
//         }
//       }
//     }

//     document.addEventListener("mousedown", handleClickOutside)
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside)
//     }
//   }, [activeMenu])

//   // Handle clicks outside of the view user modal
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (modalRef.current && !modalRef.current.contains(event.target)) {
//         setViewUser(null)
//       }
//     }

//     if (viewUser) {
//       document.addEventListener("mousedown", handleClickOutside)
//     }

//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside)
//     }
//   }, [viewUser])

//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (deleteModalRef.current && !deleteModalRef.current.contains(event.target)) {
//         setIsDeleteModalOpen(false)
//       }
//     }

//     if (isDeleteModalOpen) {
//       document.addEventListener("mousedown", handleClickOutside)
//     }

//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside)
//     }
//   }, [isDeleteModalOpen])

//   // Simulating data fetching
//   useEffect(() => {
//     // In a real app, you would fetch this data from your API
//     setTimeout(() => {
//       const mockUsers = [
//         {
//           id: 1,
//           name: "Wahidullah Ebrahimi",
//           email: "<EMAIL>",
//           role: "Admin",
//           status: "Active",
//           lastLogin: "2023-05-15 10:30 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 2,
//           name: "Jane Smith",
//           email: "<EMAIL>",
//           role: "Manager",
//           status: "Active",
//           lastLogin: "2023-05-14 09:15 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 3,
//           name: "Robert Johnson",
//           email: "<EMAIL>",
//           role: "Staff",
//           status: "Active",
//           lastLogin: "2023-05-13 02:45 PM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 4,
//           name: "Emily Davis",
//           email: "<EMAIL>",
//           role: "Staff",
//           status: "Inactive",
//           lastLogin: "2023-05-10 11:20 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 5,
//           name: "Michael Brown",
//           email: "<EMAIL>",
//           role: "Manager",
//           status: "Active",
//           lastLogin: "2023-05-15 08:50 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 6,
//           name: "Sarah Wilson",
//           email: "<EMAIL>",
//           role: "Staff",
//           status: "Active",
//           lastLogin: "2023-05-14 03:30 PM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 7,
//           name: "David Taylor",
//           email: "<EMAIL>",
//           role: "Staff",
//           status: "Inactive",
//           lastLogin: "2023-05-08 10:15 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//         {
//           id: 8,
//           name: "Lisa Anderson",
//           email: "<EMAIL>",
//           role: "Manager",
//           status: "Active",
//           lastLogin: "2023-05-15 09:45 AM",
//           avatar: "/placeholder.svg?height=40&width=40",
//         },
//       ]

//       setUsers(mockUsers)
//       setLoading(false)
//     }, 800)
//   }, [])

//   // Filter users based on search term
//   const filteredUsers = users.filter(
//     (user) =>
//       user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       user.role.toLowerCase().includes(searchTerm.toLowerCase()),
//   )

//   // Pagination
//   const indexOfLastItem = currentPage * itemsPerPage
//   const indexOfFirstItem = indexOfLastItem - itemsPerPage
//   const currentItems = filteredUsers.slice(indexOfFirstItem, indexOfLastItem)
//   const totalPages = Math.ceil(filteredUsers.length / itemsPerPage)

//   // Handle delete
//   const openDeleteModal = (user) => {
//     setUserToDelete(user)
//     setIsDeleteModalOpen(true)
//   }

//   const confirmDelete = () => {
//     // In a real app, you would call your API to delete the user
//     setUsers(users.filter((item) => item.id !== userToDelete.id))
//     setIsDeleteModalOpen(false)
//     setUserToDelete(null)
//   }

//   // Save reference to menu element
//   const setMenuRef = (id, element) => {
//     menuRefs.current[id] = element
//   }

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     )
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
//           <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage Users</h1>
//           <Link
//             to="/admin/users/add"
//             className="w-full sm:w-auto bg-primary text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
//           >
//             <UserPlus size={18} className="mr-2" />
//             Add User
//           </Link>
//         </div>

//         {/* Search and Filter */}
//         <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
//           <div className="relative">
//             <input
//               type="text"
//               placeholder="Search users by name, email, or role..."
//               className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//             />
//             <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
//           </div>
//         </div>

//         {/* Users Table */}
//         <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
//           <div className="w-full">
//             <table className="w-full table-fixed">
//               <thead className="bg-gray-50 dark:bg-gray-700">
//                 <tr>
//                   <th className="w-2/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     User
//                   </th>
//                   <th className="w-1/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Role
//                   </th>
//                   <th className="w-1/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Status
//                   </th>
//                   <th className="w-1/5 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
//                     Last Login
//                   </th>
//                   <th className="w-1/5 px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
//                 {currentItems.map((user) => (
//                   <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
//                     <td className="px-4 sm:px-6 py-4">
//                       <div className="flex items-center min-w-0">
//                         <div className="h-10 w-10 flex-shrink-0">
//                           <img
//                             className="h-10 w-10 rounded-full object-cover"
//                             src={user.avatar || "/placeholder.svg"}
//                             alt={user.name}
//                           />
//                         </div>
//                         <div className="ml-4 min-w-0">
//                           <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{user.name}</div>
//                           <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{user.email}</div>
//                           <div className="text-xs text-gray-500 dark:text-gray-400 sm:hidden">
//                             <span
//                               className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                                 user.status === "Active"
//                                   ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
//                                   : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
//                               }`}
//                             >
//                               {user.status}
//                             </span>
//                           </div>
//                         </div>
//                       </div>
//                     </td>
//                     <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                       <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{user.role}</div>
//                     </td>
//                     <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                       <span
//                         className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                           user.status === "Active"
//                             ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
//                             : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
//                         }`}
//                       >
//                         {user.status}
//                       </span>
//                     </td>
//                     <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
//                       <div className="truncate">{user.lastLogin}</div>
//                     </td>
//                     <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
//                       <div className="relative" ref={(element) => setMenuRef(user.id, element)}>
//                         <button
//                           onClick={() => setActiveMenu(activeMenu === user.id ? null : user.id)}
//                           className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
//                         >
//                           <MoreVertical size={18} />
//                         </button>
//                         {activeMenu === user.id && (
//                           <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10">
//                             <button
//                               onClick={() => setViewUser(user)}
//                               className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                             >
//                               <Eye size={16} />
//                               View
//                             </button>
//                             <Link
//                               to={`/admin/users/edit/:${user.id}`}
//                               className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                             >
//                               <Edit size={16} />
//                               Edit
//                             </Link>
//                             <button
//                               onClick={() => openDeleteModal(user)}
//                               className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
//                             >
//                               <Trash2 size={16} className="mr-2" />
//                               Delete
//                             </button>
//                           </div>
//                         )}
//                       </div>
//                     </td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>

//           {/* Pagination */}
//           {totalPages > 1 && (
//             <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
//               <div className="text-sm text-gray-700 dark:text-gray-300">
//                 <p>
//                   Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{" "}
//                   <span className="font-medium">
//                     {indexOfLastItem > filteredUsers.length ? filteredUsers.length : indexOfLastItem}
//                   </span>{" "}
//                   of <span className="font-medium">{filteredUsers.length}</span> results
//                 </p>
//               </div>
//               <div className="flex space-x-2">
//                 <button
//                   onClick={() => setCurrentPage(currentPage - 1)}
//                   disabled={currentPage === 1}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === 1
//                       ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
//                       : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
//                   }`}
//                 >
//                   Previous
//                 </button>
//                 <button
//                   onClick={() => setCurrentPage(currentPage + 1)}
//                   disabled={currentPage === totalPages}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === totalPages
//                       ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
//                       : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
//                   }`}
//                 >
//                   Next
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>

//         {/* Delete Confirmation Modal */}
//         {isDeleteModalOpen && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//             <div
//               ref={deleteModalRef}
//               className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full"
//             >
//               <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
//               <p className="mb-6 text-gray-600 dark:text-gray-300">
//                 Are you sure you want to delete "{userToDelete?.name}"? This action cannot be undone.
//               </p>
//               <div className="flex justify-end space-x-3">
//                 <button
//                   onClick={() => setIsDeleteModalOpen(false)}
//                   className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={confirmDelete}
//                   className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
//                 >
//                   Delete
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}

//         {/* View User Modal */}
//         {viewUser && (
//           <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
//             <div ref={modalRef} className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md shadow-lg">
//               <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">User Details</h2>
//               <div className="flex items-center gap-4 mb-4">
//                 <img
//                   src={viewUser.avatar || "/placeholder.svg"}
//                   alt={viewUser.name}
//                   className="w-16 h-16 rounded-full object-cover"
//                 />
//                 <div>
//                   <div className="text-lg font-medium text-gray-900 dark:text-white">{viewUser.name}</div>
//                   <div className="text-sm text-gray-500">{viewUser.email}</div>
//                 </div>
//               </div>
//               <div className="text-sm text-gray-700 dark:text-gray-300 space-y-2">
//                 <div>
//                   <strong>Role:</strong> {viewUser.role}
//                 </div>
//                 <div>
//                   <strong>Status:</strong> {viewUser.status}
//                 </div>
//                 <div>
//                   <strong>Last Login:</strong> {viewUser.lastLogin}
//                 </div>
//               </div>
//               <div className="mt-6 text-right">
//                 <button
//                   onClick={() => setViewUser(null)}
//                   className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded hover:bg-gray-300 dark:hover:bg-gray-600"
//                 >
//                   Close
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   )
// }

// export default ManageUsers


