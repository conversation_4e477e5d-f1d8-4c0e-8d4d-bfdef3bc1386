import Joi from "joi";
import FarmModel from "../models/farmModel.js";
import asyncHandler from "../middlewares/asyncHandler.js";

const FarmController = {
  farmSchema: Joi.object({
    FName: Joi.string().min(3).max(25).required(),
    F_Email: Joi.string().email().required(),
    owner: Joi.number().required(),
    F_Phone: Joi.string()
      .pattern(/^\+93[0-9]{9}$/)
      .required(),
    F_Location: Joi.string()
      .pattern(/^[-+]?[0-9]*\.?[0-9]+ [-+]?[0-9]*\.?[0-9]+$/)
      .required(),
    User_Id: Joi.number().required(),
  }),

  createFarm: asyncHandler(async (req, res) => {
    console.log('Received farm data:', req.body);

    const { error, value } = FarmController.farmSchema.validate(req.body);
    if (error) {
      console.log('Validation error:', error.details[0].message);
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    console.log('Validated data:', value);

    const isEmailTaken = await FarmModel.isEmailTaken(value.F_Email);
    if (isEmailTaken)
      return res.status(400).json({
        success: false,
        error: "Email already in use. Please use another email.",
      });

    const isPhoneTaken = await FarmModel.isPhoneTaken(value.F_Phone);
    if (isPhoneTaken)
      return res.status(400).json({
        success: false,
        error: "Phone number already in use. Please use another phone number.",
      });

    try {
      const newFarm = await FarmModel.createFarm(value);
      console.log('Farm created successfully:', newFarm);
      res.status(201).json({
        success: true,
        message: "Farm created successfully",
        farm: newFarm,
      });
    } catch (dbError) {
      console.error('Database error creating farm:', dbError);
      res.status(500).json({
        success: false,
        error: "Database error: " + dbError.message,
      });
    }
  }),

  getAllFarms: asyncHandler(async (req, res) => {
    const farms = await FarmModel.getAllFarms();
    res.json({
      success: true,
      totalFarms: farms.length,
      message: "Farms fetched successfully",
      farms,
    });
  }),

  getFarmById: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const farm = await FarmModel.getFarmById(id);
    if (!farm)
      return res.status(404).json({ success: false, error: "Farm not found" });
    res.json({ success: true, farm });
  }),

  updateFarm: asyncHandler(async (req, res) => {
    const { error, value } = FarmController.farmSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const id = req.params.id;
    const result = await FarmModel.updateFarm(id, value);
    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, error: "Farm not found" });

    res.json({ success: true, message: "Farm updated successfully" });
  }),

  deleteFarm: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const result = await FarmModel.deleteFarm(id);
    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, error: "Farm not found" });

    res.json({ success: true, message: "Farm deleted successfully" });
  }),
};

export default FarmController;
