/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';

const LanguageContext = createContext();

// Define translations outside the component to make them available for export
const translations = {
  en: {
    // Navigation
    dashboard: 'Dashboard',
    users: 'Users',
    news: 'News',
    services: 'Services',
    farms: 'Farms',
    feed: 'Feed',
    medicine: 'Medicine',
    inventory: 'Inventory',
    notifications: 'Notifications',
    reports: 'Reports',
    settings: 'Settings',

    // Languages
    english: 'English',
    pashto: 'Pashto',

    // Inventory
    inventory_management: 'Inventory Management',
    item_name: 'Item Name',
    category: 'Category',
    quantity: 'Quantity',
    batch_number: 'Batch Number',
    expiry_date: 'Expiry Date',
    actions: 'Actions',
    search_items: 'Search Items',
    add_item: 'Add Item',
    edit: 'Edit',
    delete: 'Delete',

    expired: 'Expired',
    expiring_soon: 'Expiring Soon',

    // Reports
    weekly_report: 'Weekly Report',
    monthly_report: 'Monthly Report',
    annual_report: 'Annual Report',
    generate_report: 'Generate Report',
    export_pdf: 'Export PDF',
    export_excel: 'Export Excel',
    revenue_vs_expenses: 'Revenue vs Expenses',
    sales_by_category: 'Sales by Category',
    monthly_comparison: 'Monthly Comparison',

    // Notifications
    new_order_alert: 'New order #{orderId} placed by {customer}',
low_stock_alert: 'Low stock for {item}: only {quantity} left',
expiry_alert: '{item} is expiring in {days} days',
new_user_alert: 'New user registered: {user}',
report_generated_alert: 'Report "{report}" (ID: {reportId}) generated successfully',

    clear_all: 'Clear All',
    clear_all_confirm: 'Are you sure you want to clear all notifications?',
    no_notifications: 'No Notifications',
    mark_read: 'Mark as Read',
    mark_all_read: 'Mark All as Read',
    new: 'New',
    archived: 'Archived',
    archive: 'Archive',
    unarchive: 'Unarchive',
    notification_settings: 'Notification Settings',
    notification_channels: 'Notification Channels',
    notification_categories: 'Notification Categories',
    email_notifications: 'Email Notifications',
    browser_notifications: 'Browser Notifications',
    sound_notifications: 'Sound Notifications',
    enabled: 'Enabled',
    disabled: 'Disabled',
    yes: 'Yes',
    no: 'No',

    // Categories
    system: 'System',
    orders: 'Orders',

    // Chicken Management
    chicken_management: 'Chicken Management',
    purchase: 'Purchase',
    sale: 'Sale',

    // Medicine Management
    medicine_management: 'Medicine Management',
    medicine_management_description: 'Manage medicines for poultry health',
    add_new_medicine: 'Add New Medicine',
    edit_medicine: 'Edit Medicine',
    medicine_information: 'Medicine Information',
    name: 'Name',
    type: 'Type',

    price: 'Price',
    supplier: 'Supplier',

    status: 'Status',
    description: 'Description',
    dosage: 'Dosage',
    administration: 'Administration',
    storage: 'Storage',
    search_medicines: 'Search medicines...',
    filter: 'Filter',
    medicine_status: 'Medicine Status',
    all: 'All',
    in_stock: 'In Stock',

    out_of_stock: 'Out of Stock',
    total_medicines: 'Total Medicines',
    needs_replenishment: 'Needs Replenishment',
    medicine_distribution: 'Medicine Distribution',
    medicine_list: 'Medicine List',

    // 'edit': 'Edit',
    // 'delete': 'Delete',
    no_medicines_found: 'No medicines found',
    confirm_delete_medicine: 'Are you sure you want to delete this medicine?',
    export: 'Export',
    import: 'Import',
    save_medicine: 'Save Medicine',
    saving: 'Saving...',
    back: 'Back',
    value: 'Value',

    // Medicine Types
    vaccine: 'Vaccine',
    antibiotic: 'Antibiotic',
    vitamin: 'Vitamin',
    supplement: 'Supplement',
    other: 'Other',

    // Administration Methods
    oral: 'Oral',
    injection: 'Injection',
    topical: 'Topical',

    // Storage Conditions
    room_temperature: 'Room Temperature',
    refrigerated: 'Refrigerated',
    frozen: 'Frozen',

    // Feed Management
    feed_management: 'Feed Management',
    feed_management_description: 'Manage poultry feed inventory and distribution',
    add_new_feed: 'Add New Feed',
    edit_feed: 'Edit Feed',
    feed_information: 'Feed Information',
    feed_list: 'Feed List',
    feed_distribution: 'Feed Distribution',
    total_feeds: 'Total Feeds',
    search_feeds: 'Search feeds...',
    feed_status: 'Feed Status',
    feed_type: 'Feed Type',
    feed_quantity: 'Feed Quantity',
    feed_price: 'Feed Price',
    feed_supplier: 'Feed Supplier',
    feed_batch_number: 'Batch Number',
    feed_expiry_date: 'Expiry Date',
    feed_description: 'Description',
    feed_composition: 'Composition',
    feed_protein_content: 'Protein Content (%)',
    feed_fat_content: 'Fat Content (%)',
    feed_storage: 'Storage Condition',
    feed_added_successfully: 'Feed added successfully',
    feed_updated_successfully: 'Feed updated successfully',
    feed_deleted_successfully: 'Feed deleted successfully',
    confirm_delete_feed: 'Are you sure you want to delete this feed?',
    no_feeds_found: 'No feeds found',

    // Feed Types
    starter: 'Starter',
    grower: 'Grower',
    finisher: 'Finisher',
    layer: 'Layer',
    broiler: 'Broiler',

    // Feed Storage Conditions

    medicine_not_found: 'Medicine not found!',
    invalid_medicine_id: 'Invalid medicine ID',
    loading: 'Loading medicine details...',
    no_medicines_loaded: 'No medicines data available. Please try again later.',
    back_to_medicines: 'Back to Medicines',

    // Farm Management
    farm_management: 'Farm Management',
    farm_management_description: 'Manage your poultry farms and their requirements',
    add_new_farm: 'Add New Farm',
    edit_farm: 'Edit Farm',
    farm_information: 'Farm Information',
    location: 'Location',

    capacity: 'Capacity',
    current_stock: 'Current Stock',
    last_inspection: 'Last Inspection',
    next_inspection: 'Next Inspection',

    notes: 'Notes',
    farm_status: 'Farm Status',
    total_farms: 'Total Farms',
    total_capacity: 'Total Capacity',
    total_birds: 'Total Birds',
    birds_in_stock: 'Birds in Stock',
    farm_capacity_distribution: 'Farm Capacity Distribution',
    farm_list: 'Farm List',
    search_farms: 'Search farms...',
    confirm_delete_farm: 'Are you sure you want to delete this farm?',
    no_farms_found: 'No farms found',
    active: 'Active',
    maintenance: 'Maintenance',
    inactive: 'Inactive',
    active_farms: 'Active Farms',
    name_required: 'Farm name is required',
    location_required: 'Location is required',
    type_required: 'Farm type is required',
    valid_capacity_required: 'Enter a valid capacity',
    farm_added_successfully: 'Farm added successfully',
    add_farm_error: 'Failed to add farm. Please try again.',

    save_farm: 'Save Farm',
    enter_farm_name: 'Enter farm name',
    enter_location: 'Enter location',
    enter_capacity: 'Enter capacity',
    additional_notes: 'Additional notes about the farm...',
  },
  ps: {
    // Navigation
    dashboard: 'ډشبورډ',
    users: 'کاروونکي',
    news: 'خبرونه',
    services: 'خدمتونه',
    farms: 'فارمونه',
    feed: 'خوراک',
    medicine: 'درمل',
    inventory: 'ذخیره',
    notifications: 'خبرتیاوې',
    reports: 'راپورونه',
    settings: 'تنظیمات',

    // Languages
    english: 'انکلیش',
    pashto: 'پښتو',

    // Inventory
    inventory_management: 'د ذخیرې مدیریت',
    item_name: 'د توکي نوم',
    category: 'کټګوري',
    quantity: 'مقدار',
    batch_number: 'د ګروپ شمیره',
    expiry_date: 'د پای نیټه',
    // 'actions': 'کړنې',
    search_items: 'توکي لټول',
    add_item: 'توکی اضافه کول',
    edit: 'سمول',
    delete: 'ړنګول',
    low_stock: 'د ذخیرې کمښت',
    expired: 'پای ته رسیدلی',
    expiring_soon: 'ژر پای ته رسي',

    // Reports
    weekly_report: 'د اونۍ راپور',
    monthly_report: 'د میاشتې راپور',
    annual_report: 'د کال راپور',
    generate_report: 'راپور جوړول',
    export_pdf: 'PDF صادرول',
    export_excel: 'Excel صادرول',
    revenue_vs_expenses: 'عاید او لګښت',
    sales_by_category: 'د کټګورۍ له مخې خرڅلاو',
    monthly_comparison: 'د میاشتو پرتله',

    // Notifications
    new_order: 'نوی امر',

    expiry_alert: 'د پای نیټه خبرتیا',
    new_user: 'نوی کارن',
    report_generated: 'راپور جوړ شو',
    clear_all: 'ټول پاکول',
    clear_all_confirm: 'ایا تاسو غواړئ ټولې خبرتیاوې پاک کړئ؟',
    no_notifications: 'هیڅ خبرتیا نشته',
    mark_read: 'لوستل شوي په نښه کول',
    mark_all_read: 'ټول لوستل شوي په نښه کول',
    new: 'نوی',
    archived: 'ارشیف شوی',
    archive: 'ارشیف کول',
    unarchive: 'د ارشیف نه ایستل',
    notification_settings: 'د خبرتیا تنظیمات',
    notification_channels: 'د خبرتیا چینلونه',
    notification_categories: 'د خبرتیا کټګورۍ',
    email_notifications: 'د بریښنالیک خبرتیاوې',
    browser_notifications: 'د براوزر خبرتیاوې',
    sound_notifications: 'د غږ خبرتیاوې',
    enabled: 'فعال',
    disabled: 'غیرفعال',
    yes: 'هو',
    no: 'نه',

    // Categories
    system: 'سیستم',

    orders: 'امرونه',

    // Chicken Management
    chicken_management: 'د چرګانو مدیریت',
    purchase: 'اخیستل',
    sale: 'خرڅول',

    // Medicine Management
    medicine_management: 'د درملو مدیریت',
    medicine_management_description: 'د چرګانو د روغتیا لپاره د درملو مدیریت',
    add_new_medicine: 'نوي درمل اضافه کړئ',
    edit_medicine: 'د درملو سمون',
    medicine_information: 'د درملو معلومات',
    name: 'نوم',
    type: 'ډول',

    price: 'قیمت',
    supplier: 'تولیدونکی',

    // 'batch_number': 'د بیچ شمیره',
    status: 'حالت',
    description: 'تفصیل',
    dosage: 'دوز',
    administration: 'د استعمال طریقه',
    storage: 'زخیره کول',
    search_medicines: 'د درملو لټون...',
    filter: 'فیلتر',
    medicine_status: 'د درملو حالت',
    all: 'ټول',
    in_stock: 'په زخیره کې',

    out_of_stock: 'زخیره نشته',
    total_medicines: 'ټول درمل',
    needs_replenishment: 'د تجدید اړتیا',
    medicine_distribution: 'د درملو توزیع',
    medicine_list: 'د درملو لیست',
    actions: 'عملیات',
    // 'edit': 'سمون',
    // 'delete': 'ړنګول',
    no_medicines_found: 'هیڅ درمل ونه موندل شول',
    confirm_delete_medicine: 'ایا تاسو غواړئ دا درمل حذف کړئ؟',
    export: 'صادرول',
    import: 'واردول',
    save_medicine: 'درمل خوندي کړئ',
    saving: 'ساتل کېږي...',
    back: 'بیرته',
    value: 'ارزښت',

    // Medicine Types
    vaccine: 'واکسین',
    antibiotic: 'انتی بیوتیک',
    vitamin: 'ویتامین',
    supplement: 'مکمل',
    other: 'نور',

    // Administration Methods
    oral: 'خولې',
    injection: 'انجکشن',
    topical: 'بشپړ',

    // Storage Conditions
    room_temperature: 'د کوټې تودوخه',

    // Feed Management
    feed_management: 'د غذايي موادو مدیریت',
    feed_management_description: 'د چرګانو د خواړو انبار او وېش مدیریت',
    add_new_feed: 'نوی خواړه اضافه کړئ',
    edit_feed: 'خواړه سم کړئ',
    feed_information: 'د خواړو معلومات',
    feed_list: 'د خواړو لیست',
    feed_distribution: 'د خواړو وېش',
    total_feeds: 'ټول خواړه',
    search_feeds: 'خواړه ولټوئ...',
    feed_status: 'د خواړو حالت',
    feed_type: 'د خواړو ډول',
    feed_quantity: 'د خواړو مقدار',
    feed_price: 'د خواړو قیمت',
    feed_supplier: 'د خواړو پلورونکی',
    feed_batch_number: 'د ګروپ شمیره',
    feed_expiry_date: 'د پای نیټه',
    feed_description: 'تفصیل',
    feed_composition: 'ترکیب',
    feed_protein_content: 'د پروټین مقدار (%)',
    feed_fat_content: 'د چربی مقدار (%)',
    feed_storage: 'د ساتنې حالت',
    feed_added_successfully: 'خواړه په بریالیتوب سره اضافه شول',
    feed_updated_successfully: 'خواړه په بریالیتوب سره تازه شول',
    feed_deleted_successfully: 'خواړه په بریالیتوب سره حذف شول',
    confirm_delete_feed: 'ایا تاسو غواړئ دا خواړه حذف کړئ؟',
    no_feeds_found: 'هیڅ خواړه ونه موندل شول',

    // Feed Types
    starter: 'لومړنی',
    grower: 'ودیدونکی',
    finisher: 'پای ته رسولونکی',
    layer: 'هګۍ ورکوونکی',
    broiler: 'غوښه ورکوونکی',

    // Feed Storage Conditions

    refrigerated: 'یخ شوی',
    frozen: 'کنګل شوی',

    medicine_not_found: 'دوا ونه موندل شوه!',
    invalid_medicine_id: 'د دوا ID ناسم دی',
    loading: 'د دوا توضیحات پورته کیږي...',
    no_medicines_loaded: 'د دوا معلومات شتون نلري. مهرباني وکړئ وروسته بیا هڅه وکړئ.',
    back_to_medicines: 'بیرته د درملو لیکست ته',

    // Farm Management
    farm_management: 'د فارم مدیریت',
    farm_management_description: 'خپل مرغۍ فارمونه او د هغوی اړتیاوې مدیریت کړئ',
    add_new_farm: 'نوی فارم اضافه کړئ',
    edit_farm: 'فارم سمون',
    farm_information: 'د فارم معلومات',
    location: 'ځای',

    capacity: 'ظرفیت',
    current_stock: 'اوسنی ذخیره',
    last_inspection: 'وروستی معاینه',
    next_inspection: 'راتلونکی معاینه',

    notes: 'یادښتونه',
    farm_status: 'د فارم حالت',
    total_farms: 'ټول فارمونه',
    total_capacity: 'ټول ظرفیت',
    total_birds: 'ټول مرغۍ',
    birds_in_stock: 'په ذخیره کې مرغۍ',
    farm_capacity_distribution: 'د فارم ظرفیت ویش',
    farm_list: 'د فارم لیست',
    search_farms: 'فارمونه لټوئ...',
    confirm_delete_farm: 'ایا تاسو غواړئ دا فارم حذف کړئ؟',
    no_farms_found: 'هیڅ فارم ونه موندل شو',
    active: 'فعال',
    maintenance: 'د ساتنې لاندې',
    inactive: 'غیر فعال',
    active_farms: 'فعال فارمونه',
    name_required: 'د فارم نوم اړین دی',
    location_required: 'ځای اړین دی',
    type_required: 'د فارم ډول اړین دی',
    valid_capacity_required: 'د معتبر ظرفیت ولیکي',
    farm_added_successfully: 'فارم په بریالیتوب سره اضافه شو',
    add_farm_error: 'د فارم اضافه کولو کې ناکامي. بیا هڅه وکړئ.',

    save_farm: 'فارم ساتل',
    enter_farm_name: 'د فارم نوم ولیکي',
    enter_location: 'ځای ولیکي',
    enter_capacity: 'ظرفیت ولیکي',
    additional_notes: 'د فارم په اړه اضافي یادښتونه...',
  },
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState(localStorage.getItem('localizeOfManagement') || 'en');

  useEffect(() => {
    localStorage.setItem('localizeOfManagement', language);
    document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
  }, [language]);

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ps' : 'en'));
  };

  // Add translation function
  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, toggleLanguage, translations, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);

// Export translations for use in other files
export { translations };
// /* eslint-disable react-refresh/only-export-components */
// /* eslint-disable react/prop-types */
// import { createContext, useState, useContext, useEffect } from 'react';

// const LanguageContext = createContext();

// // Define translations outside the component to make them available for export
// const translations = {
//   en: {
//     // Navigation
//     dashboard: 'Dashboard',
//     users: 'Users',
//     news: 'News',
//     services: 'Services',
//     farms: 'Farms',
//     feed: 'Feed',
//     medicine: 'Medicine',
//     inventory: 'Inventory',
//     notifications: 'Notifications',
//     reports: 'Reports',
//     settings: 'Settings',

//     // Inventory
//     inventory_management: 'Inventory Management',
//     item_name: 'Item Name',
//     category: 'Category',
//     quantity: 'Quantity',
//     batch_number: 'Batch Number',
//     expiry_date: 'Expiry Date',
//     actions: 'Actions',
//     search_items: 'Search Items',
//     add_item: 'Add Item',
//     edit: 'Edit',
//     delete: 'Delete',

//     expired: 'Expired',
//     expiring_soon: 'Expiring Soon',

//     // Reports
//     weekly_report: 'Weekly Report',
//     monthly_report: 'Monthly Report',
//     annual_report: 'Annual Report',
//     generate_report: 'Generate Report',
//     export_pdf: 'Export PDF',
//     export_excel: 'Export Excel',
//     revenue_vs_expenses: 'Revenue vs Expenses',
//     sales_by_category: 'Sales by Category',
//     monthly_comparison: 'Monthly Comparison',

//     // Notifications
//     new_order_alert: 'New order #{orderId} placed by {customer}',
// low_stock_alert: 'Low stock for {item}: only {quantity} left',
// expiry_alert: '{item} is expiring in {days} days',
// new_user_alert: 'New user registered: {user}',
// report_generated_alert: 'Report "{report}" (ID: {reportId}) generated successfully',

//     clear_all: 'Clear All',
//     clear_all_confirm: 'Are you sure you want to clear all notifications?',
//     no_notifications: 'No Notifications',
//     mark_read: 'Mark as Read',
//     mark_all_read: 'Mark All as Read',
//     new: 'New',
//     archived: 'Archived',
//     archive: 'Archive',
//     unarchive: 'Unarchive',
//     notification_settings: 'Notification Settings',
//     notification_channels: 'Notification Channels',
//     notification_categories: 'Notification Categories',
//     email_notifications: 'Email Notifications',
//     browser_notifications: 'Browser Notifications',
//     sound_notifications: 'Sound Notifications',
//     enabled: 'Enabled',
//     disabled: 'Disabled',
//     yes: 'Yes',
//     no: 'No',

//     // Categories
//     system: 'System',
//     orders: 'Orders',

//     // Chicken Management
//     chicken_management: 'Chicken Management',
//     purchase: 'Purchase',
//     sale: 'Sale',

//     // Medicine Management
//     medicine_management: 'Medicine Management',
//     medicine_management_description: 'Manage medicines for poultry health',
//     add_new_medicine: 'Add New Medicine',
//     edit_medicine: 'Edit Medicine',
//     medicine_information: 'Medicine Information',
//     name: 'Name',
//     type: 'Type',

//     price: 'Price',
//     supplier: 'Supplier',

//     status: 'Status',
//     description: 'Description',
//     dosage: 'Dosage',
//     administration: 'Administration',
//     storage: 'Storage',
//     search_medicines: 'Search medicines...',
//     filter: 'Filter',
//     medicine_status: 'Medicine Status',
//     all: 'All',
//     in_stock: 'In Stock',

//     out_of_stock: 'Out of Stock',
//     total_medicines: 'Total Medicines',
//     needs_replenishment: 'Needs Replenishment',
//     medicine_distribution: 'Medicine Distribution',
//     medicine_list: 'Medicine List',

//     // 'edit': 'Edit',
//     // 'delete': 'Delete',
//     no_medicines_found: 'No medicines found',
//     confirm_delete_medicine: 'Are you sure you want to delete this medicine?',
//     export: 'Export',
//     import: 'Import',
//     save_medicine: 'Save Medicine',
//     saving: 'Saving...',
//     back: 'Back',
//     value: 'Value',

//     // Medicine Types
//     vaccine: 'Vaccine',
//     antibiotic: 'Antibiotic',
//     vitamin: 'Vitamin',
//     supplement: 'Supplement',
//     other: 'Other',

//     // Administration Methods
//     oral: 'Oral',
//     injection: 'Injection',
//     topical: 'Topical',

//     // Storage Conditions
//     room_temperature: 'Room Temperature',
//     refrigerated: 'Refrigerated',
//     frozen: 'Frozen',

//     // Feed Management
//     feed_management: 'Feed Management',
//     feed_management_description: 'Manage poultry feed inventory and distribution',
//     add_new_feed: 'Add New Feed',
//     edit_feed: 'Edit Feed',
//     feed_information: 'Feed Information',
//     feed_list: 'Feed List',
//     feed_distribution: 'Feed Distribution',
//     total_feeds: 'Total Feeds',
//     search_feeds: 'Search feeds...',
//     feed_status: 'Feed Status',
//     feed_type: 'Feed Type',
//     feed_quantity: 'Feed Quantity',
//     feed_price: 'Feed Price',
//     feed_supplier: 'Feed Supplier',
//     feed_batch_number: 'Batch Number',
//     feed_expiry_date: 'Expiry Date',
//     feed_description: 'Description',
//     feed_composition: 'Composition',
//     feed_protein_content: 'Protein Content (%)',
//     feed_fat_content: 'Fat Content (%)',
//     feed_storage: 'Storage Condition',
//     feed_added_successfully: 'Feed added successfully',
//     feed_updated_successfully: 'Feed updated successfully',
//     feed_deleted_successfully: 'Feed deleted successfully',
//     confirm_delete_feed: 'Are you sure you want to delete this feed?',
//     no_feeds_found: 'No feeds found',

//     // Feed Types
//     starter: 'Starter',
//     grower: 'Grower',
//     finisher: 'Finisher',
//     layer: 'Layer',
//     broiler: 'Broiler',

//     // Feed Storage Conditions

//     medicine_not_found: 'Medicine not found!',
//     invalid_medicine_id: 'Invalid medicine ID',
//     loading: 'Loading medicine details...',
//     no_medicines_loaded: 'No medicines data available. Please try again later.',
//     back_to_medicines: 'Back to Medicines',

//     // Farm Management
//     farm_management: 'Farm Management',
//     farm_management_description: 'Manage your poultry farms and their requirements',
//     add_new_farm: 'Add New Farm',
//     edit_farm: 'Edit Farm',
//     farm_information: 'Farm Information',
//     location: 'Location',

//     capacity: 'Capacity',
//     current_stock: 'Current Stock',
//     last_inspection: 'Last Inspection',
//     next_inspection: 'Next Inspection',

//     notes: 'Notes',
//     farm_status: 'Farm Status',
//     total_farms: 'Total Farms',
//     total_capacity: 'Total Capacity',
//     total_birds: 'Total Birds',
//     birds_in_stock: 'Birds in Stock',
//     farm_capacity_distribution: 'Farm Capacity Distribution',
//     farm_list: 'Farm List',
//     search_farms: 'Search farms...',
//     confirm_delete_farm: 'Are you sure you want to delete this farm?',
//     no_farms_found: 'No farms found',
//     active: 'Active',
//     maintenance: 'Maintenance',
//     inactive: 'Inactive',
//     active_farms: 'Active Farms',
//     name_required: 'Farm name is required',
//     location_required: 'Location is required',
//     type_required: 'Farm type is required',
//     valid_capacity_required: 'Enter a valid capacity',
//     farm_added_successfully: 'Farm added successfully',
//     add_farm_error: 'Failed to add farm. Please try again.',

//     save_farm: 'Save Farm',
//     enter_farm_name: 'Enter farm name',
//     enter_location: 'Enter location',
//     enter_capacity: 'Enter capacity',
//     additional_notes: 'Additional notes about the farm...',

//     // Dashboard
//     quick_links: 'Quick Links',
//     statistics: 'Statistics',
//     recent_activities: 'Recent Activities',
//     system_alerts: 'System Alerts',
//     daily_report: 'Daily Report',
//     yearly_report: 'Yearly Report',
//     export_report: 'Export Report',
//     print_report: 'Print Report',
//     share_report: 'Share Report',
//     download_report: 'Download Report',
//     view_report: 'View Report',
//     previous: 'Previous',
//     next: 'Next',
//     search: 'Search',
//     filter: 'Filter',
//     all: 'All',
//     feeds: 'Feeds',
//     medicines: 'Medicines',
//     loading: 'Loading...',
//     success: 'Success!',
//     error: 'Error',
//     warning: 'Warning',
//     info: 'Info',

//     // News Management
//     manage_news: 'Manage News',
//     add_news: 'Add News',
//     edit_news: 'Edit News',
//     delete_news: 'Delete News',
//     view_news: 'View News',
//     news_title: 'Title',
//     news_category: 'Category',
//     news_author: 'Author',
//     news_date: 'Date',
//     news_status: 'Status',
//     news_actions: 'Actions',
//     confirm_delete_news: 'Are you sure you want to delete this news item?',
//     news_deleted_success: 'News deleted successfully',
//     error_loading_news: 'Error loading news. Please try again.',
//     error_viewing_news: 'Error viewing news item. Please try again.',
//     error_editing_news: 'Error editing news item. Please try again.',
//     error_deleting_news: 'Failed to delete news',

//     // Services Management
//     manage_services: 'Manage Services',
//     add_service: 'Add Service',
//     edit_service: 'Edit Service',
//     delete_service: 'Delete Service',
//     view_service: 'View Service',
//     service_title: 'Title',
//     service_description: 'Description',
//     service_category: 'Category',
//     service_price: 'Price',
//     service_status: 'Status',
//     service_actions: 'Actions',
//     confirm_delete_service: 'Are you sure you want to delete this service?',
//     service_deleted_success: 'Service deleted successfully',
//     error_loading_services: 'Error loading services. Please try again.',
//     error_viewing_service: 'Error viewing service. Please try again.',
//     error_editing_service: 'Error editing service. Please try again.',
//     error_deleting_service: 'Failed to delete service',
//     premium_hens: 'Premium Hens',
//     baby_chickens: 'Baby Chickens',
//     wholesale: 'Wholesale',

//     // Settings
//     system_settings: 'System Settings',
//     save_settings: 'Save Settings',
//     settings_saved: 'Settings have been saved successfully.',
//     general_settings: 'General Settings',
//     site_name: 'Site Name',
//     site_description: 'Site Description',
//     maintenance_mode: 'Enable Maintenance Mode',
//     contact_information: 'Contact Information',
//     contact_email: 'Contact Email',
//     contact_phone: 'Contact Phone',
//     address: 'Address',
//     social_media: 'Social Media',
//     facebook: 'Facebook',
//     twitter: 'Twitter',
//     instagram: 'Instagram',
//   },
//   ps: {
//     // Navigation
//     dashboard: 'ډشبورډ',
//     users: 'کاروونکي',
//     news: 'خبرونه',
//     services: 'خدمتونه',
//     farms: 'فارمونه',
//     feed: 'خوراک',
//     medicine: 'درمل',
//     inventory: 'ذخیره',
//     notifications: 'خبرتیاوې',
//     reports: 'راپورونه',
//     settings: 'تنظیمات',

//     // Inventory
//     inventory_management: 'د ذخیرې مدیریت',
//     item_name: 'د توکي نوم',
//     category: 'کټګوري',
//     quantity: 'مقدار',
//     batch_number: 'د ګروپ شمیره',
//     expiry_date: 'د پای نیټه',
//     // 'actions': 'کړنې',
//     search_items: 'توکي لټول',
//     add_item: 'توکی اضافه کول',
//     edit: 'سمول',
//     delete: 'ړنګول',
//     low_stock: 'د ذخیرې کمښت',
//     expired: 'پای ته رسیدلی',
//     expiring_soon: 'ژر پای ته رسي',

//     // Reports
//     weekly_report: 'د اونۍ راپور',
//     monthly_report: 'د میاشتې راپور',
//     annual_report: 'د کال راپور',
//     generate_report: 'راپور جوړول',
//     export_pdf: 'PDF صادرول',
//     export_excel: 'Excel صادرول',
//     revenue_vs_expenses: 'عاید او لګښت',
//     sales_by_category: 'د کټګورۍ له مخې خرڅلاو',
//     monthly_comparison: 'د میاشتو پرتله',

//     // Notifications
//     new_order: 'نوی امر',

//     expiry_alert: 'د پای نیټه خبرتیا',
//     new_user: 'نوی کارن',
//     report_generated: 'راپور جوړ شو',
//     clear_all: 'ټول پاکول',
//     clear_all_confirm: 'ایا تاسو غواړئ ټولې خبرتیاوې پاک کړئ؟',
//     no_notifications: 'هیڅ خبرتیا نشته',
//     mark_read: 'لوستل شوي په نښه کول',
//     mark_all_read: 'ټول لوستل شوي په نښه کول',
//     new: 'نوی',
//     archived: 'ارشیف شوی',
//     archive: 'ارشیف کول',
//     unarchive: 'د ارشیف نه ایستل',
//     notification_settings: 'د خبرتیا تنظیمات',
//     notification_channels: 'د خبرتیا چینلونه',
//     notification_categories: 'د خبرتیا کټګورۍ',
//     email_notifications: 'د بریښنالیک خبرتیاوې',
//     browser_notifications: 'د براوزر خبرتیاوې',
//     sound_notifications: 'د غږ خبرتیاوې',
//     enabled: 'فعال',
//     disabled: 'غیرفعال',
//     yes: 'هو',
//     no: 'نه',

//     // Categories
//     system: 'سیستم',

//     orders: 'امرونه',

//     // Chicken Management
//     chicken_management: 'د چرګانو مدیریت',
//     purchase: 'اخیستل',
//     sale: 'خرڅول',

//     // Medicine Management
//     medicine_management: 'د درملو مدیریت',
//     medicine_management_description: 'د چرګانو د روغتیا لپاره د درملو مدیریت',
//     add_new_medicine: 'نوي درمل اضافه کړئ',
//     edit_medicine: 'د درملو سمون',
//     medicine_information: 'د درملو معلومات',
//     name: 'نوم',
//     type: 'ډول',

//     price: 'قیمت',
//     supplier: 'تولیدونکی',

//     // 'batch_number': 'د بیچ شمیره',
//     status: 'حالت',
//     description: 'تفصیل',
//     dosage: 'دوز',
//     administration: 'د استعمال طریقه',
//     storage: 'زخیره کول',
//     search_medicines: 'د درملو لټون...',
//     filter: 'فیلتر',
//     medicine_status: 'د درملو حالت',
//     all: 'ټول',
//     in_stock: 'په زخیره کې',

//     out_of_stock: 'زخیره نشته',
//     total_medicines: 'ټول درمل',
//     needs_replenishment: 'د تجدید اړتیا',
//     medicine_distribution: 'د درملو توزیع',
//     medicine_list: 'د درملو لیست',
//     actions: 'عملیات',
//     // 'edit': 'سمون',
//     // 'delete': 'ړنګول',
//     no_medicines_found: 'هیڅ درمل ونه موندل شول',
//     confirm_delete_medicine: 'ایا تاسو غواړئ دا درمل حذف کړئ؟',
//     export: 'صادرول',
//     import: 'واردول',
//     save_medicine: 'درمل خوندي کړئ',
//     saving: 'ساتل کېږي...',
//     back: 'بیرته',
//     value: 'ارزښت',

//     // Medicine Types
//     vaccine: 'واکسین',
//     antibiotic: 'انتی بیوتیک',
//     vitamin: 'ویتامین',
//     supplement: 'مکمل',
//     other: 'نور',

//     // Administration Methods
//     oral: 'خولې',
//     injection: 'انجکشن',
//     topical: 'بشپړ',

//     // Storage Conditions
//     room_temperature: 'د کوټې تودوخه',

//     // Feed Management
//     feed_management: 'د غذايي موادو مدیریت',
//     feed_management_description: 'د چرګانو د خواړو انبار او وېش مدیریت',
//     add_new_feed: 'نوی خواړه اضافه کړئ',
//     edit_feed: 'خواړه سم کړئ',
//     feed_information: 'د خواړو معلومات',
//     feed_list: 'د خواړو لیست',
//     feed_distribution: 'د خواړو وېش',
//     total_feeds: 'ټول خواړه',
//     search_feeds: 'خواړه ولټوئ...',
//     feed_status: 'د خواړو حالت',
//     feed_type: 'د خواړو ډول',
//     feed_quantity: 'د خواړو مقدار',
//     feed_price: 'د خواړو قیمت',
//     feed_supplier: 'د خواړو پلورونکی',
//     feed_batch_number: 'د ګروپ شمیره',
//     feed_expiry_date: 'د پای نیټه',
//     feed_description: 'تفصیل',
//     feed_composition: 'ترکیب',
//     feed_protein_content: 'د پروټین مقدار (%)',
//     feed_fat_content: 'د چربی مقدار (%)',
//     feed_storage: 'د ساتنې حالت',
//     feed_added_successfully: 'خواړه په بریالیتوب سره اضافه شول',
//     feed_updated_successfully: 'خواړه په بریالیتوب سره تازه شول',
//     feed_deleted_successfully: 'خواړه په بریالیتوب سره حذف شول',
//     confirm_delete_feed: 'ایا تاسو غواړئ دا خواړه حذف کړئ؟',
//     no_feeds_found: 'هیڅ خواړه ونه موندل شول',

//     // Feed Types
//     starter: 'لومړنی',
//     grower: 'ودیدونکی',
//     finisher: 'پای ته رسولونکی',
//     layer: 'هګۍ ورکوونکی',
//     broiler: 'غوښه ورکوونکی',

//     // Feed Storage Conditions

//     refrigerated: 'یخ شوی',
//     frozen: 'کنګل شوی',

//     medicine_not_found: 'دوا ونه موندل شوه!',
//     invalid_medicine_id: 'د دوا ID ناسم دی',
//     loading: 'د دوا توضیحات پورته کیږي...',
//     no_medicines_loaded: 'د دوا معلومات شتون نلري. مهرباني وکړئ وروسته بیا هڅه وکړئ.',
//     back_to_medicines: 'بیرته د درملو لیکست ته',

//     // Farm Management
//     farm_management: 'د فارم مدیریت',
//     farm_management_description: 'خپل مرغۍ فارمونه او د هغوی اړتیاوې مدیریت کړئ',
//     add_new_farm: 'نوی فارم اضافه کړئ',
//     edit_farm: 'فارم سمون',
//     farm_information: 'د فارم معلومات',
//     location: 'ځای',

//     capacity: 'ظرفیت',
//     current_stock: 'اوسنی ذخیره',
//     last_inspection: 'وروستی معاینه',
//     next_inspection: 'راتلونکی معاینه',

//     notes: 'یادښتونه',
//     farm_status: 'د فارم حالت',
//     total_farms: 'ټول فارمونه',
//     total_capacity: 'ټول ظرفیت',
//     total_birds: 'ټول مرغۍ',
//     birds_in_stock: 'په ذخیره کې مرغۍ',
//     farm_capacity_distribution: 'د فارم ظرفیت ویش',
//     farm_list: 'د فارم لیست',
//     search_farms: 'فارمونه لټوئ...',
//     confirm_delete_farm: 'ایا تاسو غواړئ دا فارم حذف کړئ؟',
//     no_farms_found: 'هیڅ فارم ونه موندل شو',
//     active: 'فعال',
//     maintenance: 'د ساتنې لاندې',
//     inactive: 'غیر فعال',
//     active_farms: 'فعال فارمونه',
//     name_required: 'د فارم نوم اړین دی',
//     location_required: 'ځای اړین دی',
//     type_required: 'د فارم ډول اړین دی',
//     valid_capacity_required: 'د معتبر ظرفیت ولیکي',
//     farm_added_successfully: 'فارم په بریالیتوب سره اضافه شو',
//     add_farm_error: 'د فارم اضافه کولو کې ناکامي. بیا هڅه وکړئ.',

//     save_farm: 'فارم ساتل',
//     enter_farm_name: 'د فارم نوم ولیکي',
//     enter_location: 'ځای ولیکي',
//     enter_capacity: 'ظرفیت ولیکي',
//     additional_notes: 'د فارم په اړه اضافي یادښتونه...',

//     // Dashboard
//     quick_links: 'ژر لینکونه',
//     statistics: 'احصایې',
//     recent_activities: 'نوي فعالیتونه',
//     system_alerts: 'د سیسټم خبرتیاوې',
//     daily_report: 'د ورځې راپور',
//     yearly_report: 'د کال راپور',
//     export_report: 'راپور صادرول',
//     print_report: 'راپور چاپول',
//     share_report: 'راپور شریکول',
//     download_report: 'راپور ډاونلوډ کول',
//     view_report: 'راپور وګورئ',
//     previous: 'مخکینی',
//     next: 'راتلونکی',
//     search: 'لټون',
//     filter: 'فلټر',
//     all: 'ټول',
//     feeds: 'خوراک',
//     medicines: 'درمل',
//     loading: 'پورته کیږي...',
//     success: 'بریالیتوب!',
//     error: 'تېروتنه',
//     warning: 'خبرداری',
//     info: 'معلومات',

//     // News Management
//     manage_news: 'د خبرونو مدیریت',
//     add_news: 'خبر اضافه کول',
//     edit_news: 'خبر سمول',
//     delete_news: 'خبر ړنګول',
//     view_news: 'خبر وګورئ',
//     news_title: 'عنوان',
//     news_category: 'کټګوري',
//     news_author: 'لیکوال',
//     news_date: 'نیټه',
//     news_status: 'حالت',
//     news_actions: 'عملیات',
//     confirm_delete_news: 'ایا تاسو غواړئ دا خبر ړنګ کړئ؟',
//     news_deleted_success: 'خبر په بریالیتوب سره ړنګ شو',
//     error_loading_news: 'د خبرونو پورته کولو کې تېروتنه. بیا هڅه وکړئ.',
//     error_viewing_news: 'د خبر لیدلو کې تېروتنه. بیا هڅه وکړئ.',
//     error_editing_news: 'د خبر سمولو کې تېروتنه. بیا هڅه وکړئ.',
//     error_deleting_news: 'د خبر ړنګولو کې ناکامي',

//     // Services Management
//     manage_services: 'د خدمتونو مدیریت',
//     add_service: 'خدمت اضافه کول',
//     edit_service: 'خدمت سمول',
//     delete_service: 'خدمت ړنګول',
//     view_service: 'خدمت وګورئ',
//     service_title: 'عنوان',
//     service_description: 'تفصیل',
//     service_category: 'کټګوري',
//     service_price: 'قیمت',
//     service_status: 'حالت',
//     service_actions: 'عملیات',
//     confirm_delete_service: 'ایا تاسو غواړئ دا خدمت ړنګ کړئ؟',
//     service_deleted_success: 'خدمت په بریالیتوب سره ړنګ شو',
//     error_loading_services: 'د خدمتونو پورته کولو کې تېروتنه. بیا هڅه وکړئ.',
//     error_viewing_service: 'د خدمت لیدلو کې تېروتنه. بیا هڅه وکړئ.',
//     error_editing_service: 'د خدمت سمولو کې تېروتنه. بیا هڅه وکړئ.',
//     error_deleting_service: 'د خدمت ړنګولو کې ناکامي',
//     premium_hens: 'غوره چرګې',
//     baby_chickens: 'کوچني چرګان',
//     wholesale: 'خپلواک پلور',

//     // Settings
//     system_settings: 'د سیسټم تنظیمات',
//     save_settings: 'تنظیمات ساتل',
//     settings_saved: 'تنظیمات په بریالیتوب سره ساتل شول.',
//     general_settings: 'عمومي تنظیمات',
//     site_name: 'د سایټ نوم',
//     site_description: 'د سایټ تفصیل',
//     maintenance_mode: 'د ساتنې حالت فعالول',
//     contact_information: 'د اړیکې معلومات',
//     contact_email: 'د اړیکې بریښنالیک',
//     contact_phone: 'د اړیکې تلیفون',
//     address: 'پته',
//     social_media: 'ټولنیز رسنۍ',
//     facebook: 'فیسبوک',
//     twitter: 'ټویټر',
//     instagram: 'انسټاګرام',
//   },
// };

// export const LanguageProvider = ({ children }) => {
//   const [language, setLanguage] = useState(localStorage.getItem('localizeOfManagement') || 'en');

//   useEffect(() => {
//     localStorage.setItem('localizeOfManagement', language);
//     document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
//   }, [language]);

//   const toggleLanguage = () => {
//     setLanguage((prev) => (prev === 'en' ? 'ps' : 'en'));
//   };

//   // Add translation function
//   const t = (key) => {
//     return translations[language][key] || key;
//   };

//   return (
//     <LanguageContext.Provider value={{ language, setLanguage, toggleLanguage, t }}>
//       {children}
//     </LanguageContext.Provider>
//   );
// };

// export const useLanguage = () => useContext(LanguageContext);

// // Export translations for use in other files
// export { translations };
