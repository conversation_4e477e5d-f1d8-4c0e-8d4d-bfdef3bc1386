// middlewares/chatImageMiddleware.js
import multer from "multer";
import sharp from "sharp";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import asyncHandler from "./asyncHandler.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Multer Setup
const multerStorage = multer.memoryStorage();
const multerFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image")) cb(null, true);
  else cb(new Error("Not an image! Please upload only images."), false);
};

const upload = multer({ storage: multerStorage, fileFilter: multerFilter });

export const uploadChatImage = upload.single("image");

// Resize Middleware
export const resizeChatImage = asyncHandler(async (req, res, next) => {
  if (!req.file) return next();

  const dir = path.join(__dirname, "../public/images/chats");
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });

  const filename = `chat-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}.jpeg`;
  req.file.filename = filename;

  await sharp(req.file.buffer)
    .resize(600, 600)
    .toFormat("jpeg")
    .jpeg({ quality: 90 })
    .toFile(path.join(dir, filename));

  // Only set type to 'image' if not provided (e.g. if no text came)
  if (!req.body.message_type || req.body.message_type === "image") {
    req.body.message_type = "image";
  }

  req.body.image_url = filename;
  next();
});
