import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ShoppingCart,
  Truck,
  RotateCcw,
  Store,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3
} from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Link from '../../components/feed-components/Link';

const ChickenDashboard = () => {
  const navigate = useNavigate();
  const { statistics, fetchStatistics } = useChicken();
  const { language, translations } = useLanguage();

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  const quickActions = [
    {
      title: t('purchase_chickens') || 'Purchase Chickens',
      description: t('buy_chickens_from_suppliers') || 'Buy chickens from suppliers',
      icon: <ShoppingCart className="h-8 w-8" />,
      link: '/admin/chickens/purchases/add',
      color: 'bg-blue-500',
      hoverColor: 'hover:bg-blue-600',
    },
    {
      title: t('allocate_to_farm') || 'Allocate to Farm',
      description: t('send_chickens_to_farms') || 'Send chickens to farms for 45 days',
      icon: <Truck className="h-8 w-8" />,
      link: '/admin/chickens/allocations/add',
      color: 'bg-green-500',
      hoverColor: 'hover:bg-green-600',
    },
    {
      title: t('buyback_from_farm') || 'Buyback from Farm',
      description: t('buy_chickens_back_from_farms') || 'Buy chickens back from farms',
      icon: <RotateCcw className="h-8 w-8" />,
      link: '/admin/chickens/buybacks/add',
      color: 'bg-orange-500',
      hoverColor: 'hover:bg-orange-600',
    },
    {
      title: t('distribute_to_shops') || 'Distribute to Shops',
      description: t('send_chickens_to_shops') || 'Send chickens to shops for sale',
      icon: <Store className="h-8 w-8" />,
      link: '/admin/chickens/distributions/add',
      color: 'bg-purple-500',
      hoverColor: 'hover:bg-purple-600',
    },
  ];

  const managementSections = [
    {
      title: t('purchases_management') || 'Purchases Management',
      description: t('view_all_chicken_purchases') || 'View and manage all chicken purchases',
      link: '/admin/chickens/purchases',
      icon: <ShoppingCart className="h-6 w-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: t('farm_allocations') || 'Farm Allocations',
      description: t('manage_farm_allocations') || 'Manage chickens allocated to farms',
      link: '/admin/chickens/allocations',
      icon: <Truck className="h-6 w-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: t('buybacks_management') || 'Buybacks Management',
      description: t('manage_farm_buybacks') || 'Manage chickens bought back from farms',
      link: '/admin/chickens/buybacks',
      icon: <RotateCcw className="h-6 w-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: t('shop_distributions') || 'Shop Distributions',
      description: t('manage_shop_distributions') || 'Manage chickens distributed to shops',
      link: '/admin/chickens/distributions',
      icon: <Store className="h-6 w-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: t('process_reports') || 'Process Reports',
      description: t('view_complete_lifecycle_profit_loss') || 'View complete lifecycle analysis with profit/loss tracking',
      link: '/admin/chickens/reports',
      icon: <BarChart3 className="h-6 w-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('chicken_management') || 'Chicken Management'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_chicken_lifecycle') || 'Manage the complete chicken lifecycle from purchase to sale'}
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_purchases') || 'Total Purchases'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {statistics.totalPurchases || 0}
                </h3>
                <p className="text-xs text-blue-600">
                  {statistics.totalChickensPurchased || 0} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <ShoppingCart className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('active_allocations') || 'Active Allocations'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {statistics.activeAllocations || 0}
                </h3>
                <p className="text-xs text-green-600">
                  {statistics.readyForBuyback || 0} {t('ready_for_buyback') || 'ready for buyback'}
                </p>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Truck className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_buybacks') || 'Total Buybacks'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {statistics.totalBuybacks || 0}
                </h3>
                <p className="text-xs text-orange-600">
                  {statistics.totalChickensBoughtBack || 0} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <RotateCcw className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-500/5 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_distributions') || 'Total Distributions'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {statistics.totalDistributions || 0}
                </h3>
                <p className="text-xs text-purple-600">
                  {statistics.totalChickensDistributed || 0} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-purple-500/10 rounded-full">
                <Store className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {t('quick_actions') || 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link
                key={index}
                to={action.link}
                className={`p-6 rounded-lg ${action.color} ${action.hoverColor} text-white transition-all duration-200 transform hover:scale-105 hover:shadow-lg`}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  {action.icon}
                  <h3 className="font-semibold text-lg">{action.title}</h3>
                  <p className="text-sm opacity-90">{action.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Management Sections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {t('management_sections') || 'Management Sections'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {managementSections.map((section, index) => (
              <Link
                key={index}
                to={section.link}
                className={`p-6 rounded-lg border-2 border-gray-200 hover:border-gray-300 ${section.bgColor} transition-all duration-200 hover:shadow-md`}
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${section.bgColor}`}>
                    <div className={section.color}>
                      {section.icon}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                      {section.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {section.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Process Flow Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            {t('chicken_lifecycle_process') || 'Chicken Lifecycle Process'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="font-bold">1</span>
              </div>
              <h4 className="font-semibold text-blue-900 mb-2">
                {t('purchase') || 'Purchase'}
              </h4>
              <p className="text-sm text-blue-700">
                {t('buy_chickens_from_suppliers') || 'Buy chickens from suppliers'}
              </p>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="w-12 h-12 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="font-bold">2</span>
              </div>
              <h4 className="font-semibold text-green-900 mb-2">
                {t('allocate') || 'Allocate'}
              </h4>
              <p className="text-sm text-green-700">
                {t('send_to_farms_45_days') || 'Send to farms for 45 days'}
              </p>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="font-bold">3</span>
              </div>
              <h4 className="font-semibold text-orange-900 mb-2">
                {t('buyback') || 'Buyback'}
              </h4>
              <p className="text-sm text-orange-700">
                {t('buy_back_after_45_days') || 'Buy back after 45 days'}
              </p>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="w-12 h-12 bg-purple-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="font-bold">4</span>
              </div>
              <h4 className="font-semibold text-purple-900 mb-2">
                {t('distribute') || 'Distribute'}
              </h4>
              <p className="text-sm text-purple-700">
                {t('send_to_shops_for_sale') || 'Send to shops for sale'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChickenDashboard;
