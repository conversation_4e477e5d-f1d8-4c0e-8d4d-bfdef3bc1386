/* eslint-disable no-undef */
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './index.html',
    './src/**/*.{js,jsx,ts,tsx}',
    '*.{js,ts,jsx,tsx,mdx}',
    'app/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',

        // Primary Colors (Main Branding)
        primary: {
          DEFAULT: '#FF6B00', // Orange - for buttons & highlights
          foreground: '#FFFFFF', // White text on orange background
        },

        // Secondary Colors
        secondary: {
          DEFAULT: '#2C3E50', // Dark Blue - for navbar, headers
          foreground: '#FFFFFF', // White text on dark blue background
        },

        // Destructive/Accent (Red)
        destructive: {
          DEFAULT: '#D32F2F', // Red - for urgency, discounts, notifications
          foreground: '#FFFFFF', // White text on red background
        },

        // Success (Green)
        success: '#388E3C', // Green - for freshness & success messages

        // Muted (Light Gray)
        muted: {
          DEFAULT: '#F5F5F5', // Light Gray - for soft background sections
          foreground: '#333333', // Dark text on light gray background
        },

        // Accent (can reuse primary for consistency)
        accent: {
          DEFAULT: '#FF6B00', // Orange - same as primary for consistency
          foreground: '#FFFFFF', // White text on orange background
        },

        // Popover
        popover: {
          DEFAULT: '#FFFFFF', // White background for popovers
          foreground: '#333333', // Dark text on white background
        },

        // Card
        card: {
          DEFAULT: '#FFFFFF', // White background for cards
          foreground: '#333333', // Dark text on white background
        },

        // Additional custom colors
        'primary-hover': '#E05A00', // Darker orange for hover states
        textprimary: '#333333', // Dark Gray - for text contrast
        textSecondary: '#2C3E50',
        'border-color': '#CCCCCC', // Light border color
        'border-dark': '#999999', // Darker border color
      },
      borderRadius: {
        lg: '0.5rem',
        md: 'calc(0.5rem - 2px)',
        sm: 'calc(0.5rem - 4px)',
        xl: '0.75rem',
      },
      fontFamily: {
        headingEn: ['Poppins'],
        headingPs: ['Noto Naskh Arabic'],
        bodyEn: ['Inter'],
        bodyPs: ['Almarai'],
      },
      boxShadow: {
        card: '0px 4px 10px rgba(0, 0, 0, 0.1)',
        'card-hover': '0px 8px 20px rgba(0, 0, 0, 0.15)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
