import React from 'react';
import { X } from 'lucide-react';

const Drawer = React.forwardRef(
  ({ isOpen, onClose, title, children, position = 'right', size = 'md', className = '', ...props }, ref) => {
    if (!isOpen) return null;

    const positions = {
      left: 'left-0',
      right: 'right-0',
      top: 'top-0',
      bottom: 'bottom-0',
    };

    const sizes = {
      sm: position === 'left' || position === 'right' ? 'w-80' : 'h-80',
      md: position === 'left' || position === 'right' ? 'w-96' : 'h-96',
      lg: position === 'left' || position === 'right' ? 'w-[32rem]' : 'h-[32rem]',
      xl: position === 'left' || position === 'right' ? 'w-[40rem]' : 'h-[40rem]',
      full: position === 'left' || position === 'right' ? 'w-full' : 'h-full',
    };

    return (
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75" onClick={onClose} />

        <div
          ref={ref}
          className={`fixed ${positions[position]} ${sizes[size]} bg-white dark:bg-gray-800 shadow-xl transform transition-transform duration-300 ease-in-out ${className}`}
          {...props}
        >
          {title && (
            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">{title}</h3>
              <button
                type="button"
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
                onClick={onClose}
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          )}
          <div className="p-6">{children}</div>
        </div>
      </div>
    );
  }
);

Drawer.displayName = 'Drawer';

export default Drawer;
