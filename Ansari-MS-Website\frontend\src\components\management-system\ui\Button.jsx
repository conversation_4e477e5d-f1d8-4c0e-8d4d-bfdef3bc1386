/* eslint-disable react/prop-types */
import * as React from 'react';
import { cn } from '@/components/management-system/lib/utils';

const Button = React.forwardRef(
  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {
    const Comp = asChild ? React.Slot : 'button';

    const variantStyles = {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'underline-offset-4 hover:underline text-primary',
      success: 'bg-success text-white hover:bg-success/90 shadow-sm',
      warning: 'bg-yellow-500 text-white hover:bg-yellow-600 shadow-sm',
      info: 'bg-blue-500 text-white hover:bg-blue-600 shadow-sm',
    };

    const sizeStyles = {
      default: 'h-10 py-2 px-4',
      sm: 'h-9 px-3 rounded-md',
      lg: 'h-11 px-8 rounded-md',
      icon: 'h-10 w-10',
    };

    return (
      <Comp
        className={cn(
          'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
          variantStyles[variant],
          sizeStyles[size],
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button };

// const Button = ({ children, onClick, className, disabled }) => {
//   return (
//     <button
//       className={`px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 ${className}`}
//       onClick={onClick}
//       disabled={disabled}
//     >
//       {children}
//     </button>
//   );
// };

// export default Button;

// /* eslint-disable react/prop-types */
// // src/components/ui/button.jsx
// import * as React from "react"

// const Button = React.forwardRef(
//   ({ className, variant = "default", size = "default", asChild = false, ...props }, ref) => {
//     const Comp = asChild ? React.Slot : "button"

//     const variantStyles = {
//       default: "bg-primary text-primary-foreground hover:bg-primary/90",
//       destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
//       outline: "border border-input hover:bg-accent hover:text-accent-foreground",
//       secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
//       ghost: "hover:bg-accent hover:text-accent-foreground",
//       link: "underline-offset-4 hover:underline text-primary",
//     }

//     const sizeStyles = {
//       default: "h-10 py-2 px-4",
//       sm: "h-9 px-3 rounded-md",
//       lg: "h-11 px-8 rounded-md",
//       icon: "h-10 w-10",
//     }

//     return (
//       <Comp
//         className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background ${variantStyles[variant]} ${sizeStyles[size]} ${className}`}
//         ref={ref}
//         {...props}
//       />
//     )
//   }
// )
// Button.displayName = "Button"

// export { Button }
