import React from 'react';

const Tabs = React.forwardRef(({ children, value, onChange, className = '', ...props }, ref) => {
  return (
    <div ref={ref} className={className} {...props}>
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {React.Children.map(children, (child) => {
            if (!React.isValidElement(child)) return null;
            return React.cloneElement(child, {
              selected: child.props.value === value,
              onClick: () => onChange(child.props.value),
            });
          })}
        </nav>
      </div>
      <div className="mt-4">
        {React.Children.map(children, (child) => {
          if (!React.isValidElement(child)) return null;
          if (child.props.value === value) {
            return child.props.children;
          }
          return null;
        })}
      </div>
    </div>
  );
});

const Tab = ({ label, value, selected, onClick, className = '', ...props }) => {
  const baseStyles = 'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none';
  const selectedStyles = selected
    ? 'border-[#FF6B00] text-[#FF6B00]'
    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600';

  return (
    <button className={`${baseStyles} ${selectedStyles} ${className}`} onClick={onClick} {...props}>
      {label}
    </button>
  );
};

const TabPanel = ({ children, value, ...props }) => {
  return (
    <div role="tabpanel" {...props}>
      {children}
    </div>
  );
};

Tabs.Tab = Tab;
Tabs.Panel = TabPanel;

Tabs.displayName = 'Tabs';
Tab.displayName = 'Tabs.Tab';
TabPanel.displayName = 'Tabs.Panel';

export default Tabs;
