import express from "express";
import FarmController from "../controllers/farmController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();
router.use(authenticate, authorizeAdmin);

router.post("/", FarmController.createFarm);
router.get("/", FarmController.getAllFarms);
router.get("/:id", FarmController.getFarmById);
router.put("/:id", FarmController.updateFarm);
router.delete("/:id", FarmController.deleteFarm);

export default router;
