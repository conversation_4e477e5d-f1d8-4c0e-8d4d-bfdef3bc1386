'use client';

import { useState, useEffect } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, MapPin, Phone, Mail, Calendar, Users, Package, Activity } from 'lucide-react';
import Card from '../../components/shared/Card';
import Button from '../../components/shared/Button';
import Badge from '../../components/shared/Badge';
import Modal from '../../components/shared/Modal';
import { useFarm } from '../../contexts/FarmContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from 'recharts';

const FarmDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { farms, deleteFarm } = useFarm();
  const { language, translations } = useLanguage();
  const [farm, setFarm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    const foundFarm = farms.find((f) => f.id === id);
    if (foundFarm) {
      setFarm(foundFarm);
    }
    setLoading(false);
  }, [id, farms]);

  const openDeleteModal = () => {
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (window.confirm(t('confirm_delete_farm'))) {
      try {
        await deleteFarm(id);
        navigate('/admin/farms');
      } catch (error) {
        console.error('Error deleting farm:', error);
        setError('Failed to delete farm');
      }
    }
  };

  const handleEdit = () => {
    navigate(`/admin/farms/edit/${id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Error</h2>
        <p className="mt-2 text-gray-600 dark:text-gray-400">{error}</p>
        <Link to="/admin/farms" className="mt-4 inline-flex items-center text-[#FF6B00] hover:text-[#FF6B00]/80">
          <ArrowLeft className="mr-2" size={16} />
          Back to Farms
        </Link>
      </div>
    );
  }

  if (!farm) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <Card>
            <Card.Content className="flex flex-col items-center justify-center py-12">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{t('farm_not_found')}</h2>
              <Button
                onClick={() => navigate('/admin/farms')}
                className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <ArrowLeft className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                {t('back_to_farms')}
              </Button>
            </Card.Content>
          </Card>
        </div>
      </div>
    );
  }

  const chartData = [
    {
      name: t('capacity'),
      value: farm.capacity,
    },
    {
      name: t('current_stock'),
      value: farm.currentStock,
    },
  ];

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex justify-between items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="outline"
            onClick={() => navigate('/admin/farms')}
            className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <ArrowLeft className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            {t('back_to_farms')}
          </Button>
          <div className={`flex gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="outline"
              onClick={handleEdit}
              className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <Edit className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('edit')}
            </Button>
            <Button
              variant="destructive"
              onClick={openDeleteModal}
              className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <Trash2 className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('delete')}
            </Button>
          </div>
        </div>

        {/* Farm Information */}
        <Card>
          <Card.Header>
            <Card.Title>{farm.name}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">{t('farm_information')}</h3>
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('location')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{farm.location}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('type')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{t(farm.type)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('capacity')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{farm.capacity}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('current_stock')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{farm.currentStock}</dd>
                  </div>
                </dl>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-4">{t('inspection_dates')}</h3>
                <dl className="space-y-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('last_inspection')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{farm.lastInspection}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('next_inspection')}</dt>
                    <dd className="mt-1 text-sm text-gray-900 dark:text-white">{farm.nextInspection}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">{t('status')}</dt>
                    <dd className="mt-1">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          farm.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : farm.status === 'maintenance'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {t(farm.status)}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </Card.Content>
        </Card>

        {/* Chart */}
        <Card>
          <Card.Header>
            <Card.Title>{t('farm_capacity_distribution')}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#FF6B00" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card.Content>
        </Card>

        {/* Notes */}
        {farm.notes && (
          <Card>
            <Card.Header>
              <Card.Title>{t('notes')}</Card.Title>
            </Card.Header>
            <Card.Content>
              <p className="text-sm text-gray-700 dark:text-gray-300">{farm.notes}</p>
            </Card.Content>
          </Card>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} title="Delete Farm" size="sm">
        <div className="flex flex-col items-center text-center">
          <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
            <Trash2 className="text-red-600 dark:text-red-400" size={24} />
          </div>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Are you sure you want to delete{' '}
            <span className="font-semibold text-gray-900 dark:text-white">"{farm.name}"</span>? This action cannot be
            undone.
          </p>
          <div className="flex gap-3 w-full">
            <Button variant="secondary" onClick={() => setIsDeleteModalOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button variant="danger" onClick={confirmDelete} className="flex-1">
              Delete Farm
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default FarmDetails;
