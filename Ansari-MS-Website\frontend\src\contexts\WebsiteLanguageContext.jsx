/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from '../i18n/en.json';
import ps from '../i18n/ps.json';

// Initialize i18n
i18n.use(initReactI18next).init({
  resources: {
    en: { translation: en },
    ps: { translation: ps },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: { escapeValue: false },
});

const WebsiteLanguageContext = createContext();

const WebsiteLanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');

  const changeLanguage = (language) => {
    setCurrentLanguage(language);
    i18n.changeLanguage(language);
    // Save language preference to localStorage
    localStorage.setItem('preferredLanguage', language);
  };

  useEffect(() => {
    // Load saved language preference on initial render
    const savedLanguage = localStorage.getItem('preferredLanguage');
    if (savedLanguage) {
      setCurrentLanguage(savedLanguage);
      i18n.changeLanguage(savedLanguage);
    }
  }, []);

  return (
    <WebsiteLanguageContext.Provider value={{ currentLanguage, changeLanguage }}>
      {children}
    </WebsiteLanguageContext.Provider>
  );
};

const useWebsiteLanguage = () => {
  const context = useContext(WebsiteLanguageContext);
  if (!context) {
    throw new Error('useWebsiteLanguage must be used within a WebsiteLanguageProvider');
  }
  return context;
};

export { WebsiteLanguageProvider, useWebsiteLanguage, i18n };
