-- Migration to create Farms table
-- Create the Farms table with proper structure

CREATE TABLE IF NOT EXISTS Farms (
    F_Id INT AUTO_INCREMENT PRIMARY KEY,
    FName VARCHAR(255) NOT NULL,
    F_Email VARCHAR(255) NOT NULL UNIQUE,
    owner INT NOT NULL,
    F_Phone VARCHAR(20) NOT NULL UNIQUE,
    F_Location POINT NOT NULL,
    User_Id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Add indexes for better performance
    INDEX idx_farms_email (F_Email),
    INDEX idx_farms_phone (F_Phone),
    INDEX idx_farms_user_id (User_Id),
    INDEX idx_farms_owner (owner),
    
    -- Add spatial index for location queries
    SPATIAL INDEX idx_farms_location (F_Location)
);

-- Add foreign key constraints if users table exists
-- ALTER TABLE Farms 
-- ADD CONSTRAINT fk_farms_user_id 
-- FOREIGN KEY (User_Id) REFERENCES users(u_Id) ON DELETE CASCADE;

-- ALTER TABLE Farms 
-- ADD CONSTRAINT fk_farms_owner 
-- FOREIGN KEY (owner) REFERENCES users(u_Id) ON DELETE CASCADE;
