'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { MapPin, Phone, Mail, Clock, Send } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Button from '../../components/Button';

import axios from 'axios';

const Contact = () => {
  const {t,i18n}=useTranslation()
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const FIELD_LABELS = {
      C_Title: 'Subject',
      C_Body: 'Message',
      user_name: 'Full Name',
      user_email: 'Email Address',
      user_phone: 'Phone Number',
    };

    const payload = {
      C_Title: formData.subject,
      C_Body: formData.message,
      user_name: formData.name,
      user_email: formData.email,
      user_phone: formData.phone || null,
    };

    try {
      await axios.post('http://localhost:5432/api/v1/contact-us', payload, {
        headers: { 'Content-Type': 'application/json' },
      });

      setFormStatus({
        submitted: true,
        success: true,
        message: "Thank you! We'll get back to you soon.",
      });

      setTimeout(() => {
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });
        setFormStatus({
          submitted: false,
          success: false,
          message: '',
        });
      }, 3000);
    } catch (error) {
      let errorMessage = 'Oops! Something went wrong. Please try again later.';
      const errs = error.response?.data?.errors;

      if (Array.isArray(errs) && errs.length) {
        const friendly = errs.map((msg) => {
          const field = msg.match(/"([^"]+)"/)?.[1];
          const fieldLabel = FIELD_LABELS[field] || field.replace(/_/g, ' ');

          // Custom friendly message for phone number format error
          if (msg.includes('user_phone') && msg.includes('fails to match the required pattern')) {
            return 'Please enter a valid phone number (e.g., 07XXXXXXXX or +937XXXXXXXX).';
          }

          // Custom message for invalid email
          if (msg.includes('user_email') && msg.includes('valid email')) {
            return 'Please enter a valid email address.';
          }

          // Fallback: Replace field names with labels
          return msg.replace(/"([^"]+)"/g, (_, f) => FIELD_LABELS[f] || f.replace(/_/g, ' '));
        });

        errorMessage = friendly.join(' ');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      setFormStatus({
        submitted: true,
        success: false,
        message: errorMessage,
      });
    }
  };

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.35 } },
  };

  const dir = i18n.language === 'ps' ? 'rtl' : 'ltr';
      useEffect(() => {
        document.documentElement.setAttribute('dir', dir);
        document.documentElement.setAttribute('lang', i18n.language);
      }, []);

      const faqList = t('contact.faq.questions', { returnObjects: true });

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[90vh] top-16 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img src="/imgs/contact.jpg" alt="Contact Us" className="w-full h-full" />
          <div className="absolute inset-0 bg-gradient-to-r from-[#2C3E50]/90 to-[#333333]/80"></div>
        </div>

        <div className="relative flex  z-10 max-w-6xl mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay:0.2 }}>
            <h1 className="text-4xl sm:text-5xl rtl:flex-row-reverse flex justify-center gap-2 md:text-6xl font-extrabold  text-white mb-6 leading-tight">
              {t('contact.title')} <span className="text-primary">{t('contact.badge')}</span>
            </h1>
            <p className="text-base sm:text-xl text-white/90 max-w-3xl mx-auto mb-4">
              {t('contact.subtitle')}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information & Form Section */}
      <section className="relative top-16 py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Information */}
            <motion.div
              className="lg:col-span-1"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
              transition={{ duration: 0.8,delay:0.2 }}
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden h-full">
                <div className="p-6 bg-[#2C3E50] text-white">
                  <h2 className="text-2xl font-bold mb-2">{t('contact.info.title')}</h2>
                  <p className="text-white/80">{t('contact.info.subtitle')}</p>
                </div>

                <div className="p-6 space-y-6">
                  <div className="flex gap-2 items-start">
                    <MapPin className="text-primary mr-4 mt-1 flex-shrink-0" size={24} />
                    <div>
                      <h3 className="font-semibold text-lg text-[#333333] mb-1">{t('contact.info.office.title')}</h3>
                      <p className="text-gray-600">{t('contact.info.office.address')}</p>
                    </div>
                  </div>

                  <div className="flex gap-2 items-start ">
                    <Phone className="text-primary mr-4 mt-1 flex-shrink-0" size={24} />
                    <div>
                      <h3 className="font-semibold text-lg text-[#333333] mb-1">{t('contact.info.phone.title')}</h3>
                      <div className="flex flex-col gap-1">
                        <p className="text-gray-600" dir="ltr" style={{ unicodeBidi: 'plaintext' }}>
                          {t('contact.info.phone.number1')}
                        </p>
                        <p className="text-gray-600" dir="ltr" style={{ unicodeBidi: 'plaintext' }}>
                          {t('contact.info.phone.number2')}
                        </p>
                    </div>
                    </div>
                  </div>

                  <div className="flex gap-2 items-start">
                    <Mail className="text-primary mr-4 mt-1 flex-shrink-0" size={24} />
                    <div>
                      <h3 className="font-semibold text-lg text-[#333333] mb-1">{t('contact.info.email.title')}</h3>
                      <p className="text-gray-600">{t('contact.info.email.address1')}</p>
                      <p className="text-gray-600">{t('contact.info.email.address2')}</p>
                    </div>
                  </div>

                  <div className="flex gap-2 items-start">
                    <Clock className="text-primary mr-4 mt-1 flex-shrink-0" size={24} />
                    <div>
                      <h3 className="font-semibold text-lg text-[#333333] mb-1">{t('contact.info.hours.title')}</h3>
                      <p className="text-gray-600">{t('contact.info.hours.time1')}</p>
                      <p className="text-gray-600">{t('contact.info.hours.time2')}</p>
                      <p className="text-gray-600">{t('contact.info.hours.time3')}</p>
                    </div>
                  </div>
                </div>

                <div className="p-6 bg-gray-50 border-t">
                  <h3 className="font-semibold text-lg text-[#333333] mb-3">{t('contact.info.social')}</h3>
                  <div className="flex gap-2 ">
                    {/* Facebook */}
                    <a
                      href="#"
                      className="w-10 h-10 rounded-full bg-[#1877F2] flex items-center justify-center text-white hover:opacity-90 transition-opacity"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.675 0H1.325C.593 0 0 .593 0 1.326v21.348C0 23.407.593 24 1.325 24h11.49v-9.294H9.692v-3.622h3.123V8.413c0-3.1 1.894-4.788 4.659-4.788 1.325 0 2.464.099 2.795.143v3.24h-1.918c-1.504 0-1.796.715-1.796 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.407 24 24 23.407 24 22.674V1.326C24 .593 23.407 0 22.675 0z"/>
                      </svg>
                    </a>

                    {/* WhatsApp */}
                    <a
                      href="#"
                      className="w-10 h-10 rounded-full bg-[#25D366] flex items-center justify-center text-white hover:opacity-90 transition-opacity"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 32 32">
                        <path d="M16.016 3C9.387 3 4 8.387 4 15.016c0 2.623.801 5.049 2.176 7.055L4 29l7.157-2.15A12.925 12.925 0 0016.016 27c6.629 0 12.016-5.387 12.016-11.984C28.032 8.387 22.645 3 16.016 3zm0 21.903c-1.962 0-3.774-.534-5.332-1.456l-.384-.229-3.622 1.087 1.144-3.528-.248-.402a9.99 9.99 0 01-1.6-5.431c0-5.523 4.493-10.016 10.016-10.016S26.032 10.493 26.032 16c0 5.523-4.493 10.016-10.016 10.016zM21.12 18.364c-.298-.149-1.76-.867-2.033-.964-.273-.099-.471-.149-.67.15s-.768.964-.943 1.162c-.174.198-.347.223-.645.074-.298-.149-1.26-.464-2.398-1.477-.886-.79-1.484-1.764-1.658-2.062-.174-.298-.019-.459.13-.608.134-.133.298-.347.447-.521.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.521-.075-.149-.67-1.611-.916-2.21-.242-.579-.487-.501-.67-.51-.173-.008-.372-.009-.571-.009s-.521.075-.793.372c-.273.298-1.042 1.017-1.042 2.478 0 1.462 1.067 2.875 1.216 3.073.149.198 2.103 3.206 5.097 4.494.712.307 1.265.49 1.697.628.712.227 1.362.195 1.875.118.571-.085 1.76-.719 2.008-1.411.248-.694.248-1.29.174-1.411-.074-.124-.273-.198-.571-.347z"/>
                      </svg>
                    </a>

                    {/* Telegram */}
                    <a
                      href="#"
                      className="w-10 h-10 rounded-full bg-[#0088cc] flex items-center justify-center text-white hover:opacity-90 transition-opacity"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21.05 2.43L2.56 10.36c-1.04.43-1.03 1.03-.18 1.29l4.79 1.5 1.8 5.54c.2.61.31.85 1.03.85.68 0 .95-.31 1.31-.68l2.55-2.48 5.3 3.9c.98.53 1.68.25 1.93-.91L23.95 3.8c.33-1.32-.5-1.9-1.68-1.37z"/>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              className="lg:col-span-2"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeIn}
              transition={{ duration: 0.8,delay:0.2 }}
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="p-6 bg-[#2C3E50] text-white">
                  <h2 className="text-2xl font-bold mb-2">{t('contact.form.title')}</h2>
                  <p className="text-white/80">
                   {t('contact.form.subtitle')}
                  </p>
                </div>

                <div className="p-6">
                  {formStatus.submitted && (
                    <div
                      className={`mb-6 p-4 rounded-lg ${formStatus.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}
                    >
                      {formStatus.message}
                    </div>
                  )}

                  <form onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                          {t('contact.form.name')} *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary outline-none transition-colors"
                          placeholder={t('contact.form.placeholder.name')}
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                          {t('contact.form.email')} *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary outline-none transition-colors"
                          placeholder={t('contact.form.placeholder.email')}
                        />
                      </div>

                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                          {t('contact.form.phone')}
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary outline-none transition-colors"
                          placeholder={t('contact.form.placeholder.phone')}
                        />
                      </div>

                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                          {t('contact.form.subject')} *
                        </label>
                        <input
                          type="text"
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary outline-none transition-colors"
                          placeholder={t('contact.form.placeholder.subject')}
                        />
                      </div>
                    </div>

                    <div className="mb-6">
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                        {t('contact.form.message')} *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        rows={6}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary outline-none transition-colors resize-none"
                        placeholder={t('contact.form.placeholder.message')}
                      ></textarea>
                    </div>

                    <div>
                      <Button
                        variants="primary"
                        type="submit"
                        // className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-primary hover:bg-destructive focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                      >
                        {t('contact.form.button')}
                        <Send className="ml-2 -mr-1 h-5 w-5" />
                      </Button>
                    </div>
                  </form>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="relative top-16 py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            transition={{ duration: 0.8,delay:0.2 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[#333333] mb-4">{t('contact.map.title')}</h2>
            <p className="text-gray-600 mb-4">
             {t('contact.map.subtitle')}
            </p>
            <p className="text-gray-600 max-w-3xl mx-auto">
             {t('contact.map.description')}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            className="rounded-xl overflow-hidden shadow-lg"
            transition={{ duration: 0.8,delay:0.2 }}
          >
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d107333.95613211337!2d65.64932256418202!3d31.61499029576881!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3ed6a0d0d0a0a0a9%3A0x3e3c3e8d23c0aca0!2sKandahar%2C%20Afghanistan!5e0!3m2!1sen!2sus!4v1648138100000!5m2!1sen!2sus"
              width="100%"
              height="500"
              style={{ border: 0 }}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Our Location in Kandahar"
            ></iframe>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative top-16 py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            transition={{ duration: 0.8,delay:0.2 }}
          >
            <span className="text-primary font-semibold text-lg uppercase tracking-wider">FAQ</span>
            <h2 className="text-3xl md:text-4xl font-bold text-[#333333] mt-2 mb-4">{t('contact.faq.title')}</h2>
            <p className="text-gray-600">{t('contact.faq.subtitle')}</p>
          </motion.div>

          <motion.div
            className="space-y-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
            transition={{duration:0.8,delay:0.2}}
          >
            {faqList.map((faq, index) => (
              <motion.div key={index} className="bg-white rounded-lg shadow-md overflow-hidden" variants={fadeIn}>
                <details className="group">
                  <summary className="flex items-center justify-between p-6 cursor-pointer">
                    <h3 className="text-lg font-semibold text-[#333333]">{faq.question}</h3>
                    <span className="ml-6 flex-shrink-0 text-primary group-open:rotate-180 transition-transform duration-300">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M19 9L12 16L5 9"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </span>
                  </summary>
                  <div className="px-6 pb-6 text-gray-600">
                    <p>{faq.answer}</p>
                  </div>
                </details>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            className="mt-12 text-center"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            transition={{ duration: 0.8,delay:0.2 }}
          >
            <p className="text-gray-600 mb-6">{t('contact.faq.moreQuestions.text')}</p>
            <a
              href="#"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-sm shadow-sm text-white bg-primary hover:bg-destructive focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
            >
              {t('contact.faq.moreQuestions.button')}
            </a>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
