/* eslint-disable react/prop-types */
// src/components/management-system/ui/Popover.jsx

import { useState } from 'react';

const Popover = ({ children, content }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <PopoverTrigger>
        <button onClick={() => setIsOpen(!isOpen)} className="text-blue-500 hover:text-blue-700">
          {children}
        </button>
      </PopoverTrigger>
      {isOpen && (
        <PopoverContent>
          <div className="absolute mt-2 p-4 border rounded-lg bg-white shadow-lg">{content}</div>
        </PopoverContent>
      )}
    </div>
  );
};

const PopoverTrigger = ({ children }) => {
  return <>{children}</>;
};

const PopoverContent = ({ children }) => {
  return <>{children}</>;
};

export { Popover, PopoverTrigger, PopoverContent };
