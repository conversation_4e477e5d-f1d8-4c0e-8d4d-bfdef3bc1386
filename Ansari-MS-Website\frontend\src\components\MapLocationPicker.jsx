import { useState, useEffect, useRef } from 'react';
import { MapPin, Search } from 'lucide-react';

const MapLocationPicker = ({ onLocationSelect, initialLocation = '', className = '' }) => {
  const mapRef = useRef(null);
  const [selectedLocation, setSelectedLocation] = useState(initialLocation);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMapVisible, setIsMapVisible] = useState(true);
  const [coordinates, setCoordinates] = useState({ lat: 34.5553, lng: 69.2075 }); // Default to Kabul
  const [map, setMap] = useState(null);
  const [marker, setMarker] = useState(null);
  const [searchMessage, setSearchMessage] = useState({ type: '', text: '' });

  useEffect(() => {
    if (initialLocation) {
      const coords = initialLocation.split(' ');
      if (coords.length === 2) {
        const newCoords = {
          lat: parseFloat(coords[0]),
          lng: parseFloat(coords[1])
        };
        setCoordinates(newCoords);
        setSelectedLocation(initialLocation);

        // Update map if it exists
        if (map) {
          map.setView([newCoords.lat, newCoords.lng], 13);
          updateMarker(newCoords.lat, newCoords.lng);
        }
      }
    }
  }, [initialLocation, map]); // Added map dependency to ensure marker is created when map is ready

  // Initialize Leaflet map
  useEffect(() => {
    if (typeof window === 'undefined' || !mapRef.current || !isMapVisible) return;

    // Check if map is already initialized
    if (mapRef.current._leaflet_id) {
      return;
    }

    const initializeMap = async () => {
      try {
        // Dynamically import Leaflet
        const leaflet = await import('leaflet');

        // Create custom icon to avoid default icon issues
        const customIcon = leaflet.icon({
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41]
        });

        // Create map
        const mapInstance = leaflet.map(mapRef.current).setView([coordinates.lat, coordinates.lng], 13);

        leaflet
          .tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          })
          .addTo(mapInstance);

        setMap(mapInstance);

        // Add initial marker if location is set
        if (selectedLocation) {
          const initialMarker = leaflet.marker([coordinates.lat, coordinates.lng], { icon: customIcon })
            .addTo(mapInstance)
            .bindPopup(`Selected Location: ${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`);
          setMarker(initialMarker);
        }

        // Add click event to map
        mapInstance.on('click', (e) => {
          const { lat, lng } = e.latlng;
          updateMarker(lat, lng);
          updateLocation(lat, lng);
        });

      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(initializeMap, 100);

    // Cleanup function
    return () => {
      clearTimeout(timer);
      if (map) {
        map.remove();
        setMap(null);
        setMarker(null);
      }
    };
  }, [isMapVisible]);

  const updateMarker = async (lat, lng) => {
    if (!map) return;

    try {
      const leaflet = await import('leaflet');

      // Create custom icon
      const customIcon = leaflet.icon({
        iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
        iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
        shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
      });

      // Remove existing marker
      if (marker) {
        map.removeLayer(marker);
      }

      // Add new marker with custom icon
      const newMarker = leaflet.marker([lat, lng], { icon: customIcon })
        .addTo(map)
        .bindPopup(`Selected Location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`)
        .openPopup();

      setMarker(newMarker);
    } catch (error) {
      console.error('Error updating marker:', error);
    }
  };

  const updateLocation = (lat, lng) => {
    const locationString = `${lat.toFixed(6)} ${lng.toFixed(6)}`;
    setSelectedLocation(locationString);
    setCoordinates({ lat, lng });
    onLocationSelect(locationString);
  };

  const handleCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser. Please select location manually on the map.');
      return;
    }

    // Show loading state
    const button = document.querySelector('[data-current-location]');
    if (button) {
      button.textContent = 'Getting location...';
      button.disabled = true;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const lat = position.coords.latitude;
        const lng = position.coords.longitude;

        console.log('Current location:', lat, lng);

        updateLocation(lat, lng);
        updateMarker(lat, lng);

        // Center map on current location
        if (map) {
          map.setView([lat, lng], 15);
        }

        // Reset button
        if (button) {
          button.textContent = 'Use Current Location';
          button.disabled = false;
        }
      },
      (error) => {
        console.error('Geolocation error:', error);

        let errorMessage = 'Unable to get your current location. ';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Location access was denied. Please enable location access in your browser settings.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out.';
            break;
          default:
            errorMessage += 'An unknown error occurred.';
            break;
        }
        errorMessage += ' Please select location manually on the map.';

        alert(errorMessage);

        // Reset button
        if (button) {
          button.textContent = 'Use Current Location';
          button.disabled = false;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };

  const handleManualInput = (value) => {
    setSelectedLocation(value);
    onLocationSelect(value);

    // Try to parse coordinates and update map
    const coords = value.split(' ');
    if (coords.length === 2) {
      const lat = parseFloat(coords[0]);
      const lng = parseFloat(coords[1]);
      if (!isNaN(lat) && !isNaN(lng)) {
        setCoordinates({ lat, lng });

        // Update map and marker
        if (map) {
          map.setView([lat, lng], 13);
          updateMarker(lat, lng);
        }
      }
    }
  };

  const searchLocation = async () => {
    if (!searchQuery.trim()) {
      setSearchMessage({ type: 'error', text: 'Please enter a location to search' });
      return;
    }

    setSearchMessage({ type: 'info', text: 'Searching...' });

    try {
      console.log('Searching for:', searchQuery);

      // Simple search first - just add Afghanistan to the query
      const query = `${searchQuery} Afghanistan`;
      console.log('Search query:', query);

      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=3`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Search results:', data);

      if (data && data.length > 0) {
        const result = data[0]; // Take the first result
        const lat = parseFloat(result.lat);
        const lng = parseFloat(result.lon);

        if (isNaN(lat) || isNaN(lng)) {
          throw new Error('Invalid coordinates received');
        }

        console.log(`Found location: ${result.display_name} at ${lat}, ${lng}`);

        updateLocation(lat, lng);
        await updateMarker(lat, lng);

        // Center map on search result
        if (map) {
          map.setView([lat, lng], 13);
        }

        setSearchQuery('');
        setSearchMessage({ type: 'success', text: `Found: ${result.display_name}` });

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSearchMessage({ type: '', text: '' });
        }, 3000);
      } else {
        setSearchMessage({
          type: 'error',
          text: 'Location not found. Please try a different search term like "Kabul", "Kandahar", "Herat", etc.'
        });
      }
    } catch (error) {
      console.error('Error searching location:', error);
      setSearchMessage({
        type: 'error',
        text: `Error searching for location: ${error.message}. Please try again.`
      });
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Manual Input */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Location Coordinates
        </label>
        <input
          type="text"
          value={selectedLocation}
          onChange={(e) => handleManualInput(e.target.value)}
          placeholder="34.5553 69.2075"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <small className="text-gray-500">Format: latitude longitude</small>
      </div>

      {/* Search Location */}
      <div className="space-y-2">
        <div className="flex gap-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setSearchMessage({ type: '', text: '' }); // Clear message when typing
            }}
            placeholder="Search for a place in Afghanistan (e.g., Kabul, Kandahar, Herat)..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                searchLocation();
              }
            }}
          />
          <button
            type="button"
            onClick={() => {
              console.log('Search button clicked');
              searchLocation();
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Search className="h-4 w-4" />
          </button>
        </div>

        {/* Search Message */}
        {searchMessage.text && (
          <div
            className={`p-2 rounded-md text-sm ${
              searchMessage.type === 'success'
                ? 'bg-green-100 text-green-700'
                : searchMessage.type === 'error'
                ? 'bg-red-100 text-red-700'
                : 'bg-blue-100 text-blue-700'
            }`}
          >
            {searchMessage.text}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <button
          type="button"
          onClick={handleCurrentLocation}
          data-current-location
          className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          <MapPin className="h-4 w-4" />
          Use Current Location
        </button>
        <button
          type="button"
          onClick={() => setIsMapVisible(!isMapVisible)}
          className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          {isMapVisible ? 'Hide Map' : 'Show Map'}
        </button>
      </div>

      {/* Interactive Leaflet Map */}
      {isMapVisible && (
        <div className="border border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
          <div className="h-[400px] relative">
            {/* Map container */}
            <div ref={mapRef} className="h-full w-full z-10"></div>

            {/* Loading indicator */}
            {!map && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-0">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="mt-4 text-gray-600">Loading map...</p>
                </div>
              </div>
            )}
          </div>

          {/* Map info */}
          <div className="p-3 bg-gray-50 border-t">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                <span>Click on map to select location</span>
              </div>
              <div className="text-xs">
                Current: {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Coordinate Fine-tuning */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
          <input
            type="number"
            step="0.000001"
            value={coordinates.lat}
            onChange={(e) => {
              const lat = parseFloat(e.target.value);
              if (!isNaN(lat)) {
                const newCoords = { ...coordinates, lat };
                setCoordinates(newCoords);
                const locationString = `${lat} ${coordinates.lng}`;
                setSelectedLocation(locationString);
                onLocationSelect(locationString);

                // Update map
                if (map) {
                  map.setView([lat, coordinates.lng], map.getZoom());
                  updateMarker(lat, coordinates.lng);
                }
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
          <input
            type="number"
            step="0.000001"
            value={coordinates.lng}
            onChange={(e) => {
              const lng = parseFloat(e.target.value);
              if (!isNaN(lng)) {
                const newCoords = { ...coordinates, lng };
                setCoordinates(newCoords);
                const locationString = `${coordinates.lat} ${lng}`;
                setSelectedLocation(locationString);
                onLocationSelect(locationString);

                // Update map
                if (map) {
                  map.setView([coordinates.lat, lng], map.getZoom());
                  updateMarker(coordinates.lat, lng);
                }
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );
};

export default MapLocationPicker;
