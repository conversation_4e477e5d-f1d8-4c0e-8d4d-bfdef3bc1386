import React from 'react';
import { Calendar, Clock } from 'lucide-react';

const DateTimePicker = React.forwardRef(
  (
    {
      label,
      value,
      onChange,
      error,
      helperText,
      placeholder = 'Select date and time',
      className = '',
      disabled = false,
      required = false,
      ...props
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const [selectedDateTime, setSelectedDateTime] = React.useState(value ? new Date(value) : null);
    const dateTimePickerRef = React.useRef(null);

    React.useEffect(() => {
      const handleClickOutside = (event) => {
        if (dateTimePickerRef.current && !dateTimePickerRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleDateTimeSelect = (dateTime) => {
      setSelectedDateTime(dateTime);
      if (onChange) {
        onChange(dateTime);
      }
      setIsOpen(false);
    };

    const formatDateTime = (date) => {
      if (!date) return '';
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    };

    const baseStyles =
      'w-full px-3 py-2 rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed';
    const borderStyles = error ? 'border-red-500 dark:border-red-500' : 'border-gray-300 dark:border-gray-600';

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          <input
            ref={ref}
            type="text"
            value={formatDateTime(selectedDateTime)}
            placeholder={placeholder}
            onClick={() => !disabled && setIsOpen(true)}
            readOnly
            className={`${baseStyles} ${borderStyles} ${className}`}
            disabled={disabled}
            required={required}
            {...props}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            <Calendar className="h-5 w-5 text-gray-400" />
            <Clock className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        {isOpen && (
          <div
            ref={dateTimePickerRef}
            className="absolute z-50 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4"
          >
            {/* Calendar implementation */}
            <div className="grid grid-cols-7 gap-1 mb-4">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="text-center text-sm font-medium text-gray-500 dark:text-gray-400">
                  {day}
                </div>
              ))}
              {/* Add calendar days here */}
            </div>

            {/* Time picker implementation */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="grid grid-cols-4 gap-2">
                {Array.from({ length: 24 }, (_, i) => (
                  <button
                    key={i}
                    className="px-3 py-2 text-sm rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    {i.toString().padStart(2, '0')}:00
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
        {(error || helperText) && (
          <p className={`mt-1 text-sm ${error ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

DateTimePicker.displayName = 'DateTimePicker';

export default DateTimePicker;
