// api = https://api.openweathermap.org/data/2.5/weather?lat={lat}&lon={lon}&appid={API key}



import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { Link } from 'react-router-dom';
import ChickenInformation from '../../../components/shopper/ChickenInformation';
import {
  Calendar,
  Sun,
  Moon,
  Bell,
  Settings,
  Menu,
  CloudRain,
  CloudSun,
  Snowflake,
  Cloud,
  CloudDrizzle,
  X,
  User,
} from 'lucide-react';

// OpenWeatherMap API key
const API_KEY = '********************************';

// Utility to conditionally join class names
const classNames = (...classes) => classes.filter(Boolean).join(' ');

// Get weather icon component
const getWeatherIcon = (main) => {
  switch (main) {
    case 'Clear':
      return <Sun className="h-10 w-10 text-yellow-400" />;
    case 'Clouds':
      return <Cloud className="h-10 w-10 text-gray-400" />;
    case 'Rain':
      return <CloudRain className="h-10 w-10 text-blue-500" />;
    case 'Drizzle':
      return <CloudDrizzle className="h-10 w-10 text-blue-300" />;
    case 'Snow':
      return <Snowflake className="h-10 w-10 text-blue-300" />;
    default:
      return <Cloud className="h-10 w-10 text-gray-400" />;
  }
};

// User avatar component
const UserAvatar = ({ user, size = 8, className = '' }) => (
  <div
    className={classNames(
      `rounded-full bg-primary/10 flex items-center justify-center overflow-hidden`,
      size === 10 ? 'h-10 w-10' : `h-${size} w-${size}`,
      className
    )}
  >
    {user?.image ? (
      <img
        src={`http://localhost:5432/public/images/users/${user.image}`}
        alt={`${user.firstName || 'Farmer'} ${user.lastName || 'User'}`}
        className="h-full w-full object-cover"
        loading="lazy"
      />
    ) : (
      <User className={`text-primary ${size === 10 ? 'h-6 w-6' : 'h-5 w-5'}`} />
    )}
  </div>
);

const ShopperDashboard = () => {
  const { user, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() =>
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  const [messages] = useState([
    {
      id: 1,
      sender: 'Support',
      content: 'Hello! How can we help you today?',
      time: '2 minutes ago',
    },
    {
      id: 2,
      sender: `${user?.firstName} ${user?.lastName}`  || 'You',
      content: 'I have a question about irrigation.',
      time: 'Just now',
    },
  ]);

  const [weather, setWeather] = useState(null);
  const [forecast, setForecast] = useState([]);
  const [loading, setLoading] = useState(true);

  const toggleDarkMode = () => {
    setIsDarkMode((prev) => {
      const newMode = !prev;
      document.documentElement.classList.toggle('dark', newMode);
      return newMode;
    });
  };

  useEffect(() => {
    document.documentElement.classList.toggle('dark', isDarkMode);
  }, [isDarkMode]);

  const fetchWeather = async (lat, lon) => {
    try {
      const currentRes = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
      );
      const currentData = await currentRes.json();

      const forecastRes = await fetch(
        `https://api.openweathermap.org/data/2.5/forecast?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
      );
      const forecastData = await forecastRes.json();

      setWeather({
        temp: currentData.main.temp,
        main: currentData.weather[0].main,
        description: currentData.weather[0].description,
        location: currentData.name,
        icon: getWeatherIcon(currentData.weather[0].main),
      });

      const daily = forecastData.list
        .filter((item) => item.dt_txt.includes('12:00:00'))
        .slice(0, 4)
        .map((item) => ({
          day: new Date(item.dt_txt).toLocaleDateString('en-US', {
            weekday: 'short',
          }),
          temp: Math.round(item.main.temp),
          main: item.weather[0].main,
          icon: getWeatherIcon(item.weather[0].main),
        }));

      setForecast(daily);
    } catch (err) {
      console.error('Error fetching weather:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (pos) => fetchWeather(pos.coords.latitude, pos.coords.longitude),
        () => fetchWeather(31.6289, 65.7372)
      );
    } else {
      fetchWeather(31.6289, 65.7372);
    }
  }, []);

  return (
    <div className="space-y-6">
      

      <section className="px-4 md:px-6">
        <h1 className="text-2xl font-bold dark:text-white">
          Welcome, {user?.firstName || 'Farmer'} {user?.lastName}
        </h1>
        <p className="text-muted-foreground dark:text-gray-300">
          Check the weather and chat with support.
        </p>
      </section>

      <section className="flex flex-col  md:flex-row gap-6 px-4 md:px-6">
        <div className="flex-1 rounded-lg border bg-gradient-to-br from-blue-50 to-yellow-50 p-6 dark:from-gray-800 dark:to-gray-900 dark:border-gray-700 shadow">
          <h3 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-200">
            Weather Forecast
          </h3>

          {loading ? (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Loading weather data...
            </p>
          ) : weather ? (
            <>
              <div className="flex flex-col items-center text-center bg-white rounded-lg shadow-inner p-6 mb-4 dark:bg-gray-700">
                {weather.icon}
                <p className="text-3xl font-bold text-gray-800 mt-2 dark:text-white">
                  {Math.round(weather.temp)}°C
                </p>
                <p className="text-sm text-muted-foreground dark:text-gray-300">
                  {weather.main}, {weather.location}
                </p>
                <p className="text-xs text-green-600 mt-1 italic dark:text-green-400">
                  {weather.description}
                </p>
              </div>

              <div className="grid grid-cols-4 gap-2 text-center text-sm">
                {forecast.map((day, idx) => (
                  <div
                    key={idx}
                    className="bg-white p-2 rounded shadow-sm dark:bg-gray-700"
                  >
                    <p className="text-xs md:text-sm font-semibold  dark:text-muted">{day.day}</p>
                    <span className='flex justify-center w-full'>
                      {day.icon}
                    </span>
                    <p className="text-xs md:text-sm dark:text-gray-300">{day.temp}°C</p>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <p className="text-sm text-gray-500">Weather data not available.</p>
          )}
        </div>
      
       {/* Chat Section */}
       
        <div className="flex-1 rounded-lg border bg-muted shadow p-6 dark:bg-gray-800 dark:border-gray-700">
          <div className="flex flex-col gap-3 justify-between items-center mb-4">
            <h3 className="text-base md:text-lg font-bold dark:text-white">Chat with Support</h3>
            <Link
              to="/farmer/chat"
              className="text-xs md:text-sm text-primary border rounded-md py-2 px-3 dark:border-gray-400 hover:underline flex items-center"
            >
              Full Chat <Calendar className="h-4 w-4 ml-1" />
            </Link>
          </div>
          <div className="h-64 overflow-y-auto space-y-4 bg-muted/30 p-4 rounded dark:bg-gray-700/50">
            {messages.map((msg) => (
              <div key={msg.id} className="text-sm dark:text-gray-300">
                <p className="font-semibold">{msg.sender}</p>
                <p>{msg.content}</p>
                <p className="text-xs text-muted-foreground">{msg.time}</p>
              </div>
            ))}
          </div>
        </div>
        </section>

        {/* Chicken Information Section */}
        <section className="px-4 md:px-6">
          <ChickenInformation />
        </section>

    </div>
  );
};

export default ShopperDashboard;


///////////////////////
// 'use client';

// import { useState } from 'react';
// import { Link } from 'react-router-dom';
// import {
//   ShoppingCart,
//   Package,
//   Heart,
//   Clock,
//   ArrowUpRight,
//   TrendingUp,
//   Bell,
//   ChevronRight,
//   Star,
//   Truck,
//   Store,
// } from 'lucide-react';
// import { useAuth } from '../../../contexts/AuthContext';

// const ShopperDashboard = () => {
//   const { user } = useAuth();
//   const [stats] = useState([
//     {
//       name: 'Active Orders',
//       value: '2',
//       change: '+1',
//       isIncrease: true,
//       icon: Package,
//     },
//     {
//       name: 'Wishlist Items',
//       value: '8',
//       change: '+3',
//       isIncrease: true,
//       icon: Heart,
//     },
//     {
//       name: 'Cart Items',
//       value: '4',
//       change: '+2',
//       isIncrease: true,
//       icon: ShoppingCart,
//     },
//     {
//       name: 'Completed Orders',
//       value: '12',
//       change: '+1',
//       isIncrease: true,
//       icon: Clock,
//     },
//   ]);

//   const [recentOrders] = useState([
//     {
//       id: 'ORD-1234',
//       date: 'Apr 25, 2023',
//       status: 'Delivered',
//       total: '$125.00',
//     },
//     {
//       id: 'ORD-1233',
//       date: 'Apr 22, 2023',
//       status: 'In Transit',
//       total: '$85.50',
//     },
//     {
//       id: 'ORD-1232',
//       date: 'Apr 18, 2023',
//       status: 'Processing',
//       total: '$210.75',
//     },
//   ]);

//   const [featuredProducts] = useState([
//     {
//       id: 1,
//       name: 'Organic Tomatoes',
//       farm: 'Ahmad Farm',
//       price: '$4.99/kg',
//       rating: 4.8,
//     },
//     {
//       id: 2,
//       name: 'Fresh Eggs',
//       farm: 'Mahmood Farm',
//       price: '$3.50/dozen',
//       rating: 4.7,
//     },
//     {
//       id: 3,
//       name: 'Honey',
//       farm: 'Zahir Farm',
//       price: '$8.99/jar',
//       rating: 4.9,
//     },
//   ]);

//   return (
//     <div className="space-y-6">
//       {/* Welcome header */}
//       <div className="flex flex-col md:flex-row md:items-center md:justify-between">
//         <div>
//           <h1 className="text-2xl font-bold tracking-tight">Welcome back, {user?.firstName || 'Shopper'}</h1>
//           <p className="text-muted-foreground">Check your orders and discover fresh products</p>
//         </div>
//         <div className="mt-4 md:mt-0 flex space-x-3">
//           <Link
//             to="/shopper/products"
//             className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90"
//           >
//             Browse Products
//           </Link>
//           <Link
//             to="/shopper/cart"
//             className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground"
//           >
//             <ShoppingCart className="mr-2 h-4 w-4" />
//             View Cart
//           </Link>
//         </div>
//       </div>

//       {/* Stats cards */}
//       <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
//         {stats.map((stat, index) => (
//           <div key={index} className="rounded-lg border bg-card text-card-foreground shadow">
//             <div className="p-6 flex justify-between items-start">
//               <div>
//                 <p className="text-sm font-medium text-muted-foreground">{stat.name}</p>
//                 <p className="text-2xl font-bold mt-1">{stat.value}</p>
//                 <div className="flex items-center mt-1">
//                   <ArrowUpRight className="h-4 w-4 text-success mr-1" />
//                   <span className="text-success text-sm">{stat.change}</span>
//                 </div>
//               </div>
//               <div className="rounded-full bg-primary/10 p-3">
//                 <stat.icon className="h-5 w-5 text-primary" />
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Main content */}
//       <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
//         {/* Active orders */}
//         <div className="col-span-2 rounded-lg border bg-card text-card-foreground shadow">
//           <div className="p-6">
//             <div className="flex items-center justify-between mb-4">
//               <h3 className="text-lg font-medium">Recent Orders</h3>
//               <Link to="/shopper/orders" className="text-sm text-primary hover:underline flex items-center">
//                 View All Orders
//                 <ChevronRight className="h-4 w-4 ml-1" />
//               </Link>
//             </div>
//             <div className="overflow-x-auto">
//               <table className="w-full">
//                 <thead>
//                   <tr className="border-b text-left">
//                     <th className="pb-2 font-medium text-sm">Order ID</th>
//                     <th className="pb-2 font-medium text-sm">Date</th>
//                     <th className="pb-2 font-medium text-sm">Status</th>
//                     <th className="pb-2 font-medium text-sm text-right">Total</th>
//                   </tr>
//                 </thead>
//                 <tbody className="divide-y">
//                   {recentOrders.map((order) => (
//                     <tr key={order.id}>
//                       <td className="py-3 text-sm font-medium">{order.id}</td>
//                       <td className="py-3 text-sm text-muted-foreground">{order.date}</td>
//                       <td className="py-3 text-sm">
//                         <span
//                           className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
//                             order.status === 'Delivered'
//                               ? 'bg-green-100 text-green-800'
//                               : order.status === 'In Transit'
//                                 ? 'bg-blue-100 text-blue-800'
//                                 : 'bg-amber-100 text-amber-800'
//                           }`}
//                         >
//                           {order.status}
//                         </span>
//                       </td>
//                       <td className="py-3 text-sm font-medium text-right">{order.total}</td>
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             </div>
//           </div>
//         </div>

//         {/* Notifications */}
//         <div className="rounded-lg border bg-card text-card-foreground shadow">
//           <div className="p-6">
//             <h3 className="text-lg font-medium mb-4">Notifications</h3>
//             <div className="space-y-4">
//               <div className="flex items-start space-x-3">
//                 <div className="mt-0.5 rounded-full bg-primary/10 p-1">
//                   <Truck className="h-4 w-4 text-primary" />
//                 </div>
//                 <div className="flex-1">
//                   <p className="text-sm font-medium">Your order #ORD-1233 is out for delivery</p>
//                   <p className="text-xs text-muted-foreground mt-1">2 hours ago</p>
//                 </div>
//               </div>
//               <div className="flex items-start space-x-3">
//                 <div className="mt-0.5 rounded-full bg-green-100 p-1">
//                   <Store className="h-4 w-4 text-green-600" />
//                 </div>
//                 <div className="flex-1">
//                   <p className="text-sm font-medium">New products from Ahmad Farm are available</p>
//                   <p className="text-xs text-muted-foreground mt-1">Yesterday</p>
//                 </div>
//               </div>
//               <div className="flex items-start space-x-3">
//                 <div className="mt-0.5 rounded-full bg-amber-100 p-1">
//                   <Bell className="h-4 w-4 text-amber-600" />
//                 </div>
//                 <div className="flex-1">
//                   <p className="text-sm font-medium">Special discount on fresh vegetables</p>
//                   <p className="text-xs text-muted-foreground mt-1">2 days ago</p>
//                 </div>
//               </div>
//             </div>
//             <Link
//               to="/shopper/notifications"
//               className="mt-4 flex items-center justify-center text-sm text-primary hover:underline"
//             >
//               View all notifications
//               <ChevronRight className="h-4 w-4 ml-1" />
//             </Link>
//           </div>
//         </div>

//         {/* Featured products */}
//         <div className="col-span-2 rounded-lg border bg-card text-card-foreground shadow">
//           <div className="p-6">
//             <div className="flex items-center justify-between mb-4">
//               <h3 className="text-lg font-medium">Featured Products</h3>
//               <Link to="/shopper/products" className="text-sm text-primary hover:underline flex items-center">
//                 View All Products
//                 <ChevronRight className="h-4 w-4 ml-1" />
//               </Link>
//             </div>
//             <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
//               {featuredProducts.map((product) => (
//                 <div key={product.id} className="rounded-lg border bg-background p-4">
//                   <div className="aspect-square w-full bg-muted/40 rounded-md mb-3 flex items-center justify-center">
//                     <img
//                       src={`/placeholder.svg?height=100&width=100&text=${encodeURIComponent(product.name)}`}
//                       alt={product.name}
//                       className="h-24 w-24"
//                     />
//                   </div>
//                   <h4 className="font-medium">{product.name}</h4>
//                   <p className="text-sm text-muted-foreground">{product.farm}</p>
//                   <div className="flex items-center justify-between mt-2">
//                     <p className="font-medium">{product.price}</p>
//                     <div className="flex items-center">
//                       <Star className="h-3 w-3 fill-amber-500 text-amber-500 mr-1" />
//                       <span className="text-xs">{product.rating}</span>
//                     </div>
//                   </div>
//                   <button className="w-full mt-3 inline-flex items-center justify-center rounded-md bg-primary px-3 py-1.5 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90">
//                     Add to Cart
//                   </button>
//                 </div>
//               ))}
//             </div>
//           </div>
//         </div>

//         {/* Spending overview */}
//         <div className="rounded-lg border bg-card text-card-foreground shadow">
//           <div className="p-6">
//             <h3 className="text-lg font-medium mb-4">Spending Overview</h3>
//             <div className="h-[200px] flex items-center justify-center bg-muted/40 rounded-md">
//               <div className="text-center">
//                 <TrendingUp className="h-10 w-10 text-primary mx-auto mb-2" />
//                 <p className="text-muted-foreground">Spending chart will appear here</p>
//               </div>
//             </div>
//             <div className="mt-4 space-y-2">
//               <div className="flex items-center justify-between">
//                 <p className="text-sm">This Month</p>
//                 <p className="text-sm font-medium">$345.00</p>
//               </div>
//               <div className="flex items-center justify-between">
//                 <p className="text-sm">Last Month</p>
//                 <p className="text-sm font-medium">$280.50</p>
//               </div>
//               <div className="flex items-center justify-between text-success">
//                 <p className="text-sm">Month-over-Month</p>
//                 <p className="text-sm font-medium">+23%</p>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ShopperDashboard;
