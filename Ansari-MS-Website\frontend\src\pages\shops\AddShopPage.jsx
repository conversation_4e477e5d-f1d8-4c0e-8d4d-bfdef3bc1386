import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useShop } from '../../contexts/ShopContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../../components/Button';
import { Card, CardHeader, CardContent, CardTitle } from '../../components/feed-components/card';
import MapLocationPicker from '../../components/MapLocationPicker';
import LocationMapCell from '../../components/LocationMapCell';
import { ArrowLeft } from 'lucide-react';
import axios from 'axios';

const AddShopPage = () => {
  const navigate = useNavigate();
  const { addShop } = useShop();
  const { language, translations } = useLanguage();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [shoppers, setShoppers] = useState([]);
  const [selectedShopper, setSelectedShopper] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    owner: '',
    phone: '',
    location: '',
    userId: '',
  });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // Fetch shoppers on component mount
  useEffect(() => {
    const fetchShoppers = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const response = await axios.get('http://localhost:5432/api/v1/users/shoppers', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.data.success) {
          setShoppers(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching shoppers:', error);
      }
    };

    fetchShoppers();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleShopperSelect = (e) => {
    const shopperId = e.target.value;
    const shopper = shoppers.find(s => s.u_Id.toString() === shopperId);

    if (shopper) {
      setSelectedShopper(shopper);
      setFormData(prev => ({
        ...prev,
        userId: shopper.u_Id.toString()
      }));
    } else {
      setSelectedShopper(null);
      setFormData(prev => ({
        ...prev,
        userId: ''
      }));
    }
    setFeedback({ type: '', message: '' });
  };

  const handleLocationSelect = (location) => {
    setFormData(prev => ({
      ...prev,
      location: location
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      // Validate form
      if (!formData.name.trim()) {
        throw new Error(t('name_required') || 'Shop name is required');
      }
      if (!formData.email.trim()) {
        throw new Error(t('email_required') || 'Email is required');
      }
      if (!formData.phone.trim()) {
        throw new Error(t('phone_required') || 'Phone number is required');
      }
      if (!formData.location.trim()) {
        throw new Error(t('location_required') || 'Location is required');
      }
      if (!selectedShopper) {
        throw new Error(t('shopper_required') || 'Please select a shopper');
      }

      // Validate phone format (+93xxxxxxxxx)
      const phoneRegex = /^\+93[0-9]{9}$/;
      if (!phoneRegex.test(formData.phone)) {
        throw new Error(t('invalid_phone_format') || 'Phone must be in format +93xxxxxxxxx');
      }

      // Validate location format (latitude longitude)
      const locationRegex = /^[-+]?[0-9]*\.?[0-9]+ [-+]?[0-9]*\.?[0-9]+$/;
      if (!locationRegex.test(formData.location)) {
        throw new Error(t('invalid_location_format') || 'Location must be in format "latitude longitude"');
      }
      
      // Prepare shop data with owner ID
      const shopDataToSubmit = {
        SName: formData.name,
        SEmail: formData.email,
        SPhone: formData.phone,
        SLocation: `POINT(${formData.location.split(' ').reverse().join(' ')})`, // Convert to POINT(lng lat)
        SOwner: selectedShopper.name,
        userId: selectedShopper.u_Id,
      };

      console.log('Form data to submit:', shopDataToSubmit);
      console.log('Selected shopper:', selectedShopper);
      console.log('Current user:', user);
      
      await addShop(shopDataToSubmit);
      
      setFeedback({
        type: 'success',
        message: t('shop_added_successfully') || 'Shop added successfully',
      });

      setTimeout(() => {
        navigate('/admin/shops');
      }, 1500);
    } catch (error) {
      console.error('Error adding shop:', error);
      
      // Extract error message from backend response
      let errorMessage = error.message || t('add_shop_error') || 'Failed to add shop';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <Button
          variant="outline"
          onClick={() => navigate('/admin/shops')}
          className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back_to_shops') || 'Back to Shops'}
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('add_new_shop') || 'Add New Shop'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('create_new_shop_description') || 'Create a new shop in the system'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('shop_information') || 'Shop Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Shop Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  {t('shop_name') || 'Shop Name'}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_shop_name') || 'Enter shop name'}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  {t('email') || 'Email'}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_email') || 'Enter email address'}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Phone */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium mb-1">
                  {t('phone') || 'Phone'}
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="+93xxxxxxxxx"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
                <small className="text-gray-500">Format: +93xxxxxxxxx</small>
              </div>

              {/* Owner (Read-only) */}
              <div>
                <label htmlFor="owner" className="block text-sm font-medium mb-1">
                  {t('owner') || 'Owner (Selected Shopper)'}
                </label>
                <input
                  type="text"
                  id="owner"
                  name="owner"
                  value={selectedShopper?.name || ''}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="Select a shopper to set as owner"
                  readOnly
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
                <small className="text-gray-500">This field is automatically set to the selected shopper</small>
              </div>

              {/* Shopper Selection */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">
                  {t('select_shopper') || 'Select Shopper'}
                </label>
                <select
                  value={selectedShopper?.u_Id || ''}
                  onChange={handleShopperSelect}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  required
                >
                  <option value="">{t('choose_shopper') || 'Choose a shopper...'}</option>
                  {shoppers.map((shopper) => (
                    <option key={shopper.u_Id} value={shopper.u_Id}>
                      {shopper.name} ({shopper.email})
                    </option>
                  ))}
                </select>
                {selectedShopper && (
                  <div className="mt-2 p-2 bg-blue-50 rounded-md">
                    <small className="text-blue-700">
                      Selected: {selectedShopper.name} - User ID: {selectedShopper.u_Id}
                    </small>
                  </div>
                )}
              </div>
            </div>

            {/* Current Location Display */}
            {formData.location && (
              <div className="mt-6">
                <label className="block text-sm font-medium mb-2">
                  {t('selected_location') || 'Selected Location'}
                </label>
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <LocationMapCell location={formData.location} />
                </div>
              </div>
            )}

            {/* Location Selection with Map */}
            <div className="mt-6">
              <label className="block text-sm font-medium mb-1">{t('location') || 'Select Location'}</label>
              <MapLocationPicker
                onLocationSelect={handleLocationSelect}
                initialLocation={formData.location}
                className="w-full"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-[#FF6B00] hover:bg-[#e55a00] text-white rounded-lg disabled:opacity-50"
              >
                {loading ? (t('adding_shop') || 'Adding Shop...') : (t('add_shop') || 'Add Shop')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddShopPage;
