import db from "../config/db.js";

const servicesModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Services (Title, Description, Price, Image, Category, Status, Features)
   VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        data.Title,
        data.Description,
        data.Price,
        data.Image,
        data.Category,
        data.Status,
        JSON.stringify(data.Features || null), // Convert array to JSON string or null
      ]
    );

    const [service] = await db.execute(
      "SELECT * FROM Services WHERE SR_Id = ?",
      [result.insertId]
    );
    return service[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM Services");
    return rows.map((row) => ({
      ...row,
      Features: row.Features ? JSON.parse(row.Features) : null,
    }));
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM Services WHERE SR_Id = ?", [
      id,
    ]);
    return rows[0]
      ? { ...rows[0], Features: JSON.parse(rows[0].Features || "null") }
      : null;
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Services SET Title=?, Description=?, Price=?, Image=?, Category=?, Status=?, Features=?
   WHERE SR_Id=?`,
      [
        data.Title,
        data.Description,
        data.Price,
        data.Image,
        data.Category,
        data.Status,
        JSON.stringify(data.Features || null),
        id,
      ]
    );

    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Services WHERE SR_Id = ?", [
      id,
    ]);
    return result;
  },
};

export default servicesModel;
