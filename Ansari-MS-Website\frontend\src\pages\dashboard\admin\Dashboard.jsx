/* eslint-disable no-unused-vars */
'use client';

import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Users,
  Newspaper,
  Cross,
  MessageSquare,
  DollarSign,
  Percent,
  Activity,
  BarChart3,
  PieChart,
  Calendar as CalendarIcon,
  TrendingUp,
  AlertCircle,
  Calendar,
  Package,
  Tractor,
  ShoppingBag,
  Bird,
  ArrowUpRight,
  ArrowDownRight,
  Settings,
  FileText,
  ClipboardList,
  Download,
  Filter,
  ChevronDown,
  Search,
  Printer,
  Share2,
  Eye,
  MoreVertical,
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/management-system/ui/Card';
import { Button } from '@/components/management-system/ui/Button';
import { Progress } from '@/components/management-system/ui/Progress';
import { Badge } from '@/components/management-system/ui/Badge';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart as RechartsPie<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  Legend,
  AreaChart,
  Area,
} from 'recharts';

export default function Dashboard() {
  const [stats, setStats] = useState({
    users: 0,
    services: 0,
    news: 0,
    inquiries: 0,
    farms: 0,
    shops: 0,
    chickens: 0,
    feed: 0,
    medicine: 0,
  });
  const [revenueData, setRevenueData] = useState([]);
  const [pieData, setPieData] = useState([]);
  const [timeframe, setTimeframe] = useState('daily'); // 'daily', 'monthly', 'yearly'
  const [profitData, setProfitData] = useState([]);
  const [consumptionData, setConsumptionData] = useState([]);
  const [inventoryData, setInventoryData] = useState([]);
  const [alerts, setAlerts] = useState([
    {
      id: 1,
      message: 'Feed inventory is running low',
      type: 'warning',
      icon: <AlertCircle className="w-4 h-4 text-yellow-500" />,
    },
    {
      id: 2,
      message: 'New user registration spike detected',
      type: 'info',
      icon: <TrendingUp className="w-4 h-4 text-blue-500" />,
    },
    {
      id: 3,
      message: 'System maintenance scheduled',
      type: 'info',
      icon: <Calendar className="w-4 h-4 text-purple-500" />,
    },
  ]);
  const [reportType, setReportType] = useState('daily'); // 'daily', 'monthly', 'yearly'
  const [reportCategory, setReportCategory] = useState('all'); // 'all', 'feeds', 'farms', 'medicines'
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeMenu, setActiveMenu] = useState(null);

  // Quick Links data
  const quickLinks = [
    {
      id: 'users-link',
      title: 'Users',
      icon: <Users className="w-5 h-5" />,
      link: '/admin/users',
      color: 'bg-blue-500',
    },
    {
      id: 'news-link',
      title: 'News',
      icon: <Newspaper className="w-5 h-5" />,
      link: '/admin/news',
      color: 'bg-green-500',
    },
    {
      id: 'services-link',
      title: 'Services',
      icon: <Package className="w-5 h-5" />,
      link: '/admin/services',
      color: 'bg-purple-500',
    },
    {
      id: 'farms-link',
      title: 'Farms',
      icon: <Tractor className="w-5 h-5" />,
      link: '/admin/farms',
      color: 'bg-orange-500',
    },
    {
      id: 'shops-link',
      title: 'Shops',
      icon: <ShoppingBag className="w-5 h-5" />,
      link: '/admin/shops',
      color: 'bg-indigo-500',
    },
    {
      id: 'chickens-link',
      title: 'Chickens',
      icon: <Bird className="w-5 h-5" />,
      link: '/admin/chickens',
      color: 'bg-yellow-500',
    },
    {
      id: 'feed-link',
      title: 'Feed',
      icon: <ShoppingBag className="w-5 h-5" />,
      link: '/admin/feed',
      color: 'bg-yellow-500',
    },
    {
      id: 'medicine-link',
      title: 'Medicine',
      icon: <Cross className="w-5 h-5" />,
      link: '/admin/medicine',
      color: 'bg-red-500',
    },
    {
      id: 'reports-link',
      title: 'Reports',
      icon: <FileText className="w-5 h-5" />,
      link: '/admin/reports',
      color: 'bg-indigo-500',
    },
    {
      id: 'settings-link',
      title: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      link: '/admin/settings',
      color: 'bg-gray-500',
    },
  ];

  // Mock reports data
  const reports = {
    daily: {
      feeds: [
        {
          id: 'feed-1',
          title: 'Daily Feed Consumption',
          date: '2023-03-15',
          type: 'PDF',
          size: '1.2 MB',
          category: 'feeds',
        },
        {
          id: 'feed-2',
          title: 'Feed Inventory Update',
          date: '2023-03-15',
          type: 'PDF',
          size: '0.8 MB',
          category: 'feeds',
        },
        {
          id: 'feed-3',
          title: 'Feed Distribution Report',
          date: '2023-03-15',
          type: 'PDF',
          size: '1.5 MB',
          category: 'feeds',
        },
      ],
      farms: [
        {
          id: 'farm-1',
          title: 'Daily Farm Activity',
          date: '2023-03-15',
          type: 'PDF',
          size: '1.1 MB',
          category: 'farms',
        },
        {
          id: 'farm-2',
          title: 'Farm Production Update',
          date: '2023-03-15',
          type: 'PDF',
          size: '1.3 MB',
          category: 'farms',
        },
        {
          id: 'farm-3',
          title: 'Farm Health Report',
          date: '2023-03-15',
          type: 'PDF',
          size: '0.9 MB',
          category: 'farms',
        },
      ],
      medicines: [
        {
          id: 'med-1',
          title: 'Daily Medicine Usage',
          date: '2023-03-15',
          type: 'PDF',
          size: '0.7 MB',
          category: 'medicines',
        },
        {
          id: 'med-2',
          title: 'Medicine Inventory Update',
          date: '2023-03-15',
          type: 'PDF',
          size: '0.6 MB',
          category: 'medicines',
        },
        {
          id: 'med-3',
          title: 'Medicine Distribution Report',
          date: '2023-03-15',
          type: 'PDF',
          size: '1.0 MB',
          category: 'medicines',
        },
      ],
    },
    monthly: {
      feeds: [
        {
          id: 'feed-4',
          title: 'Monthly Feed Consumption',
          date: 'March 2023',
          type: 'PDF',
          size: '2.5 MB',
          category: 'feeds',
        },
        {
          id: 'feed-5',
          title: 'Feed Inventory Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '2.0 MB',
          category: 'feeds',
        },
        {
          id: 'feed-6',
          title: 'Feed Distribution Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '3.0 MB',
          category: 'feeds',
        },
      ],
      farms: [
        {
          id: 'farm-4',
          title: 'Monthly Farm Activity',
          date: 'March 2023',
          type: 'PDF',
          size: '2.2 MB',
          category: 'farms',
        },
        {
          id: 'farm-5',
          title: 'Farm Production Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '2.8 MB',
          category: 'farms',
        },
        {
          id: 'farm-6',
          title: 'Farm Health Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '2.0 MB',
          category: 'farms',
        },
      ],
      medicines: [
        {
          id: 'med-4',
          title: 'Monthly Medicine Usage',
          date: 'March 2023',
          type: 'PDF',
          size: '1.8 MB',
          category: 'medicines',
        },
        {
          id: 'med-5',
          title: 'Medicine Inventory Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '1.5 MB',
          category: 'medicines',
        },
        {
          id: 'med-6',
          title: 'Medicine Distribution Monthly',
          date: 'March 2023',
          type: 'PDF',
          size: '2.2 MB',
          category: 'medicines',
        },
      ],
    },
    yearly: {
      feeds: [
        {
          id: 'feed-7',
          title: 'Annual Feed Consumption',
          date: '2022',
          type: 'PDF',
          size: '4.5 MB',
          category: 'feeds',
        },
        {
          id: 'feed-8',
          title: 'Feed Inventory Yearbook',
          date: '2022',
          type: 'PDF',
          size: '3.8 MB',
          category: 'feeds',
        },
        {
          id: 'feed-9',
          title: 'Feed Distribution Analysis',
          date: '2022',
          type: 'PDF',
          size: '5.2 MB',
          category: 'feeds',
        },
      ],
      farms: [
        {
          id: 'farm-7',
          title: 'Annual Farm Activity',
          date: '2022',
          type: 'PDF',
          size: '4.2 MB',
          category: 'farms',
        },
        {
          id: 'farm-8',
          title: 'Farm Production Yearbook',
          date: '2022',
          type: 'PDF',
          size: '4.8 MB',
          category: 'farms',
        },
        {
          id: 'farm-9',
          title: 'Farm Health Summary',
          date: '2022',
          type: 'PDF',
          size: '3.5 MB',
          category: 'farms',
        },
      ],
      medicines: [
        {
          id: 'med-7',
          title: 'Annual Medicine Usage',
          date: '2022',
          type: 'PDF',
          size: '3.2 MB',
          category: 'medicines',
        },
        {
          id: 'med-8',
          title: 'Medicine Inventory Yearbook',
          date: '2022',
          type: 'PDF',
          size: '2.9 MB',
          category: 'medicines',
        },
        {
          id: 'med-9',
          title: 'Medicine Distribution Analysis',
          date: '2022',
          type: 'PDF',
          size: '4.1 MB',
          category: 'medicines',
        },
      ],
    },
  };

  useEffect(() => {
    // Mock data for demonstration
    setStats({
      users: 1250,
      services: 48,
      news: 32,
      inquiries: 156,
      farms: 24,
      feed: 18,
      medicine: 12,
    });

    // Mock revenue data
    setRevenueData([
      { name: 'Jan', value: 4000 },
      { name: 'Feb', value: 3000 },
      { name: 'Mar', value: 5000 },
      { name: 'Apr', value: 4500 },
      { name: 'May', value: 6000 },
      { name: 'Jun', value: 5500 },
    ]);

    // Mock pie chart data
    setPieData([
      { name: 'Farms', value: 400 },
      { name: 'Feed', value: 300 },
      { name: 'Medicine', value: 300 },
      { name: 'Services', value: 200 },
    ]);

    // Mock profit data
    setProfitData([
      { name: 'Jan', profit: 5000, expenses: 3000 },
      { name: 'Feb', profit: 4000, expenses: 2500 },
      { name: 'Mar', profit: 6000, expenses: 3500 },
      { name: 'Apr', profit: 5500, expenses: 3200 },
      { name: 'May', profit: 7000, expenses: 4000 },
      { name: 'Jun', profit: 6500, expenses: 3800 },
    ]);

    // Mock consumption data
    setConsumptionData([
      { name: 'Jan', feeds: 800, medicines: 400, farms: 500 },
      { name: 'Feb', feeds: 900, medicines: 450, farms: 550 },
      { name: 'Mar', feeds: 850, medicines: 425, farms: 520 },
      { name: 'Apr', feeds: 950, medicines: 475, farms: 580 },
      { name: 'May', feeds: 920, medicines: 460, farms: 600 },
      { name: 'Jun', feeds: 1000, medicines: 500, farms: 620 },
    ]);

    // Mock inventory data
    setInventoryData([
      { name: 'Jan', feeds: 1000, medicines: 500, farms: 600 },
      { name: 'Feb', feeds: 1200, medicines: 600, farms: 650 },
      { name: 'Mar', feeds: 1100, medicines: 550, farms: 620 },
      { name: 'Apr', feeds: 1300, medicines: 650, farms: 680 },
      { name: 'May', feeds: 1250, medicines: 625, farms: 640 },
      { name: 'Jun', feeds: 1400, medicines: 700, farms: 660 },
    ]);
  }, []);

  const COLORS = ['#FF6B00', '#2C3E50', '#388E3C', '#D32F2F'];

  // Function to get current reports based on selected type and category
  const getCurrentReports = () => {
    let currentReports = [];

    if (reportCategory === 'all') {
      // Combine reports from all categories
      currentReports = [...reports[reportType].feeds, ...reports[reportType].farms, ...reports[reportType].medicines];
    } else {
      // Get reports for specific category
      currentReports = reports[reportType][reportCategory];
    }

    // Apply search filter if query exists
    if (searchQuery) {
      return currentReports.filter((report) => report.title.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    return currentReports;
  };

  // Function to get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'feeds':
        return <ShoppingBag className="w-4 h-4 text-yellow-500" />;
      case 'farms':
        return <Tractor className="w-4 h-4 text-green-500" />;
      case 'medicines':
        return <Cross className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  // Function to get category color
  const getCategoryColor = (category) => {
    switch (category) {
      case 'feeds':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'farms':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medicines':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';
    }
  };

  // Report action handlers
  const handleViewReport = (report) => {
    console.log(`Viewing report: ${report.title}`);

    // Create a modal to display the report
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">${report.title}</h3>
          <button id="close-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-4 overflow-auto max-h-[calc(90vh-4rem)]">
          <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mb-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Date</p>
                <p class="text-base text-gray-900 dark:text-white">${report.date}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</p>
                <p class="text-base text-gray-900 dark:text-white">${report.category.charAt(0).toUpperCase() + report.category.slice(1)}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</p>
                <p class="text-base text-gray-900 dark:text-white">${report.type}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Size</p>
                <p class="text-base text-gray-900 dark:text-white">${report.size}</p>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Report Content</h4>
            <p class="text-gray-700 dark:text-gray-300">
              This is a preview of the report content. In a real application, this would display the actual report data.
            </p>
            <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Sample data visualization would appear here.</p>
            </div>
          </div>
        </div>
        <div class="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <button id="download-from-modal" class="bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white px-4 py-2 rounded-md mr-2">
            Download
          </button>
          <button id="print-from-modal" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-md">
            Print
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    document.getElementById('close-modal').addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    document.getElementById('download-from-modal').addEventListener('click', () => {
      handleDownloadReport(report);
      document.body.removeChild(modal);
    });

    document.getElementById('print-from-modal').addEventListener('click', () => {
      handlePrintReport(report);
      document.body.removeChild(modal);
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  };

  const handleShareReport = (report) => {
    console.log(`Sharing report: ${report.title}`);

    // Create a share dialog
    const shareDialog = document.createElement('div');
    shareDialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    shareDialog.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
        <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Share Report</h3>
          <button id="close-share" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-4">
          <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">Share "${report.title}" with others</p>
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Addresses</label>
            <input type="text" id="share-email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white" placeholder="Enter email addresses (comma separated)">
          </div>
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Message (Optional)</label>
            <textarea id="share-message" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white" rows="3" placeholder="Add a message"></textarea>
          </div>
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Permissions</label>
            <select id="share-permissions" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
              <option value="view">View Only</option>
              <option value="edit">Can Edit</option>
              <option value="full">Full Access</option>
            </select>
          </div>
        </div>
        <div class="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <button id="cancel-share" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-md mr-2">
            Cancel
          </button>
          <button id="send-share" class="bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white px-4 py-2 rounded-md">
            Send
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(shareDialog);

    // Add event listeners
    document.getElementById('close-share').addEventListener('click', () => {
      document.body.removeChild(shareDialog);
    });

    document.getElementById('cancel-share').addEventListener('click', () => {
      document.body.removeChild(shareDialog);
    });

    document.getElementById('send-share').addEventListener('click', () => {
      const emails = document.getElementById('share-email').value;
      const message = document.getElementById('share-message').value;
      const permissions = document.getElementById('share-permissions').value;

      console.log(`Sharing report with: ${emails}, Message: ${message}, Permissions: ${permissions}`);

      // Show success message
      alert(`Report "${report.title}" has been shared successfully!`);

      document.body.removeChild(shareDialog);
    });

    // Close dialog when clicking outside
    shareDialog.addEventListener('click', (e) => {
      if (e.target === shareDialog) {
        document.body.removeChild(shareDialog);
      }
    });
  };

  const handlePrintReport = (report) => {
    console.log(`Printing report: ${report.title}`);

    // Create a print-friendly version of the report
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${report.title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #ddd;
              padding-bottom: 20px;
            }
            .report-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
              background-color: #f5f5f5;
              padding: 10px;
              border-radius: 4px;
            }
            .report-info div {
              margin-right: 20px;
            }
            .report-content {
              margin-top: 30px;
            }
            .footer {
              margin-top: 50px;
              text-align: center;
              font-size: 12px;
              color: #777;
            }
            @media print {
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${report.title}</h1>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
          </div>
          
          <div class="report-info">
            <div>
              <strong>Date:</strong> ${report.date}
            </div>
            <div>
              <strong>Category:</strong> ${report.category.charAt(0).toUpperCase() + report.category.slice(1)}
            </div>
            <div>
              <strong>Type:</strong> ${report.type}
            </div>
            <div>
              <strong>Size:</strong> ${report.size}
            </div>
          </div>
          
          <div class="report-content">
            <h2>Report Content</h2>
            <p>This is a sample report content. In a real application, this would contain the actual report data.</p>
            
            <h3>Summary</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            
            <h3>Details</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
            
            <h3>Recommendations</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
          </div>
          
          <div class="footer">
            <p>© ${new Date().getFullYear()} Farm Management System. All rights reserved.</p>
          </div>
          
          <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #FF6B00; color: white; border: none; border-radius: 4px; cursor: pointer;">
              Print Report
            </button>
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for content to load before printing
    printWindow.onload = function () {
      // Auto-print after a short delay
      setTimeout(() => {
        printWindow.print();
        // Close the window after printing (optional)
        // printWindow.close()
      }, 500);
    };
  };

  const handleDownloadReport = (report) => {
    console.log(`Downloading report: ${report.title}`);

    // Show download progress indicator
    const progressIndicator = document.createElement('div');
    progressIndicator.className =
      'fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-md z-50';
    progressIndicator.innerHTML = `
      <div class="flex items-center">
        <div class="mr-3">
          <svg class="animate-spin h-5 w-5 text-[#FF6B00]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-900 dark:text-white">Downloading ${report.title}</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Please wait...</p>
        </div>
      </div>
    `;

    document.body.appendChild(progressIndicator);

    // Simulate download process
    setTimeout(() => {
      // Create a blob with the report content
      const reportContent = `
        ${report.title}
        Date: ${report.date}
        Category: ${report.category}
        Type: ${report.type}
        Size: ${report.size}
        
        Report Content:
        This is a sample report content. In a real application, this would contain the actual report data.
        
        Summary:
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
        
        Details:
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
        
        Recommendations:
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
      `;

      const blob = new Blob([reportContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);

      // Create a download link
      const a = document.createElement('a');
      a.href = url;
      a.download = `${report.title.replace(/\s+/g, '_')}.txt`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Update progress indicator to show success
      progressIndicator.innerHTML = `
        <div class="flex items-center">
          <div class="mr-3 text-green-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">Download Complete</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">${report.title} has been downloaded</p>
          </div>
        </div>
      `;

      // Remove the progress indicator after a delay
      setTimeout(() => {
        document.body.removeChild(progressIndicator);
      }, 3000);
    }, 1500); // Simulate download time
  };

  // Add this function to handle menu clicks
  const handleMenuClick = (reportId, event) => {
    event.stopPropagation();
    setActiveMenu(activeMenu === reportId ? null : reportId);
  };

  // Add this function to handle clicks outside the menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.report-actions-menu')) {
        setActiveMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="h-full bg-gray-50 dark:bg-gray-900">
      {/* Dashboard Content */}
      <div className="p-2 sm:p-4 md:p-6">
        {/* Welcome Section */}
        <div className="mb-4 sm:mb-6">
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white font-heading">
            Dashboard Overview
          </h1>
          <p className="text-xs sm:text-sm md:text-base text-gray-500 dark:text-gray-400 font-sans">
            Welcome back! Here&apos;s what&apos;s happening with your farm management system.
          </p>
        </div>

        {/* Quick Links Section */}
        <div className="mb-4 sm:mb-6">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-8 gap-2 sm:gap-4">
            {quickLinks.map((link, index) => (
              <Link
                key={link.id}
                to={link.link}
                className={`${link.color} p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow group`}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="text-white mb-2 group-hover:scale-110 transition-transform">{link.icon}</div>
                  <span className="text-xs sm:text-sm font-medium text-white">{link.title}</span>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Alerts Section */}
        <div className="mb-4 sm:mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4">
            {alerts.map((alert) => (
              <div
                key={alert.id}
                className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg shadow-sm flex items-center"
              >
                <div className="mr-2 sm:mr-3">{alert.icon}</div>
                <p className="text-xs sm:text-sm text-gray-700 dark:text-gray-300">{alert.message}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Timeframe Selector */}
        {/* <div className="mb-4 sm:mb-6 flex flex-wrap justify-end gap-1 sm:gap-2">
          <Button
            variant={timeframe === 'daily' ? 'default' : 'outline'}
            onClick={() => setTimeframe('daily')}
            className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
          >
            Daily
          </Button>
          <Button
            variant={timeframe === 'monthly' ? 'default' : 'outline'}
            onClick={() => setTimeframe('monthly')}
            className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
          >
            Monthly
          </Button>
          <Button
            variant={timeframe === 'yearly' ? 'default' : 'outline'}
            onClick={() => setTimeframe('yearly')}
            className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
          >
            Yearly
          </Button>
        </div> */}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6">
          <Card className="bg-gradient-to-br from-primary/10 to-primary/5 border-primary/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.users}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-green-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>12% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-primary/10 rounded-full">
                  <Users className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Services</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.services}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-green-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>8% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-blue-500/10 rounded-full">
                  <Package className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-blue-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Farms</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.farms}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-green-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>15% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-green-500/10 rounded-full">
                  <Tractor className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-green-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-indigo-500/10 to-indigo-500/5 border-indigo-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Shops</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.shops}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-indigo-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>12% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-indigo-500/10 rounded-full">
                  <ShoppingBag className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-indigo-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-500/10 to-yellow-500/5 border-yellow-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Chickens</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.chickens}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-yellow-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>18% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-yellow-500/10 rounded-full">
                  <Bird className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-yellow-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-500/10 to-yellow-500/5 border-yellow-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Chickens</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.chickens}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-yellow-500">
                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>18% increase</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-yellow-500/10 rounded-full">
                  <Bird className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-yellow-500" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500/10 to-purple-500/5 border-purple-500/20 hover:shadow-lg transition-shadow">
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Total Feed</p>
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.feed}
                  </h3>
                  <div className="flex items-center mt-1 text-xs sm:text-sm text-red-500">
                    <ArrowDownRight className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                    <span>5% decrease</span>
                  </div>
                </div>
                <div className="p-2 sm:p-3 bg-purple-500/10 rounded-full">
                  <ShoppingBag className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-purple-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6">
          {/* Revenue Chart */}
          <Card className="col-span-1">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-sm sm:text-base">Revenue Overview</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="h-[200px] sm:h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="value" stroke="#8884d8" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Distribution Chart */}
          <Card className="col-span-1">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-sm sm:text-base">Resource Distribution</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="h-[200px] sm:h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${entry.id}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4 md:gap-6">
          {/* Profit & Expenses */}
          <Card className="col-span-1 sm:col-span-2 lg:col-span-1">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-sm sm:text-base">Profit & Expenses</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="h-[200px] sm:h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={profitData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="profit" fill="#4CAF50" />
                    <Bar dataKey="expenses" fill="#F44336" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Consumption Trends */}
          <Card className="col-span-1">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-sm sm:text-base">Consumption Trends</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="h-[200px] sm:h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={consumptionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="feeds" stroke="#FF6B00" strokeWidth={2} />
                    <Line type="monotone" dataKey="medicines" stroke="#388E3C" strokeWidth={2} />
                    <Line type="monotone" dataKey="farms" stroke="#1976D2" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Inventory Status */}
          <Card className="col-span-1">
            <CardHeader className="p-3 sm:p-4">
              <CardTitle className="text-sm sm:text-base">Inventory Status</CardTitle>
            </CardHeader>
            <CardContent className="p-2 sm:p-4">
              <div className="h-[200px] sm:h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={inventoryData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="feeds" fill="#FF6B00" />
                    <Bar dataKey="medicines" fill="#388E3C" />
                    <Bar dataKey="farms" fill="#1976D2" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reports Section */}
        <div className="mt-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <h2 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-0">Reports</h2>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={reportType === 'daily' ? 'default' : 'outline'}
                onClick={() => setReportType('daily')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
              >
                Daily
              </Button>
              <Button
                variant={reportType === 'monthly' ? 'default' : 'outline'}
                onClick={() => setReportType('monthly')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
              >
                Monthly
              </Button>
              <Button
                variant={reportType === 'yearly' ? 'default' : 'outline'}
                onClick={() => setReportType('yearly')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
              >
                Yearly
              </Button>
            </div>
          </div>

          {/* Report Filters */}
          <div className="bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg shadow-sm mb-4">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4">
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={reportCategory === 'all' ? 'default' : 'outline'}
                  onClick={() => setReportCategory('all')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                >
                  All
                </Button>
                <Button
                  variant={reportCategory === 'feeds' ? 'default' : 'outline'}
                  onClick={() => setReportCategory('feeds')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                >
                  Feeds
                </Button>
                <Button
                  variant={reportCategory === 'farms' ? 'default' : 'outline'}
                  onClick={() => setReportCategory('farms')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                >
                  Farms
                </Button>
                <Button
                  variant={reportCategory === 'medicines' ? 'default' : 'outline'}
                  onClick={() => setReportCategory('medicines')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                >
                  Medicines
                </Button>
              </div>
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <div className="relative flex-grow sm:flex-grow-0">
                  <input
                    type="text"
                    placeholder="Search reports..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full sm:w-64 px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  />
                  <Search className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
                <Button
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 sm:py-2"
                >
                  <Filter className="w-4 h-4 mr-1" />
                  Filters
                </Button>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date Range
                    </label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                      defaultValue="last7days"
                    >
                      <option value="today">Today</option>
                      <option value="last7days">Last 7 Days</option>
                      <option value="last30days">Last 30 Days</option>
                      <option value="custom">Custom Range</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Format</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                      defaultValue="all"
                    >
                      <option value="all">All Formats</option>
                      <option value="pdf">PDF</option>
                      <option value="excel">Excel</option>
                      <option value="csv">CSV</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sort By</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                      defaultValue="date-desc"
                    >
                      <option value="date-desc">Date (Newest)</option>
                      <option value="date-asc">Date (Oldest)</option>
                      <option value="name-asc">Name (A-Z)</option>
                      <option value="name-desc">Name (Z-A)</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Reports Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 sm:px-6 py-3 text-left text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Report
                    </th>
                    <th
                      scope="col"
                      className="px-3 sm:px-6 py-3 text-left text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      className="px-3 sm:px-6 py-3 text-left text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Category
                    </th>
                    <th
                      scope="col"
                      className="px-3 sm:px-6 py-3 text-left text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Size
                    </th>
                    <th
                      scope="col"
                      className="px-3 sm:px-6 py-3 text-right text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {getCurrentReports().length > 0 ? (
                    getCurrentReports().map((report) => (
                      <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-3 sm:px-6 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                              <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </div>
                            <div className="ml-3">
                              <div className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white">
                                {report.title}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">{report.type}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-3 sm:px-6 py-3 whitespace-nowrap text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                          {report.date}
                        </td>
                        <td className="px-3 sm:px-6 py-3 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getCategoryColor(report.category)}`}
                          >
                            {getCategoryIcon(report.category)}
                            <span className="ml-1">
                              {report.category.charAt(0).toUpperCase() + report.category.slice(1)}
                            </span>
                          </span>
                        </td>
                        <td className="px-3 sm:px-6 py-3 whitespace-nowrap text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                          {report.size}
                        </td>
                        <td className="px-3 sm:px-6 py-3 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                          <div className="relative report-actions-menu">
                            <button
                              onClick={(e) => handleMenuClick(report.id, e)}
                              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1"
                              title="Actions"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </button>
                            {activeMenu === report.id && (
                              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10">
                                <button
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                  onClick={() => {
                                    handleViewReport(report);
                                    setActiveMenu(null);
                                  }}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  View
                                </button>
                                <button
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                  onClick={() => {
                                    handleShareReport(report);
                                    setActiveMenu(null);
                                  }}
                                >
                                  <Share2 className="h-4 w-4 mr-2" />
                                  Share
                                </button>
                                <button
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                  onClick={() => {
                                    handlePrintReport(report);
                                    setActiveMenu(null);
                                  }}
                                >
                                  <Printer className="h-4 w-4 mr-2" />
                                  Print
                                </button>
                                <button
                                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                  onClick={() => {
                                    handleDownloadReport(report);
                                    setActiveMenu(null);
                                  }}
                                >
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </button>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan="5"
                        className="px-3 sm:px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        No reports found matching your criteria.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Generate Report Form */}
          <div className="mt-4 bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg shadow-sm">
            <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white mb-3">
              Generate Custom Report
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Report Type
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                  aria-label="Report Type"
                >
                  <option value="daily">Daily Report</option>
                  <option value="monthly">Monthly Report</option>
                  <option value="yearly">Yearly Report</option>
                </select>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                  aria-label="Category"
                >
                  <option value="all">All Categories</option>
                  <option value="feeds">Feeds</option>
                  <option value="farms">Farms</option>
                  <option value="medicines">Medicines</option>
                </select>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Date Range
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                  aria-label="Date Range"
                >
                  <option value="today">Today</option>
                  <option value="last7days">Last 7 Days</option>
                  <option value="last30days">Last 30 Days</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Format
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#FF6B00] focus:border-transparent"
                  aria-label="Format"
                >
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
            </div>
            <div className="mt-3 flex justify-end">
              <Button
                className="bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white"
                onClick={() => {
                  // Get form values
                  const reportTypeSelect = document.querySelector('select[aria-label="Report Type"]');
                  const categorySelect = document.querySelector('select[aria-label="Category"]');
                  const dateRangeSelect = document.querySelector('select[aria-label="Date Range"]');
                  const formatSelect = document.querySelector('select[aria-label="Format"]');

                  const reportType = reportTypeSelect ? reportTypeSelect.value : 'daily';
                  const category = categorySelect ? categorySelect.value : 'all';
                  const dateRange = dateRangeSelect ? dateRangeSelect.value : 'today';
                  const format = formatSelect ? formatSelect.value : 'pdf';

                  console.log(
                    `Generating custom report: Type=${reportType}, Category=${category}, DateRange=${dateRange}, Format=${format}`
                  );

                  // Show generation progress indicator
                  const progressIndicator = document.createElement('div');
                  progressIndicator.className =
                    'fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-md z-50';
                  progressIndicator.innerHTML = `
                    <div class="flex items-center">
                      <div class="mr-3">
                        <svg class="animate-spin h-5 w-5 text-[#FF6B00]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Generating Report</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Please wait...</p>
                      </div>
                    </div>
                  `;

                  document.body.appendChild(progressIndicator);

                  // Simulate report generation process
                  setTimeout(() => {
                    // Create a mock report
                    const reportTitle = `${category.charAt(0).toUpperCase() + category.slice(1)} ${reportType} Report`;
                    const reportDate = new Date().toLocaleDateString();
                    const reportSize = `${(Math.random() * 2 + 0.5).toFixed(1)} MB`;

                    // Update progress indicator to show success
                    progressIndicator.innerHTML = `
                      <div class="flex items-center">
                        <div class="mr-3 text-green-500">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <p class="text-sm font-medium text-gray-900 dark:text-white">Report Generated</p>
                          <p class="text-xs text-gray-500 dark:text-gray-400">${reportTitle} is ready</p>
                        </div>
                      </div>
                    `;

                    // Add download button
                    const downloadButton = document.createElement('button');
                    downloadButton.className =
                      'mt-2 w-full bg-[#FF6B00] hover:bg-[#FF6B00]/90 text-white px-3 py-1 rounded-md text-xs';
                    downloadButton.textContent = 'Download Report';
                    downloadButton.onclick = () => {
                      // Create a blob with the report content
                      const reportContent = `
                        ${reportTitle}
                        Date: ${reportDate}
                        Category: ${category}
                        Type: ${reportType}
                        Format: ${format}
                        Size: ${reportSize}
                        
                        Report Content:
                        This is a custom generated report based on your selected criteria.
                        
                        Summary:
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
                        
                        Details:
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
                        
                        Recommendations:
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
                      `;

                      const blob = new Blob([reportContent], {
                        type: 'text/plain',
                      });
                      const url = URL.createObjectURL(blob);

                      // Create a download link
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${reportTitle.replace(/\s+/g, '_')}.${format.toLowerCase()}`;
                      document.body.appendChild(a);
                      a.click();

                      // Clean up
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);

                      // Remove the progress indicator
                      document.body.removeChild(progressIndicator);
                    };

                    progressIndicator.appendChild(downloadButton);

                    // Remove the progress indicator after a delay if not downloaded
                    setTimeout(() => {
                      if (document.body.contains(progressIndicator)) {
                        document.body.removeChild(progressIndicator);
                      }
                    }, 10000);
                  }, 2000); // Simulate generation time
                }}
              >
                Generate Report
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
