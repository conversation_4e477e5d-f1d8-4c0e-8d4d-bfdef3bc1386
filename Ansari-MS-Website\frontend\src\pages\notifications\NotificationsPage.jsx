/* eslint-disable react/jsx-key */
import { useState } from 'react';
import { Card, List, Button, Space, Tag, Badge, Empty, Tabs, Switch, Divider, Popconfirm } from 'antd';
import { BellOutlined, CheckOutlined, DeleteOutlined, SettingOutlined, InboxOutlined } from '@ant-design/icons';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';

const NotificationsPage = () => {
  const { language, translations } = useLanguage();
  const {
    notifications,
    notificationSettings,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    archiveNotification,
    updateNotificationSettings,
  } = useNotifications();

  const [activeTab, setActiveTab] = useState('all');
  const [showSettings, setShowSettings] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'low_stock':
        return <Tag color="red">{t('low_stock')}</Tag>;
      case 'expiry_alert':
        return <Tag color="orange">{t('expiry_alert')}</Tag>;
      case 'new_order':
        return <Tag color="blue">{t('new_order')}</Tag>;
      case 'new_user':
        return <Tag color="green">{t('new_user')}</Tag>;
      case 'report_generated':
        return <Tag color="purple">{t('report_generated')}</Tag>;
      default:
        return <BellOutlined />;
    }
  };

  const filteredNotifications = notifications.filter((notification) => {
    if (activeTab === 'all') return !notification.archived;
    if (activeTab === 'unread') return !notification.read && !notification.archived;
    if (activeTab === 'archived') return notification.archived;
    return notification.category === activeTab && !notification.archived;
  });

  const handleSettingsChange = (key, value) => {
    updateNotificationSettings({ [key]: value });
  };

  const handleCategoryChange = (category, value) => {
    updateNotificationSettings({
      categories: {
        ...notificationSettings.categories,
        [category]: value,
      },
    });
  };

  const tabItems = [
    {
      key: 'all',
      label: t('all'),
    },
    {
      key: 'unread',
      label: t('unread'),
    },
    {
      key: 'archived',
      label: t('archived'),
    },
    ...Object.keys(notificationSettings.categories).map((category) => ({
      key: category,
      label: t(category),
    })),
  ];

  return (
    <div className="notifications-page">
      <Card
        title={
          <Space>
            <BellOutlined />
            {t('notifications')}
            <Badge count={notifications.filter((n) => !n.read && !n.archived).length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={markAllAsRead}
              disabled={!notifications.some((n) => !n.read && !n.archived)}
            >
              {t('mark_all_read')}
            </Button>
            <Button icon={<SettingOutlined />} onClick={() => setShowSettings(!showSettings)}>
              {t('settings')}
            </Button>
            <Popconfirm
              title={t('clear_all_confirm')}
              onConfirm={clearAllNotifications}
              okText={t('yes')}
              cancelText={t('no')}
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                {t('clear_all')}
              </Button>
            </Popconfirm>
          </Space>
        }
      >
        {showSettings && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-4">{t('notification_settings')}</h3>
            <Space direction="vertical" size="large" className="w-full">
              <div>
                <h4 className="font-medium mb-2">{t('notification_channels')}</h4>
                <Space direction="vertical">
                  <Switch
                    checked={notificationSettings.email}
                    onChange={(checked) => handleSettingsChange('email', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('email_notifications')}</span>
                </Space>
                <Space direction="vertical" className="ml-4">
                  <Switch
                    checked={notificationSettings.browser}
                    onChange={(checked) => handleSettingsChange('browser', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('browser_notifications')}</span>
                </Space>
                <Space direction="vertical" className="ml-4">
                  <Switch
                    checked={notificationSettings.sound}
                    onChange={(checked) => handleSettingsChange('sound', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('sound_notifications')}</span>
                </Space>
              </div>
              <Divider />
              <div>
                <h4 className="font-medium mb-2">{t('notification_categories')}</h4>
                <Space direction="vertical">
                  {Object.entries(notificationSettings.categories).map(([category, enabled]) => (
                    <Space key={category}>
                      <Switch
                        checked={enabled}
                        onChange={(checked) => handleCategoryChange(category, checked)}
                        checkedChildren={t('enabled')}
                        unCheckedChildren={t('disabled')}
                      />
                      <span>{t(category)}</span>
                    </Space>
                  ))}
                </Space>
              </div>
            </Space>
          </div>
        )}

        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />

        {filteredNotifications.length === 0 ? (
          <Empty image={<InboxOutlined style={{ fontSize: 48 }} />} description={t('no_notifications')} />
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={filteredNotifications}
            renderItem={(notification) => (
              <List.Item
                actions={[
                  !notification.read && (
                    <Button type="link" onClick={() => markAsRead(notification.id)}>
                      {t('mark_read')}
                    </Button>
                  ),
                  <Button type="link" danger onClick={() => clearNotification(notification.id)}>
                    {t('delete')}
                  </Button>,
                  <Button type="link" onClick={() => archiveNotification(notification.id)}>
                    {t('archive')}
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.type)}
                  title={notification.title}
                  description={notification.message}
                />
              </List.Item>
            )}
          />
        )}
      </Card>
    </div>
  );
};

export default NotificationsPage;
