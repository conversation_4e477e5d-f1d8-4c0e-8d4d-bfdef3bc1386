import { useState } from 'react';
import { More<PERSON><PERSON><PERSON>, <PERSON>cil, Trash2, XCircle } from 'lucide-react';
import Button from '../Button';
import { Progress } from '../management-system/ui/Progress';

export const DataTable = ({
  columns,
  data,
  onEdit,
  onDelete,
  onDisable,
  loading,
  error,
  emptyMessage = 'No data available',
}) => {
  const [activeMenu, setActiveMenu] = useState(null);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    );
  }

  if (error) {
    return <div className="flex items-center justify-center h-64 text-red-500">{error}</div>;
  }

  if (!data || data.length === 0) {
    return <div className="flex items-center justify-center h-64 text-gray-500">{emptyMessage}</div>;
  }

  return (
    <div className="overflow-x-auto shadow-md rounded-lg">
      <table className="w-full text-sm text-gray-700 border-collapse">
        <thead className="bg-gray-200">
          <tr>
            {columns.map((column) => (
              <th key={column.key} className="px-6 py-3 text-left" style={{ width: column.width }}>
                {column.header}
              </th>
            ))}
            <th className="px-6 py-3 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item) => (
            <tr
              key={item.id}
              className={`border-b transition-all duration-300 ${
                item.disabled ? 'bg-gray-100 opacity-50' : 'bg-white hover:bg-gray-50'
              }`}
            >
              {columns.map((column) => (
                <td key={column.key} className="px-6 py-4">
                  {column.render ? (
                    column.render(item[column.key], item)
                  ) : (
                    <div className="text-sm text-gray-900 dark:text-white">{item[column.key]}</div>
                  )}
                </td>
              ))}
              <td className="px-4 py-2">
                <div className="relative">
                  <Button
                    variant="dotsbtn"
                    onClick={() => setActiveMenu(activeMenu === item.id ? null : item.id)}
                    className="hover:bg-gray-200 p-2 rounded-full"
                  >
                    <MoreVertical className="h-5 w-5 text-gray-500" />
                  </Button>

                  {activeMenu === item.id && (
                    <div className="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md z-10">
                      <div className="py-2">
                        {onEdit && (
                          <Button
                            variant="dotsbtn"
                            onClick={() => {
                              onEdit(item.id);
                              setActiveMenu(null);
                            }}
                            className="w-full flex px-4 py-2 gap-1 hover:bg-blue-100"
                          >
                            <Pencil className="h-4 w-4 mr-2 text-blue-500" />
                            Edit
                          </Button>
                        )}
                        {onDelete && (
                          <Button
                            variant="dotsbtn"
                            onClick={() => {
                              onDelete(item.id);
                              setActiveMenu(null);
                            }}
                            className="w-full px-4 py-2 flex gap-1 hover:bg-red-100"
                          >
                            <Trash2 className="h-4 w-4 mr-2 text-red-500" />
                            Delete
                          </Button>
                        )}
                        {onDisable && (
                          <Button
                            variant="dotsbtn"
                            onClick={() => {
                              onDisable(item.id);
                              setActiveMenu(null);
                            }}
                            className="w-full px-4 flex py-2 gap-1 hover:bg-gray-100"
                          >
                            <XCircle className="h-4 w-4 mr-2 text-gray-500" />
                            {item.disabled ? 'Enable' : 'Disable'}
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
