import React from 'react';
import Input from './Input';

const Form = ({ children, onSubmit, className = '', ...props }) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
  };

  return (
    <form onSubmit={handleSubmit} className={`space-y-6 ${className}`} {...props}>
      {children}
    </form>
  );
};

Form.Field = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  error,
  helperText,
  required = false,
  placeholder,
  className = '',
  ...props
}) => {
  return (
    <div className={className}>
      <Input
        label={label}
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        error={error}
        helperText={helperText}
        required={required}
        placeholder={placeholder}
        {...props}
      />
    </div>
  );
};

Form.Group = ({ children, className = '', ...props }) => {
  return (
    <div className={`space-y-4 ${className}`} {...props}>
      {children}
    </div>
  );
};

Form.Actions = ({ children, className = '', ...props }) => {
  return (
    <div className={`flex justify-end gap-3 ${className}`} {...props}>
      {children}
    </div>
  );
};

export default Form;
