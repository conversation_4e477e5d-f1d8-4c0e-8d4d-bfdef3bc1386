import <PERSON><PERSON> from "joi";
import asyncHand<PERSON> from "../middlewares/asyncHandler.js";
import shopModel from "../models/shopModel.js";

const shopSchema = Joi.object({
  SName: Joi.string().min(3).max(80).required(),
  SOwner: Joi.string().required(),
  SLocation: Joi.string().required(),
  SEmail: Joi.string().email().required(),
  SPhone: Joi.string()
    .pattern(/^((\+93)|0)?7[0-9]{8}$/)
    .required(),
  userId: Joi.number().required(),
});

const shopController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = shopSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    // 🔍 First check by email
    const emailExists = await shopModel.findByEmail(value.SEmail);
    if (emailExists)
      return res.status(400).json({
        success: false,
        error: "This email is already used by another shop",
      });

    // 🔍 Then check by phone
    const phoneExists = await shopModel.findByPhone(value.SPhone);
    if (phoneExists)
      return res.status(400).json({
        success: false,
        error: "This phone number is already used by another shop",
      });

    const shop = await shopModel.create(value);
    res
      .status(201)
      .json({ success: true, message: "Shop created", data: shop });
  }),

  getAll: asyncHandler(async (req, res) => {
    const shops = await shopModel.getAll();
    res.json({ success: true, total: shops.length, data: shops });
  }),

  getById: asyncHandler(async (req, res) => {
    const shop = await shopModel.getById(req.params.id);
    if (!shop)
      return res.status(404).json({ success: false, error: "Shop not found" });
    res.json({ success: true, data: shop });
  }),

  update: asyncHandler(async (req, res) => {
    const existing = await shopModel.getById(req.params.id);
    if (!existing) {
      return res.status(404).json({ success: false, error: "Shop not found" });
    }

    const updatedData = {
      SName: req.body.SName ?? existing.SName,
      SOwner: req.body.SOwner ?? existing.SOwner,
      SLocation: req.body.SLocation ?? existing.SLocation,
      SEmail: req.body.SEmail ?? existing.SEmail,
      SPhone: req.body.SPhone ?? existing.SPhone,
      userId: req.body.userId ?? existing.userId,
    };

    const { error } = shopSchema.validate(updatedData);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    // 🔍 First check email
    const emailExists = await shopModel.findByEmail(updatedData.SEmail);
    if (emailExists && emailExists.SId !== parseInt(req.params.id)) {
      return res.status(400).json({
        success: false,
        error: "This email is already used by another shop",
      });
    }

    // 🔍 Then check phone
    const phoneExists = await shopModel.findByPhone(updatedData.SPhone);
    if (phoneExists && phoneExists.SId !== parseInt(req.params.id)) {
      return res.status(400).json({
        success: false,
        error: "This phone number is already used by another shop",
      });
    }

    const updated = await shopModel.update(req.params.id, updatedData);
    res.json({
      success: true,
      message: "Shop updated successfully",
      shop: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await shopModel.delete(req.params.id);
    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, error: "Shop not found" });

    res.json({ success: true, message: "Shop deleted" });
  }),
};

export default shopController;
