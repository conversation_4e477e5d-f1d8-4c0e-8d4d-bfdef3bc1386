'use client';

import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { FarmProvider } from './contexts/FarmContext';
import { ShopProvider } from './contexts/ShopContext';
import { ChickenProvider } from './contexts/ChickenContext';
import { ServicesProvider } from './contexts/ServicesContext';
import { NewsProvider } from './contexts/NewsContext';
import { MedicineProvider } from './contexts/MedicineContext';
import { FeedProvider } from './contexts/FeedContext';
import { UsersProvider } from './contexts/UsersContext';
import { ReportsProvider } from './contexts/ReportsContext';
import { Suspense, lazy, useState } from 'react';
import { LanguageProvider } from './contexts/LanguageContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { ReportProvider } from './contexts/ReportContext';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingSpinner from './components/LoadingSpinner';
import { ProtectedRoute } from './routes/protected-route';


import PublicLayout from './layouts/PublicLayout';
import FarmerLayout from './layouts/FarmerLayout';
import ShopperLayout from './layouts/ShopperLayout';
import AdminLayout from './layouts/PrivateLayout';
// Lazy load layouts
// const PublicLayout = () => import('./layouts/PublicLayout');
// const AdminLayout = () => import('./layouts/PrivateLayout');
// const FarmerLayout = () => import('./layouts/FarmerLayout'); // You'll need to create this
// const ShopperLayout = () => import('./layouts/ShopperLayout'); // You'll need to create this

// Auth => Login, Register, EmailVerification, VerifyCode, ForgotPassword,Reset Password, Reset Confirmaiton
const Login = lazy(() => import('./pages/auth/Login'));
const Register = lazy(() => import('./pages/auth/Register'));
const EmailVerification = lazy(() => import('./pages/auth/EmailVarification'));
const VarifyCode = lazy(() => import('./pages/auth/VarifyCode'));
const ForgotPassword = lazy(() => import('./pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/auth/ResetPassword'));
const ResetConfirmation = lazy(() => import('./pages/auth/ResetConfirmation'));
// Lazy load public pages
import Home from './pages/public/Home';
import About from './pages/public/About';
import Services from './pages/public/Services';
import ServiceDetails from './pages/public/ServiceDetails';
import News from './pages/public/News';
import NewsDetail from './pages/public/NewsDetail';
import ChatPage from './pages/public/ChatPage';
import NotFound from './pages/public/NotFound';
import Contact from './pages/public/Contact';
// const Home = lazy(() => import('./pages/public/Home'));
// const About = lazy(() => import('./pages/public/About'));
// const Services = lazy(() => import('./pages/public/Services'));
// const ServiceDetails = lazy(() => import('./pages/public/ServiceDetails'));
// const Contact = lazy(() => import('./pages/public/Contact'));
// const News = lazy(() => import('./pages/public/News'));
// const NewsDetail = lazy(() => import('./pages/public/NewsDetail'));
// const ChatPage = lazy(() => import('./pages/public/ChatPage'));
// const NotFound = lazy(() => import('./pages/public/NotFound'));

// Lazy load admin pages
const Dashboard = lazy(() => import('./pages/dashboard/admin/Dashboard'));
const ManageNews = lazy(() => import('./pages/news/ManageNews'));
const AddNews = lazy(() => import('./pages/news/AddNews'));
const EditNews = lazy(() => import('./pages/news/EditNews'));
const ManageServices = lazy(() => import('./pages/services/ManageServices'));
const AddService = lazy(() => import('./pages/services/AddService'));
const EditService = lazy(() => import('./pages/services/EditService'));
const ManageUsers = lazy(() => import('./pages/users/ManageUsers'));
const AddUser = lazy(() => import('./pages/users/AddUser'));
const EditUser = lazy(() => import('./pages/users/EditUser'));
const Settings = lazy(() => import('./pages/settings/Settings'));
const Profile = lazy(() => import('./pages/profile/Profile'));
const FarmsPage = lazy(() => import('./pages/farms/FarmsPage'));
const AddFarmPage = lazy(() => import('./pages/farms/AddFarmPage'));
const EditFarmPage = lazy(() => import('./pages/farms/EditFarmPage'));
const FarmDetails = lazy(() => import('./pages/farms/FarmDetails'));
const ShopsPage = lazy(() => import('./pages/shops/ShopsPage'));
const AddShopPage = lazy(() => import('./pages/shops/AddShopPage'));
const EditShopPage = lazy(() => import('./pages/shops/EditShopPage'));
const ChickenDashboard = lazy(() => import('./pages/chickens/ChickenDashboard'));
const PurchasesPage = lazy(() => import('./pages/chickens/PurchasesPage'));
const AddPurchasePage = lazy(() => import('./pages/chickens/AddPurchasePage'));
const AllocationsPage = lazy(() => import('./pages/chickens/AllocationsPage'));
const AddAllocationPage = lazy(() => import('./pages/chickens/AddAllocationPage'));
const BuybacksPage = lazy(() => import('./pages/chickens/BuybacksPage'));
const AddBuybackPage = lazy(() => import('./pages/chickens/AddBuybackPage'));
const DistributionsPage = lazy(() => import('./pages/chickens/DistributionsPage'));
const AddDistributionPage = lazy(() => import('./pages/chickens/AddDistributionPage'));
const ChickenProcessReportPage = lazy(() => import('./pages/chickens/ChickenProcessReportPage'));
const SeedsPage = lazy(() => import('./pages/feed/SeedsPage'));
const AddSeedPage = lazy(() => import('./pages/feed/AddSeedPage'));
const EditSeedPage = lazy(() => import('./pages/feed/EditSeedPage'));
const MedicinePage = lazy(() => import('./pages/medicine/MedicinePage'));
const AddMedicinePage = lazy(() => import('./pages/medicine/AddMedicinePage'));
const EditFeed = lazy(() => import('./pages/feed/EditFeed'));
const EditMedicine = lazy(() => import('./pages/medicine/EditMedicinePage'));
const ReportsPage = lazy(() => import('./pages/reports/ReportsPage'));
const NotificationsPage = lazy(() => import('./pages/notifications/NotificationsPage'));
const InventoryPage = lazy(() => import('./pages/inventory/InventoryPage'));

// Farmer Dashboard (placeholder - you'll need to create these components)
const FarmerDashboard = lazy(() => import('./pages/dashboard/farmer/FarmerDashboard'));

// Shopper Dashboard (placeholder - you'll need to create these components)
const ShopperDashboard = lazy(() => import('./pages/dashboard/shop/ShopperDashboard'));

function AppRoutes() {
  const { isAuthenticated, user } = useAuth();
  const [medicines, setMedicines] = useState([
    {
      id: 1,
      farm: 'Ahmad Farm',
      location: 'Panjwai',
      medicine: 85,
      value: 21250,
      percentage: 85,
    },
    {
      id: 2,
      farm: 'Mahmood Farm',
      location: 'Kandahar',
      medicine: 70,
      value: 17500,
      percentage: 70,
    },
    {
      id: 3,
      farm: 'Zahir Farm',
      location: 'Arghandab',
      medicine: 60,
      value: 15000,
      percentage: 60,
    },
    {
      id: 4,
      farm: 'Noor Farm',
      location: 'Dand',
      medicine: 55,
      value: 13750,
      percentage: 55,
    },
  ]);



  return (
    <ErrorBoundary>
      <ServicesProvider>
        <NewsProvider>
          <FarmProvider>
            <FeedProvider>
              <MedicineProvider>
                <UsersProvider>
                  <ReportsProvider>
                    
                      <Routes>
                        {/* Public Routes */}
                        <Route
                          path="/"
                          element={
                            <Suspense fallback={<LoadingSpinner />}>
                              <PublicLayout />
                            </Suspense>
                          }
                        >
                          <Route
                            index
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <Home />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          <Route
                            path="about"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <About />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          <Route
                            path="services"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <Services />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          {/* Updated service details route to handle string IDs */}
                          <Route
                            path="services/:id"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <ServiceDetails />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          <Route
                            path="news"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <News />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          <Route
                            path="news/:id"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <NewsDetail />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                          <Route
                            path="contact"
                            element={
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <Contact />
                                </Suspense>
                              </ErrorBoundary>
                            }
                          />
                        </Route>

                        {/* Auth Routes */}
                        <Route
                          path="/signin"
                          element={
                            isAuthenticated ? (
                              <Navigate
                                to={
                                  user?.role === 'admin'
                                    ? '/admin'
                                    : user?.role === 'farmer'
                                      ? '/farmer'
                                      : user?.role === 'shopper'
                                        ? '/shopper'
                                        : '/'
                                }
                                replace
                              />
                            ) : (
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <Login />
                                </Suspense>
                              </ErrorBoundary>
                            )
                          }
                        />
                        <Route
                          path="/signup"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <Register />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                        <Route
                          path="/verify-email"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <EmailVerification />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                        <Route
                          path="/verify-code"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <VarifyCode />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                        <Route
                          path="/forgot-password"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <ForgotPassword />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                        <Route
                          path="/reset-password/:id"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <ResetPassword />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                        <Route
                          path="/reset-confirmation"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <ResetConfirmation />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />

                        {/* Admin Routes - Protected for admin role only */}
                        <Route
                          path="/admin"
                          element={
                            <ProtectedRoute allowedRoles={['admin']}>
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <AdminLayout>
                                    <Outlet />
                                  </AdminLayout>
                                </Suspense>
                              </ErrorBoundary>
                            </ProtectedRoute>
                          }
                        >
                          <Route index element={<Dashboard />} />
                          <Route path="news" element={<ManageNews />} />
                          <Route path="news/add" element={<AddNews />} />
                          <Route path="news/edit/:id" element={<EditNews />} />
                          <Route path="services" element={<ManageServices />} />
                          <Route path="services/add" element={<AddService />} />
                          <Route path="services/edit/:id" element={<EditService />} />
                          <Route path="users" element={<ManageUsers />} />
                          <Route path="users/add" element={<AddUser />} />
                          <Route path="users/edit/:id" element={<EditUser />} />
                          <Route
                            path="/admin/farms"
                            element={
                              <FarmProvider>
                                <FarmsPage />
                              </FarmProvider>
                            }
                          />
                          <Route path="/admin/farms" element={<FarmsPage />} />
                          <Route path="/admin/farms/add" element={<AddFarmPage />} />
                          <Route path="/admin/farms/edit/:id" element={<EditFarmPage />} />
                          <Route path="/admin/farms/:id" element={<FarmDetails />} />

                          {/* Shop Routes */}
                          <Route path="/admin/shops" element={<ShopsPage />} />
                          <Route path="/admin/shops/add" element={<AddShopPage />} />
                          <Route path="/admin/shops/edit/:id" element={<EditShopPage />} />

                          {/* Chicken Routes */}
                          <Route path="/admin/chickens" element={<ChickenDashboard />} />
                          <Route path="/admin/chickens/purchases" element={<PurchasesPage />} />
                          <Route path="/admin/chickens/purchases/add" element={<AddPurchasePage />} />
                          <Route path="/admin/chickens/allocations" element={<AllocationsPage />} />
                          <Route path="/admin/chickens/allocations/add" element={<AddAllocationPage />} />
                          <Route path="/admin/chickens/buybacks" element={<BuybacksPage />} />
                          <Route path="/admin/chickens/buybacks/add" element={<AddBuybackPage />} />
                          <Route path="/admin/chickens/distributions" element={<DistributionsPage />} />
                          <Route path="/admin/chickens/distributions/add" element={<AddDistributionPage />} />
                          <Route path="/admin/chickens/reports" element={<ChickenProcessReportPage />} />

                          {/* Seeds Routes */}
                          <Route path="/admin/feed" element={<SeedsPage />} />
                          <Route path="/admin/seeds/add" element={<AddSeedPage />} />
                          <Route path="/admin/seeds/edit/:id" element={<EditSeedPage />} />

                          {/* Medicine Routes */}
                          <Route path="/admin/medicine" element={<MedicinePage />} />
                          <Route path="/admin/medicine/add" element={<AddMedicinePage />} />
                          <Route
                            path="/admin/medicine/edit/:id"
                            element={<EditMedicine medicines={medicines} setMedicines={setMedicines} />}
                          />

                          <Route path="settings" element={<Settings />} />
                          <Route path="profile" element={<Profile />} />
                          <Route path="/admin/chat" element={<ChatPage />} />
                          <Route path="/admin/inventory" element={<InventoryPage />} />
                          <Route path="/admin/reports" element={<ReportsPage />} />
                          <Route path="/admin/notifications" element={<NotificationsPage />} />
                        </Route>

                        {/* Farmer Routes - Protected for farmer role only */}
                        <Route
                          path="/farmer"
                          element={
                            <ProtectedRoute allowedRoles={['farmer']}>
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <FarmerLayout>
                                    <Outlet />
                                  </FarmerLayout>
                                </Suspense>
                              </ErrorBoundary>
                            </ProtectedRoute>
                          }
                        >
                          <Route index element={<FarmerDashboard />} />
                          <Route path="chat" element={<ChatPage />} />
                          {/* <Route path='settings' element={<Settings />} /> */}
                          {/* Add more farmer-specific routes here */}
                        </Route>

                        {/* Shopper Routes - Protected for shopper role only */}
                        <Route
                          path="/shopper"
                          element={
                            <ProtectedRoute allowedRoles={['shopper']}>
                              <ErrorBoundary>
                                <Suspense fallback={<LoadingSpinner />}>
                                  <ShopperLayout>
                                    <Outlet />
                                  </ShopperLayout>
                                </Suspense>
                              </ErrorBoundary>
                            </ProtectedRoute>
                          }
                        >
                          <Route index element={<ShopperDashboard />} />
                          <Route path="chat" element={<ChatPage />} />
                          {/* Add more shopper-specific routes here */}
                        </Route>

                        {/* 404 Route */}
                        <Route
                          path="*"
                          element={
                            <ErrorBoundary>
                              <Suspense fallback={<LoadingSpinner />}>
                                <NotFound />
                              </Suspense>
                            </ErrorBoundary>
                          }
                        />
                      </Routes>
               
                  </ReportsProvider>
                </UsersProvider>
              </MedicineProvider>
            </FeedProvider>
          </FarmProvider>
        </NewsProvider>
      </ServicesProvider>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <Router>
      <LanguageProvider>
        <NotificationProvider>
          <ReportProvider>
            <AuthProvider>
              <ServicesProvider>
                <NewsProvider>
                  <FarmProvider>
                    <ShopProvider>
                      <ChickenProvider>
                        <FeedProvider>
                        <MedicineProvider>
                          <UsersProvider>
                            <ReportsProvider>
                            <ErrorBoundary>
                              <AppRoutes />
                            </ErrorBoundary>
                          </ReportsProvider>
                        </UsersProvider>
                      </MedicineProvider>
                    </FeedProvider>
                  </ChickenProvider>
                </ShopProvider>
              </FarmProvider>
                </NewsProvider>
              </ServicesProvider>
            </AuthProvider>
          </ReportProvider>
        </NotificationProvider>
      </LanguageProvider>
    </Router>
  );
}

export default App;
