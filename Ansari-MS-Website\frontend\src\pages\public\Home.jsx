/* eslint-disable react/prop-types */
'use client';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useState, useEffect, useCallback } from 'react';
import { ChevronRight, ArrowRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import axios from 'axios';

import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import TestimonialCard from '../../components/public/TestimonialCard';
import RatingForm from '../../components/public/RatingForm';
import Button from '../../components/Button';
import StatCard from '../../components/public/statCard'
import LazyImage from '../../components/LazyImage';

function Home() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  // Custom arrow components for the slider
  const NextArrow = (props) => {
    const { style, onClick } = props;
    return (
      <div
        className="z-10 w-6 md:w-10 h-6 md:h-10 flex items-center justify-center bg-primary backdrop-blur-sm rounded-full text-white absolute top-1/2 -right-7 md:-right-9 -translate-y-1/2 shadow-lg border border-primary/20 hover:bg-white hover:text-primary transition-all duration-300 cursor-pointer"
        style={{ ...style, display: 'flex' }}
        onClick={onClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M9 18l6-6-6-6" />
        </svg>
      </div>
    );
  };

  const PrevArrow = (props) => {
    const { style, onClick } = props;
    return (
      <div
        className="z-10 w-6 md:w-10 h-6 md:h-10 flex items-center justify-center bg-primary backdrop-blur-sm rounded-full text-white absolute top-1/2 -left-7 md:-left-9 -translate-y-1/2 shadow-lg border border-primary/20 hover:bg-white hover:text-primary transition-all duration-300 cursor-pointer"
        style={{ ...style, display: 'flex' }}
        onClick={onClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M15 18l-6-6 6-6" />
        </svg>
      </div>
    );
  };

  // Slider settings
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3500,
    arrows: true,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    responsive: [
      { breakpoint: 1024, settings: { slidesToShow: 3 } },
      { breakpoint: 768, settings: { slidesToShow: 2 } },
      { breakpoint: 640, settings: { slidesToShow: 1 } },
    ],
  };

  const [testimonials, setTestimonials] = useState([]);
  const [userHasReviewed, setUserHasReviewed] = useState(false);

  // Create a function to check if user has already reviewed
  const checkUserReview = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get('http://localhost:5432/api/v1/reviews/user', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data && response.data.hasReviewed) {
        setUserHasReviewed(true);
      }
    } catch (error) {
      console.error('Error checking user review status:', error);
    }
  }, []);

  // Use the InitialTestimonails component to fetch testimonials
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await axios.get('http://localhost:5432/api/v1/reviews/');
        const reviews = response.data.data;
        const totalReviews = response.data.totalReviews;

        if (totalReviews === 0) {
          setTestimonials([
            {
              id: 1,
              name: 'John Doe',
              review: 'The quality of the chickens is outstanding! Fresh, tender, and delicious.',
              rating: 5,
              avatarUrl: './imgs/avatar-placeholder.jpg',
            },
            {
              id: 2,
              name: 'Jane Smith',
              review: "I've been a customer for years and have never been disappointed. Highly recommend!",
              rating: 5,
              avatarUrl: './imgs/avatar-placeholder.jpg',
            },
            {
              id: 3,
              name: 'Robert Johnson',
              review: 'The service is excellent and the chickens are raised ethically. What more could you ask for?',
              rating: 4,
              avatarUrl: './imgs/avatar-placeholder.jpg',
            },
          ]);
          return; // Exit early to avoid overwriting
        }

        const formattedTestimonials = reviews.map((review) => ({
          id: review.R_Id,
          name: review.fullName || `${review.U_FirstName} ${review.U_LastName}`,
          review: review.R_Message,
          rating: review.R_Number,
          avatarUrl: `http://localhost:5432/public/images/users/${review.image}`,
        }));

        setTestimonials(formattedTestimonials);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setTestimonials([
          {
            id: 1,
            name: 'John Doe',
            review: 'The quality of the chickens is outstanding! Fresh, tender, and delicious.',
            rating: 5,
            avatarUrl: './imgs/avatar-placeholder.jpg',
          },
          {
            id: 2,
            name: 'Jane Smith',
            review: "I've been a customer for years and have never been disappointed. Highly recommend!",
            rating: 5,
            avatarUrl: './imgs/avatar-placeholder.jpg',
          },
          {
            id: 3,
            name: 'Robert Johnson',
            review: 'The service is excellent and the chickens are raised ethically. What more could you ask for?',
            rating: 4,
            avatarUrl: './imgs/avatar-placeholder.jpg',
          },
        ]);
      }
    };

    fetchTestimonials();
    checkUserReview(); // Check if user has already reviewed
  }, [checkUserReview]);

  // Update the handleFormSubmit function to include email
  const handleFormSubmit = (formData, rating, avatarUrl) => {
    // Add the new testimonial to the existing list of testimonials
    const newTestimonial = {
      id: testimonials.length + 1, // Incremental ID (or you could use a unique ID generation method)
      name: formData.name,
      review: formData.message,
      rating,
      avatarUrl,
      // You can store the email if needed for future use
      email: formData.email,
    };

    setTestimonials((prevTestimonials) => [...prevTestimonials, newTestimonial]);

    // Set that user has reviewed
    setUserHasReviewed(true);

    // Scroll to testimonials section
    const testimonialSection = document.querySelector('#testimonials');
    if (testimonialSection) {
      testimonialSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleClick = () => {
    navigate('/contact');
  };

  const handleMore = () => {
    navigate('/services');
  };

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  const staggerContainer = {
    // hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const dir = i18n.language === 'ps' ? 'rtl' : 'ltr';
  useEffect(() => {
    document.documentElement.setAttribute('dir', dir);
    document.documentElement.setAttribute('lang', i18n.language);
  }, []);

    const stats = t('home.expert.stats', { returnObjects: true });

  return (
    <div className="min-h-screen  bg-white">
      {/* Hero Section - Modern with Overlay */}
      <section className="relative h-[90vh] top-16 flex items-center justify-center overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0">
          <img src="./imgs/home.jpg" alt="Farm background" className="w-full h-full" />
          {/* <LazyImage src='./imgs/home.jpg' alt="Farm background" className="w-full h-full object-cover" /> */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#2c3e5073]/90 to-[#33333344]/80 backdrop-blur-0"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold  text-white mb-6 leading-tight">
              {t('home.hero.title')} <span className="text-primary">{t('home.hero.title1')}</span>
            </h1>
            <p className="text-base sm:text-xl  md:text-2xl text-white/90 max-w-3xl mx-auto mb-4 md:leading-relaxed">
              {t('home.hero.subtitle')}
            </p>
            <p className="text-base sm:text-xl  md:text-2xl text-white/90 max-w-3xl mx-auto mb-10 md:leading-relaxed">
              {t('home.hero.subtitle1')}
            </p>

            <div className="flex  items-center gap-2 md:gap-6 justify-center mt-8">
              <Button onClick={handleClick} variant="primary" className="flex items-center rtl:flex-row-reverse ">
                {t('home.hero.cws')}
                <ArrowRight className="w-5 h-5 " />
                {/* {dir === 'ps' ? (
                  <ArrowLeft className="w-5 h-5 " />
                ) : (
                )} */}
              </Button>
              <Button onClick={handleMore} variant="secondary">
                {t('home.hero.cta')}
              </Button>
            </div>
          </motion.div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
            className="w-8 h-14 rounded-full border-2 border-white/50 flex items-center justify-center"
          >
            <motion.div
              animate={{ y: [0, 8, 0] }}
              transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
              className="w-2 h-2 bg-white rounded-full"
            />
          </motion.div>
        </div>
      </section>

      {/* About Our Chickens Section - Card-based design */}
      <section className="relative top-16 py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="text-primary font-semibold text-lg  uppercase tracking-wider">
              {t('home.about.title')}
            </span>
            <h2 className="text-4xl md:text-5xl font-bold  text-[#333333] mt-2">{t('home.about.heading')}</h2>
            <div className="w-2/5 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {/* <img src="./imgs/baby-chickens.jpg" alt="Farm baby chickens" 
              className="w-full h-auto  z-10 relative" /> */}
               <LazyImage src="./imgs/baby-chickens.jpg" alt="Farm baby chickens" 
              className="w-full h-auto  z-10 relative" />
              <div className="absolute -bottom-8 -right-3 w-32 h-32 bg-primary rounded-2xl -z-10"></div>
              <div className="absolute -top-8 -left-3 w-32 h-32 border-2 border-[#2C3E50] rounded-2xl -z-10"></div>
            </motion.div>

            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h3 className="text-3xl font-bold  text-[#333333]">{t('home.about.subheading')}</h3>
              <p className="text-lg  text-[#333333]/80 leading-relaxed">{t('home.about.paragraph1')}</p>
              <p className="text-lg  text-[#333333]/80 leading-relaxed">{t('home.about.paragraph2')}</p>

              <ul className="space-y-3">
                {t('home.about.features', { returnObjects: true }).map((item, index) => (
                  <motion.li
                    key={index}
                    className="flex items-center gap-3 text-lg text-[#333333]"
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                  >
                    <div className="w-6 h-6 rounded-full bg-[#388E3C] flex items-center justify-center text-white">
                      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M10 3L4.5 8.5L2 6"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    {item}
                  </motion.li>
                ))}
              </ul>

              <Button onClick={handleMore} className="flex rtl:flex-row-reverse" variant="primary">
                {t('home.about.discover')}
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Hens Section - Modern with statistics */}
      <section className="relative top-16 py-20 px-4 bg-gradient-to-b from-[#EBE8E5] to-[#ffffff]">
        <div className="max-w-6xl mx-auto bg-gradient-to-b from-[#EBE8E5] to-[#ffffff]">
          {/* Section Heading */}
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
            }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="text-primary font-semibold text-lg uppercase tracking-wider">
              {t('home.expert.badge')}
            </span>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#333333] mt-2">
              {t('home.expert.title')}
            </h2>
            <div className="w-2/4 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

          {/* Responsive Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <motion.div
              className="space-y-6 order-2 md:order-1"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              // transition={{ duration: 0.8,delay: 0.2 }}
            >
              <h3 className="text-2xl sm:text-3xl font-bold text-[#333333]">
                {t('home.expert.heading')}
              </h3>
              <p className="text-base sm:text-lg text-[#333333]/80 leading-relaxed">
                {t('home.expert.paragraph1')}
              </p>
              <p className="text-base sm:text-lg text-[#333333]/80 leading-relaxed">
                {t('home.expert.paragraph2')}
              </p>

              {/* Animated Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
                {stats.map((stat, index) => (
                  <StatCard
                    key={index}
                    targetValue={stat.value}
                    label={stat.label}
                    lang={i18n.language} // enables Pashto-specific styles
                  />
                ))}
              </div>
              <Button variant="primary" className='flex rtl:flex-row-reverse' onClick={handleMore}>
                {t('home.expert.lmah')}
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </motion.div>

            {/* Image Section */}
            <motion.div
              className="relative order-1 md:order-2 flex justify-center"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="relative w-full max-w-md md:max-w-lg">
                {/* <img
                  src="./imgs/hens-illustration-1.jpeg"
                  alt={t('home.expert.imageAlt')}
                  className="w-full h-auto relative"
                /> */}
                <LazyImage src="./imgs/hens-illustration-1.jpeg"
                  alt={t('home.expert.imageAlt')}
                  className="w-full h-auto relative" loading='lazy' />
                <div className="absolute -bottom-8 -left-3  w-24 h-24 sm:w-32 sm:h-32 bg-destructive rounded-2xl -z-10"></div>
                <div className="absolute -top-8 -right-3 w-24 h-24 sm:w-32 sm:h-32 border-2 border-[#2C3E50] rounded-2xl -z-10"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section - Modern cards with hover effects */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          {/* Section Heading */}
          <motion.div
            className="text-center mb-16 px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary font-semibold text-lg md:text-xl uppercase tracking-wide">{t('home.services.badge')}</span>
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-bold  text-[#333333] mt-2 leading-tight">
              {t('home.services.title')}
            </h2>
            <div className="w-2/4 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
            <p className="text-sm sm:text-base md:text-lg text-foreground/70  max-w-3xl mx-auto mt-6">
              {t('home.services.paragraph')}
            </p>
          </motion.div>

          {/* Swiper Carousel */}
          <motion.div
            className="w-full px-4 md:px-10"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.35 }}
          >
            <Slider {...settings}>
              {t('home.services.items',{returnObjects:true}).map((item, index) => (
                <div key={index} className="p-4">
                  <motion.div
                    className="bg-white rounded-2xl border overflow-hidden flex flex-col h-[500px] w-full transition-all duration-300 hover:-translate-y-2 hover:shadow-sm"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.35 }}
                  >
                    {/* Image Section */}
                    <div className="relative w-full h-[50%] overflow-hidden">
                      <img
                        src={item.img || '/placeholder.svg'}
                        alt={item.title}
                        className="w-full h-full transition-transform duration-700 group-hover:scale-110"
                      />
                      {/* <LazyImage   src={item.img || '/placeholder.svg'}
                        alt={item.title}
                        className="w-full h-full transition-transform duration-700 group-hover:scale-110" /> */}
                      <div className="absolute inset-0 bg-gradient-to-t from-[#2C3E50]/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    {/* Text Section */}
                    <div className="p-6 flex flex-col flex-grow">
                      <h4 className="text-lg md:text-xl font-bold font-['Poppins',sans-serif] text-[#333333] mb-3 group-hover:text-primary transition-colors duration-300">
                        {item.title}
                      </h4>
                      <p className="text-[#333333]/70 mb-4 flex-grow text-sm sm:text-base md:text-lg leading-relaxed">
                        {item.description}
                      </p>
                      <div className="flex items-center justify-center">
                        <Button variant="primary" className='flex '>
                          {t('home.services.button')}
                          <ChevronRight className="w-5 h-5" />
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                </div>
              ))}
            </Slider>
          </motion.div>
        </div>
      </section>

      {/* Features Section - Modern with icons */}
      <section className="relative top-16 py-20 px-4 bg-gradient-to-b from-[#2C3E50] to-[#1a2530]">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary text-lg  uppercase tracking-wider">{t('home.whyChooseUs.headingSmall')}</span>
            <h2 className="text-4xl md:text-5xl font-bold  text-white mt-2">{t('home.whyChooseUs.headingMain')}</h2>
            <div className="w-2/4 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

          <motion.div
            className="grid md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-2xl bg-[#388E3C] flex items-center justify-center mb-6">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12 6V12L16 14M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white  mb-4">{t('home.whyChooseUs.premiumHensTitle')}</h3>
              <p className="text-white/80  leading-relaxed">
               {t('home.whyChooseUs.premiumHensDesc')}
              </p>
            </motion.div>

            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-2xl bg-primary flex items-center justify-center mb-6">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white  mb-4">{t('home.whyChooseUs.babyChickensTitle')}</h3>
              <p className="text-white/80  leading-relaxed">
               {t('home.whyChooseUs.babyChickensDesc')}
              </p>
            </motion.div>

            <motion.div
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-2"
              variants={fadeIn}
            >
              <div className="w-16 h-16 rounded-2xl bg-destructive flex items-center justify-center mb-6">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M16 18L18 20L22 16M12 15H7C5.89543 15 5 14.1046 5 13V7C5 5.89543 5.89543 5 7 5H17C18.1046 5 19 5.89543 19 7V12M7 9H17M7 13H12"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <h3 className="text-xl  font-bold text-white mb-4">{t('home.whyChooseUs.wholesaleTitle')}</h3>
              <p className="text-white/80  leading-relaxed">
               {t('home.whyChooseUs.wholesaleDesc')}
              </p>
            </motion.div>
          </motion.div>

          <div className="text-center flex items-center justify-center mt-12">
            <Button variant="secondary" onClick={handleClick}>
              {t('home.whyChooseUs.contactUs')}
            </Button>
          </div>
        </div>
      </section>

      {/* Customer Satisfaction Section - Modern testimonials */}
      <section id="testimonials" className="relative top-16 py-20 px-4 bg-muted">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-destructive font-semibold text-lg  uppercase tracking-wider">{t('home.testimonialsSection.headingSmall')}</span>
            <h2 className="text-4xl md:text-5xl font-bold   text-[#333333] mt-2">{t('home.testimonialsSection.headingMain')}</h2>
            <div className="w-2/4 h-1 bg-destructive mx-auto mt-6 rounded-full"></div>
            <p className="text-lg text-[#333333]/70 max-w-3xl  mx-auto mt-6">
              {t('home.testimonialsSection.description')}
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                variants={fadeIn}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.35, delay: index * 0.2 }}
              >
                <TestimonialCard
                  name={testimonial.name}
                  review={testimonial.review}
                  rating={testimonial.rating}
                  avatarUrl={testimonial.avatarUrl}
                />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Feedback Section - Modern form design */}
      <section className="relative top-16 py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary font-semibold text-lg uppercase  tracking-wider">{t('home.review.feedbackSection.headingSmall')}</span>
            <h2 className="text-4xl md:text-5xl font-bold  text-[#333333] mt-2">{t('home.review.feedbackSection.headingMain')}</h2>
            <div className="w-2/4 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
            <p className="text-lg text-[#333333]/70 max-w-3xl  mx-auto mt-6">
             {t('home.review.feedbackSection.description')}
            </p>
          </motion.div>

          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.35 }}
          >
            <div className="bg-white p-8 md:p-10 rounded-md shadow-sm border border-[#EEEEEE]">
              {userHasReviewed ? (
                <div className="text-center p-6 bg-success/20 rounded-lg border border-amber-100">
                  <h3 className="text-xl font-bold text-success mb-2">Thank You!</h3>
                  <p className="text-success">You have already submitted a review. We appreciate your feedback!</p>
                </div>
              ) : (
                <RatingForm onSubmit={handleFormSubmit} />
              )}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

export default Home;
