import express from "express";

import {
  authenticate,
  authorizeShoper<PERSON>nd<PERSON><PERSON><PERSON>,
  authorizeAdmin,
} from "../middlewares/authMiddleware.js";

import {
  uploadChatImage,
  resizeChatImage,
} from "../middlewares/chatImageMiddleware.js";

import Chat<PERSON>ontroller from "../controllers/chatController.js";

const router = express.Router();

// Add these in the correct order for the `/send` route
router.post(
  "/send",
  authenticate,
  authorizeShoperAndFarmer,
  uploadChatImage,
  resizeChatImage,
  ChatController.sendMessage,
);

// Create a new chat room (either between a user and admin)
router.post(
  "/start",
  authenticate,
  authorize<PERSON>hoperAnd<PERSON>armer,
  ChatController.startChat,
);

// Get messages from a chat room
router.get(
  "/:room_id/messages",
  authenticate,
  authorizeShoperAndFarmer,
  ChatController.getMessages,
);

// Delete a message
router.delete(
  "/messages/:messageId",
  authenticate,
  authorize<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChatController.deleteMessage,
);

// Edit a message
router.put(
  "/messages/:messageId",
  authenticate,
  authorizeShoperAndFarmer,
  ChatController.editMessage,
);

// Get all admin <-> farmer chat rooms
router.get(
  "/admin/farmer-chats",
  authenticate,
  authorizeAdmin,
  ChatController.getAdminChatsWithFarmers,
);

// Get all admin <-> shopper chat rooms
router.get(
  "/admin/shopper-chats",
  authenticate,
  authorizeAdmin,
  ChatController.getAdminChatsWithShoppers,
);

// Send message to all farmers
router.post(
  "/admin/send-farmer",
  authenticate,
  authorizeAdmin,
  ChatController.sendMessageToAllFarmers,
);

// Send message to all shoppers
router.post(
  "/admin/send-shopper",
  authenticate,
  authorizeAdmin,
  ChatController.sendMessageToAllShoppers,
);

export default router;
