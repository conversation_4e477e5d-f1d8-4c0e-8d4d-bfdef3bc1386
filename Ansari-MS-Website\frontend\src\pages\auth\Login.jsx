'use client';

import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Eye, EyeOff, LogIn, Mail, Lock, CheckCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await axios.post('http://localhost:5432/api/v1/users/login', {
        U_Email: formData.email,
        password: formData.password,
      });

      if (response.data.success) {
        const token = response.data.token;
        const { u_Id, U_FirstName, U_Email, Role, image, U_LastName } = response.data.user;

        // Create user object with all necessary data
        const userData = {
          id: u_Id,
          firstName: U_FirstName,
          lastName: U_LastName,
          email: U_Email,
          role: Role,
          image,
        };

        // Use the login function from AuthContext to store token and user data
        login(token, userData);

        // Redirect based on user role
        redirectBasedOnRole(Role);
      } else {
        if (response.data.error && response.data.error.includes('Your email is not verified')) {
          setError('Your email is not verified. Redirecting to verify page...');
          localStorage.setItem('registeredEmail', formData.email);

          // Wait 3 seconds before navigating
          setTimeout(() => {
            navigate('/verify-email');
          }, 3000);
        } else {
          setError(response.data.error || 'Login failed.');
        }
      }
    } catch (err) {
      if (err.response) {
        const errorMsg =
          err.response?.data?.error || err.response?.data?.message || 'An error occurred. Please try again.';

        if (errorMsg.includes('Your email is not verified')) {
          setError('Your email is not verified. Redirecting to verify page...');
          localStorage.setItem('registeredEmail', formData.email);

          // Wait 3 seconds before navigating
          setTimeout(() => {
            navigate('/verify-email');
          }, 3000);
        } else if (errorMsg.includes('must be a valid email')) {
          setError('Please enter a valid email address.');
        } else {
          setError(errorMsg);
        }
      } else {
        setError('An error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to handle redirection based on user role
  const redirectBasedOnRole = (role) => {
    switch (role.toLowerCase()) {
      case 'admin':
        navigate('/admin');
        break;
      case 'farmer':
        navigate('/admin'); // Change this to "/farmer" when you have a farmer dashboard
        break;
      case 'shopper':
        navigate('/admin'); // Change this to "/shopper" when you have a shopper dashboard
        break;
      default:
        navigate('/'); // Regular users go to home page
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-muted">
      {/* Left side - Form */}
      <div className="w-full md:w-1/2 px-6 py-8 md:px-12 md:py-12 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <div className="mb-8">
            <h1 className="text-3xl font-bold font-heading text-text-primary">Welcome Back</h1>
            <p className="mt-2 text-muted-foreground">Sign in to access your account</p>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mb-6 bg-destructive/10 border border-destructive/20 rounded-xl p-4"
            >
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-destructive" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-destructive font-medium">{error}</p>
                </div>
              </div>
            </motion.div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <label htmlFor="email" className="block text-sm font-medium text-card-foreground mb-1">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-4 w-4 text-muted-foreground/60" />
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="pl-10 w-full py-3 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                  placeholder="<EMAIL>"
                />
              </div>
            </motion.div>

            {/* Password Field */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-1">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-4 w-4 text-muted-foreground/60" />
                </div>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="pl-10 w-full py-3 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                  )}
                </button>
              </div>
            </motion.div>

            {/* Remember Me & Forgot Password */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="flex items-center justify-between"
            >
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary focus:ring-primary/50 border-border rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-muted-foreground">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <Link to="/forgot-password" className="text-primary hover:text-primary-hover font-medium">
                  Forgot your password?
                </Link>
              </div>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <button
                type="submit"
                disabled={loading}
                className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg text-primary-foreground font-medium
                  bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50
                  transition-colors duration-200 shadow-md ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {loading ? (
                  <div className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Signing in...
                  </div>
                ) : (
                  <div className="flex items-center">
                    Sign In
                    <LogIn className="ml-2 h-5 w-5" />
                  </div>
                )}
              </button>
            </motion.div>

            {/* Sign Up Link */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="text-center"
            >
              <p className="text-sm text-muted-foreground">
                Don&apos;t have an account?{' '}
                <Link to="/signup" className="text-primary hover:text-primary-hover font-medium">
                  Sign up
                </Link>
              </p>
            </motion.div>
          </form>
        </motion.div>
      </div>

      {/* Right side - Image/Illustration */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="hidden md:block md:w-1/2 bg-gradient-to-br from-primary to-primary-hover"
      >
        <div className="h-full flex flex-col justify-center items-center text-primary-foreground p-12">
          <div className="max-w-md">
            <h2 className="text-3xl font-bold font-heading mb-6">Welcome Back!</h2>
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Access your personalized dashboard and manage your account</p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Track your orders and view your purchase history</p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Connect with our community and access exclusive content</p>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Login;

////////////////////////////////////////////////////
// "use client"

// import { useState } from "react"
// import { Link, useNavigate } from "react-router-dom"
// import { motion } from "framer-motion"
// import { Eye, EyeOff, LogIn, Mail, Lock, CheckCircle } from "lucide-react"
// // import { useAuth } from "../contexts/AuthContext"
// import axios from "axios"

// const Login = () => {
//   const [formData, setFormData] = useState({
//     email: "",
//     password: "",
//   })
//   const [showPassword, setShowPassword] = useState(false)
//   const [error, setError] = useState("")
//   const [loading, setLoading] = useState(false)
//   const navigate = useNavigate()
//   // const { updateUserData } = useAuth()

//   const handleChange = (e) => {
//     const { name, value } = e.target
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))
//   }

//   const togglePasswordVisibility = () => {
//     setShowPassword(!showPassword)
//   }

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setError("");
//     setLoading(true);

//     try {
//       const response = await axios.post("http://localhost:5432/api/v1/users/login", {
//         U_Email: formData.email,
//         password: formData.password,
//       });

//       if (response.data.success) {
//         localStorage.setItem("token", response.data.token);

//         const { u_Id, U_FirstName, U_Email, Role, image } = response.data.user;
//         const user = {
//           id: u_Id,
//           firstName: U_FirstName,
//           email: U_Email,
//           role: Role,
//           image,
//         };

//         localStorage.setItem("user", JSON.stringify(user));
//   console.log(user)
//         if (user.role === "admin") {
//           navigate("/admin");
//         } else if(user.role==='farmer') {
//           navigate("/farmer");
//         } else if(user.role==='shopper'){
//           navigate('/shopper')
//         }else {
//           navigate('/')
//         }
//       } else {
//         if (response.data.error && response.data.error.includes("Your email is not verified")) {
//           setError("Your email is not verified. Redirecting to verify page...");
//           localStorage.setItem("registeredEmail", formData.email);

//           // Wait 3 seconds before navigating
//           setTimeout(() => {
//             navigate("/verify-email");
//           }, 3000);
//         } else {
//           setError(response.data.error || "Login failed.");
//         }
//       }
//     } catch (err) {
//       if (err.response) {
//         const errorMsg =
//           err.response?.data?.error || err.response?.data?.message || "An error occurred. Please try again.";

//         if (errorMsg.includes("Your email is not verified")) {
//           setError("Your email is not verified. Redirecting to verify page...");
//           localStorage.setItem("registeredEmail", formData.email);

//           // Wait 3 seconds before navigating
//           setTimeout(() => {
//             navigate("/verify-email");
//           }, 3000);
//         } else if (errorMsg.includes("must be a valid email")) {
//           setError("Please enter a valid email address.");
//         } else {
//           setError(errorMsg);
//         }
//       } else {
//         setError("An error occurred. Please try again.");
//       }
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen flex flex-col md:flex-row bg-muted">
//       {/* Left side - Form */}
//       <div className="w-full md:w-1/2 px-6 py-8 md:px-12 md:py-12 flex items-center justify-center">
//         <motion.div
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.5 }}
//           className="w-full max-w-md"
//         >
//           <div className="mb-8">
//             <h1 className="text-3xl font-bold font-heading text-text-primary">Welcome Back</h1>
//             <p className="mt-2 text-muted-foreground">Sign in to access your account</p>
//           </div>

//           {error && (
//             <motion.div
//               initial={{ opacity: 0, height: 0 }}
//               animate={{ opacity: 1, height: "auto" }}
//               className="mb-6 bg-destructive/10 border border-destructive/20 rounded-xl p-4"
//             >
//               <div className="flex">
//                 <div className="flex-shrink-0">
//                   <svg className="h-5 w-5 text-destructive" viewBox="0 0 20 20" fill="currentColor">
//                     <path
//                       fillRule="evenodd"
//                       d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
//                       clipRule="evenodd"
//                     />
//                   </svg>
//                 </div>
//                 <div className="ml-3">
//                   <p className="text-sm text-destructive font-medium">{error}</p>
//                 </div>
//               </div>
//             </motion.div>
//           )}

//           <form onSubmit={handleSubmit} className="space-y-6">
//             {/* Email Field */}
//             <motion.div
//               initial={{ opacity: 0, y: 10 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.1 }}
//             >
//               <label htmlFor="email" className="block text-sm font-medium text-card-foreground mb-1">
//                 Email Address
//               </label>
//               <div className="relative">
//                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                   <Mail className="h-4 w-4 text-muted-foreground/60" />
//                 </div>
//                 <input
//                   type="email"
//                   id="email"
//                   name="email"
//                   value={formData.email}
//                   onChange={handleChange}
//                   required
//                   className="pl-10 w-full py-3 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
//                   placeholder="<EMAIL>"
//                 />
//               </div>
//             </motion.div>

//             {/* Password Field */}
//             <motion.div
//               initial={{ opacity: 0, y: 10 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.2 }}
//             >
//               <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-1">
//                 Password
//               </label>
//               <div className="relative">
//                 <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
//                   <Lock className="h-4 w-4 text-muted-foreground/60" />
//                 </div>
//                 <input
//                   type={showPassword ? "text" : "password"}
//                   id="password"
//                   name="password"
//                   value={formData.password}
//                   onChange={handleChange}
//                   required
//                   className="pl-10 w-full py-3 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
//                   placeholder="••••••••"
//                 />
//                 <button
//                   type="button"
//                   onClick={togglePasswordVisibility}
//                   className="absolute inset-y-0 right-0 pr-3 flex items-center"
//                 >
//                   {showPassword ? (
//                     <EyeOff className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
//                   ) : (
//                     <Eye className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
//                   )}
//                 </button>
//               </div>
//             </motion.div>

//             {/* Remember Me & Forgot Password */}
//             <motion.div
//               initial={{ opacity: 0, y: 10 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.3 }}
//               className="flex items-center justify-between"
//             >
//               <div className="flex items-center">
//                 <input
//                   id="remember-me"
//                   name="remember-me"
//                   type="checkbox"
//                   className="h-4 w-4 text-primary focus:ring-primary/50 border-border rounded"
//                 />
//                 <label htmlFor="remember-me" className="ml-2 block text-sm text-muted-foreground">
//                   Remember me
//                 </label>
//               </div>
//               <div className="text-sm">
//                 <Link to="/forgot-password" className="text-primary hover:text-primary-hover font-medium">
//                   Forgot your password?
//                 </Link>
//               </div>
//             </motion.div>

//             {/* Submit Button */}
//             <motion.div
//               initial={{ opacity: 0, y: 10 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.4 }}
//             >
//               <button
//                 type="submit"
//                 disabled={loading}
//                 className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg text-primary-foreground font-medium
//                   bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50
//                   transition-colors duration-200 shadow-md ${loading ? "opacity-70 cursor-not-allowed" : ""}`}
//               >
//                 {loading ? (
//                   <div className="flex items-center">
//                     <svg
//                       className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
//                       xmlns="http://www.w3.org/2000/svg"
//                       fill="none"
//                       viewBox="0 0 24 24"
//                     >
//                       <circle
//                         className="opacity-25"
//                         cx="12"
//                         cy="12"
//                         r="10"
//                         stroke="currentColor"
//                         strokeWidth="4"
//                       ></circle>
//                       <path
//                         className="opacity-75"
//                         fill="currentColor"
//                         d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                       ></path>
//                     </svg>
//                     Signing in...
//                   </div>
//                 ) : (
//                   <div className="flex items-center">
//                     Sign In
//                     <LogIn className="ml-2 h-5 w-5" />
//                   </div>
//                 )}
//               </button>
//             </motion.div>

//             {/* Sign Up Link */}
//             <motion.div
//               initial={{ opacity: 0, y: 10 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ duration: 0.3, delay: 0.5 }}
//               className="text-center"
//             >
//               <p className="text-sm text-muted-foreground">
//                 Don&apos;t have an account?{" "}
//                 <Link to="/signup" className="text-primary hover:text-primary-hover font-medium">
//                   Sign up
//                 </Link>
//               </p>
//             </motion.div>
//           </form>
//         </motion.div>
//       </div>

//       {/* Right side - Image/Illustration */}
//       <motion.div
//         initial={{ opacity: 0, x: 20 }}
//         animate={{ opacity: 1, x: 0 }}
//         transition={{ duration: 0.5, delay: 0.2 }}
//         className="hidden md:block md:w-1/2 bg-gradient-to-br from-primary to-primary-hover"
//       >
//         <div className="h-full flex flex-col justify-center items-center text-primary-foreground p-12">
//           <div className="max-w-md">
//             <h2 className="text-3xl font-bold font-heading mb-6">Welcome Back!</h2>
//             <div className="space-y-6">
//               <motion.div
//                 initial={{ opacity: 0, x: 20 }}
//                 animate={{ opacity: 1, x: 0 }}
//                 transition={{ duration: 0.4, delay: 0.3 }}
//                 className="flex items-start"
//               >
//                 <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
//                 <p>Access your personalized dashboard and manage your account</p>
//               </motion.div>
//               <motion.div
//                 initial={{ opacity: 0, x: 20 }}
//                 animate={{ opacity: 1, x: 0 }}
//                 transition={{ duration: 0.4, delay: 0.4 }}
//                 className="flex items-start"
//               >
//                 <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
//                 <p>Track your orders and view your purchase history</p>
//               </motion.div>
//               <motion.div
//                 initial={{ opacity: 0, x: 20 }}
//                 animate={{ opacity: 1, x: 0 }}
//                 transition={{ duration: 0.4, delay: 0.5 }}
//                 className="flex items-start"
//               >
//                 <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
//                 <p>Connect with our community and access exclusive content</p>
//               </motion.div>
//             </div>
//           </div>
//         </div>
//       </motion.div>
//     </div>
//   )
// }

// export default Login
