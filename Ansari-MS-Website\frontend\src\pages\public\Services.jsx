/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useServices } from '../../contexts/ServicesContext';
import { motion } from 'framer-motion';
import { ChevronRight, Check, ArrowRight, Search, MapPin, Phone, Clock, ExternalLink, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import Button from '../../components/Button';

// Import Swiper styles
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const Services = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  // Service tabs state
  const [activeTab, setActiveTab] = useState('premium-hens');
  const { services } = useServices();

  // Map and location search state
  const mapRef = useRef(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [currentServiceIndex, setCurrentServiceIndex] = useState(0);

  // Navigate to service details
  const handleServiceClick = (service) => {
    const serviceId = service.SR_Id || service.id;
    if (!serviceId) {
      console.error('Service ID is required');
      return;
    }
    navigate(`/services/${serviceId}`);
  };

  // Filter services by category
  const getServicesByCategory = useCallback(
    (category) => {
      return services.filter(
        (service) => service.Category && service.Category.toLowerCase() === category.toLowerCase()
      );
    },
    [services]
  );

  // Get services for the active tab
  const activeServiceItems = useMemo(() => {
    const services = getServicesByCategory(
      activeTab === 'premium-hens' ? 'Premium Hens' : activeTab === 'baby-chickens' ? 'Baby Chickens' : 'Wholesale'
    );
    // Remove duplicates by creating a map of unique services using SR_Id
    const uniqueServices = Array.from(new Map(services.map((service) => [service.SR_Id, service])).values());
    return uniqueServices;
  }, [getServicesByCategory, activeTab]);

  // Fallback service data if no services exist in context
  const fallbackServices = {
    'premium-hens': [
      {
        id: 'premium-hens-1',
        title: t('servicesData.premium.title'),
        category: t('services.tabs.premium') ,
        description:
          t('servicesData.premium.descriptoin'),
        features: [
          'Excellent egg layers (250-300 eggs per year)',
          'Hardy and adaptable to different environments',
          'Friendly and docile temperament',
          'Rich, brown feathering with glossy appearance',
          'Ideal for beginners and experienced farmers alike',
        ],
        image: './imgs/hens-illustration.jpg',
        price: '450 AF per hen',
        status: 'active',
      },
      {
        id: 'premium-hens-2',
        title: 'Leghorn White',
        category: t('services.tabs.premium') ,
        description:
          'Leghorn White hens are prolific egg layers with excellent feed-to-egg conversion, making them economical and productive.',
        features: [
          'Exceptional egg production (300+ eggs per year)',
          'Efficient feed conversion ratio',
          'Pure white feathers and bright red combs',
          'Lightweight but hardy birds',
          'Early maturity and long productive life',
        ],
        image: './imgs/hens-illustration.jpg',
        price: '400 AF per hen',
        status: 'active',
      },
      {
        id: 'premium-hens-3',
        title: 'Plymouth Rock',
        category: t('services.tabs.premium') ,
        description:
          'Plymouth Rock hens are dual-purpose birds known for their friendly nature and consistent egg production.',
        features: [
          'Good egg production (200-250 eggs per year)',
          'Distinctive barred pattern feathering',
          'Calm and friendly disposition',
          'Cold-hardy and heat-tolerant',
          'Excellent foragers with free-range capabilities',
        ],
        image: './imgs/hens-illustration.jpg',
        price: '425 AF per hen',
        status: 'active',
      },
    ],
    'baby-chickens': [
      {
        id: 'baby-chickens-1',
        title: 'Day-Old Broilers',
        category: t('services.tabs.baby') ,
        description:
          'Our day-old broiler chicks are bred for rapid growth and excellent meat production, perfect for commercial operations.',
        features: [
          'Fast growth rate (market weight in 6-8 weeks)',
          'Excellent feed conversion efficiency',
          'Vaccinated against common poultry diseases',
          'Robust health and vigor from day one',
          'Bulk discounts available for large orders',
        ],
        image: './imgs/baby-chickens.jpg',
        price: '80 AF per chick',
        status: 'active',
      },
      {
        id: 'baby-chickens-2',
        title: 'Layer Pullets',
        category: t('services.tabs.baby'),
        description:
          'These female chicks are specifically bred for egg production, with genetics selected for high yield and quality eggs.',
        features: [
          'Selected from high-producing egg lines',
          'Begin laying at 18-20 weeks of age',
          'Vaccinated and health-certified',
          'Available in various breeds',
          'Detailed care instructions provided',
        ],
        image: './imgs/baby-chickens.jpg',
        price: '100 AF per chick',
        status: 'active',
      },
      {
        id: 'baby-chickens-3',
        title: 'Mixed Breed Chicks',
        category: t('services.tabs.baby'),
        description:
          'Our mixed breed chicks offer variety and interest for backyard flocks, with a range of colors and characteristics.',
        features: [
          'Diverse genetics for colorful and interesting flocks',
          'Hardy and adaptable to various conditions',
          'Great for beginners and children',
          'Vaccinated against common diseases',
          'Surprise element with various breeds in each batch',
        ],
        image: './imgs/baby-chickens.jpg',
        price: '90 AF per chick',
        status: 'active',
      },
    ],
    wholesale: [
      {
        id: 'wholesale-1',
        title: 'Commercial Farm Package',
        category: t('services.tabs.wholesale'),
        description: 'Complete package for commercial poultry operations, including birds, feed, and ongoing support.',
        features: [
          'Minimum order of 500 birds',
          'Discounted pricing structure',
          'Technical support and consultation',
          'Feed supply options available',
          'Regular health check-ups included',
        ],
        image: './imgs/fresh-hens.jpg',
        price: 'Contact for pricing',
        status: 'active',
      },
      {
        id: 'wholesale-2',
        title: 'Restaurant Supply Program',
        category: t('services.tabs.wholesale'),
        description:
          'Regular supply of fresh poultry products tailored specifically for restaurants and food service businesses.',
        features: [
          'Consistent weekly or monthly delivery',
          'Custom processing options available',
          'Flexible payment terms for established businesses',
          'Quality guarantee on all products',
          'Emergency supply options for unexpected demand',
        ],
        image: './imgs/fresh-hens.jpg',
        price: 'Contact for pricing',
        status: 'active',
      },
      {
        id: 'wholesale-3',
        title: 'Market Reseller Package',
        category: t('services.tabs.wholesale'),
        description:
          'Bulk poultry products for market vendors and resellers, with competitive pricing to ensure profit margins.',
        features: [
          'Volume-based pricing tiers',
          'Branded packaging options',
          'Marketing support materials',
          'Flexible pickup and delivery options',
          'Product freshness guarantee',
        ],
        image: './imgs/fresh-hens.jpg',
        price: 'Contact for pricing',
        status: 'active',
      },
    ],
  };

  // Use fallback data if no services exist in the context for the active category
  const displayServices = activeServiceItems.length > 0 ? activeServiceItems : fallbackServices[activeTab] || [];

  // Auto-rotation effect for services (5 seconds interval)
  useEffect(() => {
    if (displayServices.length <= 1) {
      setCurrentServiceIndex(0);
      return;
    }

    const interval = setInterval(() => {
      setCurrentServiceIndex((prevIndex) => (prevIndex + 1) % displayServices.length);
    }, 5000); // Change service every 5 seconds

    return () => clearInterval(interval);
  }, [displayServices.length, activeTab]);

  // Reset service index when tab changes
  useEffect(() => {
    setCurrentServiceIndex(0);
  }, [activeTab]);

  // Branches data
  const branches = t('services.branches.locations', { returnObjects: true });


  // Process steps
  const processSteps = t('services.process.processSteps', { returnObjects: true });

  // Initialize map
  useEffect(() => {
    // Check if map is already initialized on the container
    if (typeof window === 'undefined' || !mapRef.current || map || mapRef.current._leaflet_id) return;

    let leafletInstance;
    let mapInstance;
    let L;

    // Import Leaflet dynamically
    const initializeMap = async () => {
      try {
        const leaflet = await import('leaflet');
        leafletInstance = leaflet;
        L = leafletInstance;

        // Fix Leaflet's icon paths
        delete leafletInstance.Icon.Default.prototype._getIconUrl;
        leafletInstance.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
        });

        // Create map
        mapInstance = leafletInstance.map(mapRef.current).setView([31.6205, 65.7158], 13); // Kandahar

        leafletInstance
          .tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          })
          .addTo(mapInstance);

        setMap(mapInstance);

        // Create custom icon for branches
        const branchIcon = leafletInstance.icon({
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
          shadowSize: [41, 41],
        });

        // Add markers for each branch with enhanced popups
        const newMarkers = branches.map((branch) => {
          const marker = leafletInstance.marker(branch.coordinates, { icon: branchIcon }).addTo(mapInstance).bindPopup(`
            <div style="text-align: center;">
              <b style="font-size: 14px;">${branch.name}</b>
              <p style="margin: 5px 0;">${branch.address}</p>
              <p style="margin: 5px 0; color: #FF6B00;">${branch.phone}</p>
              <button
                style="background-color: #2C3E50; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-top: 5px;"
                onclick="document.dispatchEvent(new CustomEvent('select-branch', {detail: ${branch.id}}))"
              >
                View Details
              </button>
            </div>
          `);

          marker.on('click', () => {
            setSelectedBranch(branch);
          });

          return marker;
        });

        setMarkers(newMarkers);
        setSelectedBranch(branches[0]); // Select first branch by default

        // Add event listener for custom popup button click
        document.addEventListener('select-branch', (e) => {
          const branchId = e.detail;
          const branch = branches.find((b) => b.id === branchId);
          if (branch) {
            setSelectedBranch(branch);
            // Scroll to the selected branch details
            document.getElementById('selected-branch-details')?.scrollIntoView({ behavior: 'smooth' });
          }
        });

        // Add map click event to allow users to find nearest branch to any point
        mapInstance.on('click', (e) => {
          const { lat, lng } = e.latlng;

          // Find closest branch to clicked point
          let closestBranch = branches[0];
          let closestDistance = calculateDistance(lat, lng, branches[0].coordinates[0], branches[0].coordinates[1]);

          branches.forEach((branch) => {
            const distance = calculateDistance(lat, lng, branch.coordinates[0], branch.coordinates[1]);

            if (distance < closestDistance) {
              closestDistance = distance;
              closestBranch = branch;
            }
          });

          // Add a temporary marker at clicked location
          const clickMarker = leafletInstance
            .marker([lat, lng])
            .addTo(mapInstance)
            .bindPopup(
              `<b>Selected Location</b><br>Nearest branch: ${closestBranch.name} (${Math.round(closestDistance * 10) / 10} km away)`
            )
            .openPopup();

          // Set the closest branch as selected with distance info
          setSelectedBranch({
            ...closestBranch,
            distanceFromClick: Math.round(closestDistance * 10) / 10, // Round to 1 decimal place
          });

          // Remove the marker after 5 seconds
          setTimeout(() => {
            mapInstance.removeLayer(clickMarker);
          }, 5000);
        });
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    initializeMap();

    // Cleanup function - make sure to properly clean up the map
    return () => {
      if (map) {
        map.remove();
        setMap(null);
        setMarkers([]);
      }
    };
  }); // Add map to the dependency array to properly handle cleanup

  // Handle search
  const handleSearch = async () => {
    if (!searchQuery.trim() || !map) return;

    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${searchQuery}, Kandahar`);
      const data = await response.json();

      if (data.length > 0) {
        const location = {
          lat: Number.parseFloat(data[0].lat),
          lon: Number.parseFloat(data[0].lon),
          display_name: data[0].display_name,
        };

        // Center map on search result
        map.setView([location.lat, location.lon], 15);

        // Import Leaflet dynamically to get access to L
        const leaflet = await import('leaflet');

        // Add a marker for the searched location
        const searchMarker = leaflet
          .marker([location.lat, location.lon])
          .addTo(map)
          .bindPopup(`<b>Your Search Location</b><br>${location.display_name}`)
          .openPopup();

        // Find closest branch
        let closestBranch = branches[0];
        let closestDistance = calculateDistance(
          location.lat,
          location.lon,
          branches[0].coordinates[0],
          branches[0].coordinates[1]
        );

        branches.forEach((branch) => {
          const distance = calculateDistance(location.lat, location.lon, branch.coordinates[0], branch.coordinates[1]);

          if (distance < closestDistance) {
            closestDistance = distance;
            closestBranch = branch;
          }
        });

        // Set the closest branch as selected
        setSelectedBranch({
          ...closestBranch,
          distanceFromSearch: Math.round(closestDistance * 10) / 10, // Round to 1 decimal place
        });

        // Highlight the closest branch marker
        markers.forEach((marker) => {
          const markerLatLng = marker.getLatLng();
          if (markerLatLng.lat === closestBranch.coordinates[0] && markerLatLng.lng === closestBranch.coordinates[1]) {
            setTimeout(() => {
              marker.openPopup();
            }, 1000); // Slight delay to let the map pan first
          }
        });

        // Draw a line between search location and closest branch
        const line = leaflet
          .polyline([[location.lat, location.lon], closestBranch.coordinates], {
            color: '#FF6B00',
            weight: 3,
            opacity: 0.7,
            dashArray: '10, 10',
          })
          .addTo(map);

        // Remove the line and search marker after 10 seconds
        setTimeout(() => {
          map.removeLayer(line);
          map.removeLayer(searchMarker);
        }, 10000);
      } else {
        alert('No results found in Kandahar. Try another search.');
      }
    } catch (error) {
      console.error('Error fetching location:', error);
    }
  };

  // Calculate distance between two points (Haversine formula)
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in km
    return d;
  };

  const deg2rad = (deg) => {
    return deg * (Math.PI / 180);
  };

  // Handle branch selection
  const handleBranchSelect = async (branch) => {
    setSelectedBranch(branch);

    if (map && markers) {
      // Center map on selected branch
      map.setView(branch.coordinates, 15);

      // Find and open the popup for this branch
      markers.forEach((marker) => {
        const markerLatLng = marker.getLatLng();
        if (markerLatLng.lat === branch.coordinates[0] && markerLatLng.lng === branch.coordinates[1]) {
          marker.openPopup();

          // Import Leaflet dynamically to get access to L
          import('leaflet').then((leaflet) => {
            // Add a temporary highlight effect
            const highlightCircle = leaflet
              .circle(branch.coordinates, {
                color: '#FF6B00',
                fillColor: '#FF6B00',
                fillOpacity: 0.2,
                radius: 100,
              })
              .addTo(map);

            // Remove the highlight after 2 seconds
            setTimeout(() => {
              map.removeLayer(highlightCircle);
            }, 2000);
          });
        }
      });
    }
  };

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerContainer = {
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  // Custom arrow components for the slider
  const NextArrow = (props) => {
    const { style, onClick } = props;
    return (
      <div
        className="z-10 w-6 md:w-10 h-6 md:h-10 flex items-center justify-center bg-primary backdrop-blur-sm rounded-full text-white absolute top-1/2 -right-7 md:-right-12 -translate-y-1/2 shadow-lg border border-primary/20 hover:bg-white hover:text-primary transition-all duration-300 cursor-pointer"
        style={{ ...style, display: 'flex' }}
        onClick={onClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M9 18l6-6-6-6" />
        </svg>
      </div>
    );
  };

  const PrevArrow = (props) => {
    const { style, onClick } = props;
    return (
      <div
        className="z-10 w-6 md:w-10 h-6 md:h-10 flex items-center justify-center bg-primary backdrop-blur-sm rounded-full text-white absolute top-1/2 -left-7 md:-left-12 -translate-y-1/2 shadow-lg border border-primary/20 hover:bg-white hover:text-primary transition-all duration-300 cursor-pointer"
        style={{ ...style, display: 'flex' }}
        onClick={onClick}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M15 18l-6-6 6-6" />
        </svg>
      </div>
    );
  };

  // Slider settings
  const settings = {
    dots: true,
    infinite: displayServices.length > 1,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: displayServices.length > 1,
    autoplaySpeed: 4000,
    arrows: displayServices.length > 1,
    prevArrow: <PrevArrow />,
    nextArrow: <NextArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
          infinite: displayServices.length > 1,
          arrows: displayServices.length > 1,
          autoplay: displayServices.length > 1,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          infinite: displayServices.length > 1,
          arrows: displayServices.length > 1,
          autoplay: displayServices.length > 1,
        },
      },
    ],
  };

  const dir = i18n.language === 'ps' ? 'rtl' : 'ltr';
    useEffect(() => {
      document.documentElement.setAttribute('dir', dir);
      document.documentElement.setAttribute('lang', i18n.language);
    }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-[90vh] top-16 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img src="./imgs/services.jpg" alt="Services background" className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-gradient-to-r from-secondary/90 to-[#34495e]/80"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-headingEn font-extrabold text-white mb-6 leading-tight">
              {t('services.badge')} <span className="text-primary">{t('services.title')}</span>
            </h1>
            <p className="text-base sm:text-xl text-white/90 font-bodyEn max-w-3xl mx-auto mb-8">
              {t('services.subtitle')}
            </p>
            <div className="flex justify-center">
              <div className="w-24 h-1 bg-primary rounded-full"></div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Tabs Section */}
      <section className="relative top-16 py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary font-headingEn font-semibold text-lg uppercase tracking-wider">What We Offer</span>
            <h2 className="text-3xl md:text-4xl font-headingEn font-bold text-secondary mt-2">Our Premium Services</h2>
            <div className="w-24 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

          {/* Tabs */}
          <div className="flex flex-wrap justify-center mb-8 gap-3">
            {['premium-hens', 'baby-chickens', 'wholesale'].map((categoryId, index) => (
              <button
                key={`${categoryId}-${index}`}
                onClick={() => setActiveTab(categoryId)}
                className={`px-6 py-3 rounded-lg font-bodyEn font-medium transition-all duration-300 shadow-sm ${
                  activeTab === categoryId
                    ? 'bg-primary text-white shadow-card-hover transform scale-105'
                    : 'bg-card text-textprimary border border-border-color hover:bg-muted hover:shadow-card'
                }`}
              >
                {categoryId === 'premium-hens'
                  ? t('services.tabs.premium')
                  : categoryId === 'baby-chickens'
                    ? t('services.tabs.baby')
                    : t('services.tabs.wholesale') }
              </button>
            ))}
          </div>

          {/* Auto-Rotating Service Display */}
          <motion.div
            className="w-full px-4 md:px-16 md:h-[500px]"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.35 }}
          >
            <div className="bg-card rounded-xl md:h-[500px] border border-border-color shadow-card-hover overflow-hidden relative">
              {displayServices.length > 0 && (
                <motion.div
                  key={`${displayServices[currentServiceIndex]?.SR_Id || displayServices[currentServiceIndex]?.id}-${currentServiceIndex}`}
                  className="outline-none md:h-full"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 md:items-center md:h-[500px]">
                    {/* Left side - Image */}
                    <div className="relative h-full">
                      <img
                        src={displayServices[currentServiceIndex]?.Image ? `http://localhost:5432/public/images/services/${displayServices[currentServiceIndex].Image}` : displayServices[currentServiceIndex]?.image || '/placeholder.svg'}
                        alt={displayServices[currentServiceIndex]?.Title || displayServices[currentServiceIndex]?.title}
                        className="w-full h-full object-cover rounded-l-xl"
                      />
                      <div className="absolute top-2 md:top-4 left-2 md:left-4 bg-primary text-white px-3 md:px-4 py-1.5 rounded-lg text-xs md:text-sm font-headingEn font-semibold shadow-card">
                        {displayServices[currentServiceIndex]?.Category || displayServices[currentServiceIndex]?.category}
                      </div>

                      {/* Service Counter - Show only if multiple services */}
                      {displayServices.length > 1 && (
                        <div className="absolute bottom-2 md:bottom-4 right-2 md:right-4 bg-black/50 text-white px-3 py-1 rounded-lg text-xs font-bodyEn">
                          {currentServiceIndex + 1} / {displayServices.length}
                        </div>
                      )}
                    </div>

                    {/* Right side - Content */}
                    <div className="p-6 md:p-8 flex flex-col justify-between h-full overflow-y-auto">
                      <div>
                        <h3 className="text-lg md:text-2xl font-headingEn font-bold text-secondary mb-4 break-words">
                          {displayServices[currentServiceIndex]?.Title || displayServices[currentServiceIndex]?.title}
                        </h3>
                        <p className="text-textprimary font-bodyEn mb-6 leading-relaxed">
                          {displayServices[currentServiceIndex]?.Description || displayServices[currentServiceIndex]?.description}
                        </p>

                        <div className="mb-6">
                          <h4 className="text-sm md:text-base font-headingEn font-semibold text-secondary mb-3 flex items-center">
                            <span className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                              <Check size={14} className="text-primary" />
                            </span>
                            Key Features
                          </h4>
                          <ul className="space-y-3 pl-8">
                            {(displayServices[currentServiceIndex]?.Features || displayServices[currentServiceIndex]?.features) &&
                              (displayServices[currentServiceIndex]?.Features || displayServices[currentServiceIndex]?.features).slice(0, 3).map((feature, index) => (
                                <li key={`${displayServices[currentServiceIndex]?.SR_Id || displayServices[currentServiceIndex]?.id}-${index}`} className="flex items-start">
                                  <div className="w-2 h-2 rounded-full bg-primary mt-2 mr-3 flex-shrink-0"></div>
                                  <span className="text-xs md:text-sm text-textprimary font-bodyEn leading-relaxed">
                                    {feature}
                                  </span>
                                </li>
                              ))}
                          </ul>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-auto pt-4 border-t border-border-color">
                        <div className="text-xl md:text-2xl font-headingEn font-bold text-primary mb-4 sm:mb-0">
                          ${displayServices[currentServiceIndex]?.Price || displayServices[currentServiceIndex]?.price}
                        </div>
                        <div className="flex gap-3 w-full sm:w-auto">
                          <Button
                            variant="primary"
                            onClick={() => handleServiceClick(displayServices[currentServiceIndex])}
                            className="flex items-center text-xs md:text-sm font-bodyEn w-full sm:w-auto justify-center"
                          >
                            <Info size={14} className="mr-1" /> View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Manual Navigation Dots - Show only if multiple services */}
              {displayServices.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {displayServices.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentServiceIndex(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === currentServiceIndex ? 'bg-primary' : 'bg-white/50 hover:bg-white/70'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Process Section */}
      <section className="relative top-16 py-16 px-4 bg-muted">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary font-headingEn font-semibold text-lg uppercase tracking-wider">{t('services.process.subtitle')}</span>
            <h2 className="text-3xl md:text-4xl font-headingEn font-bold text-secondary mt-2">{t('services.process.title')}</h2>
            <div className="w-32 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
          </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((item, index) => (
              <motion.div
                key={index}
                className="bg-card p-6 rounded-xl shadow-card hover:shadow-card-hover transition-shadow duration-300 relative border border-border-color"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: item.step * 0.1 }}
              >
                <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center font-headingEn font-bold shadow-card">
                  {item.step}
                </div>
                <div className="text-4xl mb-4 mt-4 text-center text-primary">{item.icon}</div>
                <h3 className="text-xl font-headingEn font-bold text-secondary mb-2 text-center">{item.title}</h3>
                <p className="text-textprimary/80 font-bodyEn text-center leading-relaxed">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Branches & Map Section */}
      <section className="py-16 relative top-16 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <span className="text-primary font-headingEn font-semibold text-lg uppercase tracking-wider">{t('services.branches.subtitle')}</span>
            <h2 className="text-3xl md:text-4xl font-headingEn font-bold text-secondary mt-2">{t('services.branches.title')}</h2>
            <div className="w-32 h-1 bg-primary mx-auto mt-6 rounded-full"></div>
            <p className="text-lg text-textprimary/70 font-bodyEn max-w-3xl mx-auto mt-6 leading-relaxed">
             {t('services.branches.description')}
            </p>
          </motion.div>

          {/* Branch Cards */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {branches.map((branch) => (
              <motion.div
                key={branch.id}
                className={`flex flex-col rounded-xl shadow-card border overflow-hidden cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-card-hover ${
                  selectedBranch?.id === branch.id
                    ? 'ring-2 ring-primary transform scale-105 bg-card border-primary'
                    : 'bg-card border-border-color hover:border-primary/50'
                }`}
                variants={fadeIn}
                onClick={() => handleBranchSelect(branch)}
              >
                <div className="flex items-center justify-center overflow-hidden h-48">
                  <img src={branch.image || '/placeholder.svg'} alt={branch.name} className="w-full h-full object-cover" />
                </div>
                <div className="p-6 flex-grow">
                  <h3 className="text-xl font-headingEn font-bold text-secondary mb-3">{branch.name}</h3>
                  <div className="flex items-start gap-1 mb-3">
                    <MapPin className="text-primary mr-2 flex-shrink-0 mt-1" size={18} />
                    <p className="text-textprimary font-bodyEn">{branch.address}</p>
                  </div>
                  <div className="flex items-start gap-1 mb-3">
                    <Phone className="text-primary mr-2 flex-shrink-0 mt-1" size={18} />
                    <p className="text-textprimary font-bodyEn">{branch.phone}</p>
                  </div>
                  <div className="flex items-start gap-1">
                    <Clock className="text-primary mr-2 flex-shrink-0 mt-1" size={18} />
                    <p className="text-textprimary font-bodyEn text-sm">{branch.hours}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Search and Map */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Search */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="bg-card rounded-xl shadow-card border border-border-color overflow-hidden p-6">
                <h3 className="text-xl font-headingEn font-bold text-secondary mb-4">{t('services.branches.findNear')}</h3>
                <p className="text-textprimary font-bodyEn mb-4 leading-relaxed">
                  {t('services.branches.enterLocation')}
                </p>

                <div className="relative mb-4">
                  <input
                    type="text"
                    placeholder={t('services.branches.placeholder')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                </div>

                <div className="flex justify-center items-center">
                  <Button variant="primary" onClick={handleSearch}>
                    {t('services.branches.search')}
                  </Button>
                </div>

                <div className="mt-6">
                  <h4 className="font-semibold text-secondary  mb-2">{t('services.branches.whyVisit')}</h4>
                  <ul className="space-y-2 ">
                    <li className="flex  items-start gap-1">
                      <Check size={16} className="text-destructive mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm ">{t('services.branches.reasons.see')}</span>
                    </li>
                    <li className="flex  items-start gap-1">
                      <Check size={16} className="text-destructive mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm ">{t('services.branches.reasons.expert')}</span>
                    </li>
                    <li className="flex  items-start gap-1 ">
                      <Check size={16} className="text-destructive mr-2 mt-1 flex-shrink-0" />
                      <span className="text-sm ">{t('services.branches.reasons.promotions')}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Map */}
            <motion.div
              className="lg:col-span-2"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4 }}
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden h-full">
                <div className="h-[400px] relative">
                  {/* Map container */}
                  <div ref={mapRef} className="h-full w-full z-10"></div>

                  {/* Fallback for when map is loading */}
                  {!map && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-0">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                        <p className="mt-4 text-gray-600">Loading map...</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>

          {/* Selected Branch Details */}
          {selectedBranch && (
            <motion.div
              id="selected-branch-details"
              className="mt-8 bg-white rounded-xl shadow-sm border overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="p-6 bg-[#2C3E50] text-white">
                <h3 className="text-xl font-bold ">{t('services.branches.detailsTitle')}</h3>
                {(selectedBranch.distanceFromSearch || selectedBranch.distanceFromClick) && (
                  <p className="text-white/80 text-sm  mt-1">
                    {selectedBranch.distanceFromSearch &&
                      `${selectedBranch.distanceFromSearch} km from your search location`}
                    {selectedBranch.distanceFromClick &&
                      `${selectedBranch.distanceFromClick} km from your selected point`}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
                <div className="md:col-span-1 flex justify-center">
                  <img src={selectedBranch.image || '/placeholder.svg'} alt={selectedBranch.name} className="" />
                </div>
                <div className="md:col-span-2">
                  <h3 className="text-2xl font-bold text-secondary  mb-4">{selectedBranch.name}</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="flex items-start">
                      <MapPin className="text-primary mr-3 flex-shrink-0 mt-1" size={20} />
                      <div>
                        <h4 className="font-semibold text-secondary mb-1 ">Address</h4>
                        <p className="text-gray-600">{selectedBranch.address}</p>
                        <a
                          href={`https://www.google.com/maps/search/?api=1&query=${selectedBranch.coordinates[0]},${selectedBranch.coordinates[1]}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center  text-primary hover:underline mt-1 text-sm"
                        >
                          Get directions <ExternalLink size={14} className="ml-1" />
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <Phone className="text-primary mr-3 flex-shrink-0 mt-1" size={20} />
                      <div>
                        <h4 className="font-semibold text-secondary  mb-1">Contact</h4>
                        <p className="text-gray-600 ">{selectedBranch.phone}</p>
                        <a
                          href={`tel:${selectedBranch.phone.replace(/\s+/g, '')}`}
                          className="inline-flex items-center text-primary hover:underline mt-1 text-sm "
                        >
                          Call now
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start md:col-span-2">
                      <Clock className="text-primary mr-3 flex-shrink-0 mt-1" size={20} />
                      <div>
                        <h4 className="font-semibold text-secondary  mb-1">Business Hours</h4>
                        <p className="text-gray-600 ">{selectedBranch.hours}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap items-center gap-3">
                    <Button variant="primary" className="items-center flex rtl:flex-row-reverse">
                      {t('services.branches.contactBranch')} <Phone size={16} className="ml-2" />
                    </Button>
                    <a
                      href={`https://www.google.com/maps/search/?api=1&query=${selectedBranch.coordinates[0]},${selectedBranch.coordinates[1]}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-[#2C3E50] text-white px-2 py-1.5  rounded-sm flex items-center rtl:flex-row-reverse hover:bg-[#1a2530] transition-colors"
                    >
                      {t('services.branches.viewMap')} <ExternalLink size={16} className="ml-2" />
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 relative top-16 px-4 bg-gradient-to-r from-secondary to-[#34495e]">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h2 className="text-3xl md:text-4xl font-headingEn font-bold text-white mb-6">{t('services.cta.title')}</h2>
            <p className="text-white/80 text-lg font-bodyEn mb-8 max-w-2xl mx-auto leading-relaxed">
             {t('services.cta.description')}
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center bg-primary text-white font-headingEn font-bold px-8 py-4 rounded-lg hover:bg-primary-hover transition-all duration-300 shadow-card-hover transform hover:scale-105"
            >
              {t('services.cta.button')} <ArrowRight className="ml-2" />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Services;
