'use client';

import { useState } from 'react';

// Mock data for medicines
const mockMedicines = [
  {
    id: '1',
    name: 'Amoxicillin',
    type: 'Antibiotic',
    quantity: 150,
    price: 25.99,
    supplier: 'MedPharm Inc.',
    expiryDate: '2024-12-31',
    status: 'in-stock',
    batchNumber: 'AMX-2023-001',
  },
  {
    id: '2',
    name: 'Tylosin',
    type: 'Antibiotic',
    quantity: 80,
    price: 32.5,
    supplier: 'VetSupplies Co.',
    expiryDate: '2024-10-15',
    status: 'in-stock',
    batchNumber: 'TYL-2023-045',
  },
  {
    id: '3',
    name: 'Vitamin B Complex',
    type: 'Supplement',
    quantity: 200,
    price: 18.75,
    supplier: 'NutriVet',
    expiryDate: '2025-03-22',
    status: 'in-stock',
    batchNumber: 'VBC-2023-112',
  },
  {
    id: '4',
    name: 'Enrofloxacin',
    type: 'Antibiotic',
    quantity: 30,
    price: 45.0,
    supplier: 'MedPharm Inc.',
    expiryDate: '2024-08-10',
    status: 'low-stock',
    batchNumber: 'ENR-2023-078',
  },
  {
    id: '5',
    name: 'Coccidiostat',
    type: 'Antiparasitic',
    quantity: 10,
    price: 38.25,
    supplier: 'PoultryHealth Ltd.',
    expiryDate: '2024-06-30',
    status: 'low-stock',
    batchNumber: 'COC-2023-033',
  },
  {
    id: '6',
    name: 'Electrolyte Solution',
    type: 'Supplement',
    quantity: 0,
    price: 12.99,
    supplier: 'NutriVet',
    expiryDate: '2025-01-15',
    status: 'out-of-stock',
    batchNumber: 'ELS-2023-090',
  },
  {
    id: '7',
    name: 'Ivermectin',
    type: 'Antiparasitic',
    quantity: 45,
    price: 29.5,
    supplier: 'VetSupplies Co.',
    expiryDate: '2024-11-20',
    status: 'in-stock',
    batchNumber: 'IVM-2023-056',
  },
  {
    id: '8',
    name: 'Doxycycline',
    type: 'Antibiotic',
    quantity: 0,
    price: 27.75,
    supplier: 'MedPharm Inc.',
    expiryDate: '2024-09-05',
    status: 'out-of-stock',
    batchNumber: 'DOX-2023-022',
  },
];

export function useMedicines() {
  const [medicines, setMedicines] = useState(mockMedicines);

  const deleteMedicine = (id) => {
    setMedicines(medicines.filter((medicine) => medicine.id !== id));
  };

  return {
    medicines,
    deleteMedicine,
  };
}
