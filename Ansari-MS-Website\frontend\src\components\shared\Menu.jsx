import React from 'react';
import { ChevronRight } from 'lucide-react';

const Menu = React.forwardRef(({ children, className = '', ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={`py-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 ${className}`}
      role="menu"
      {...props}
    >
      {children}
    </div>
  );
});

const MenuItem = React.forwardRef(({ children, icon, onClick, disabled = false, className = '', ...props }, ref) => {
  return (
    <button
      ref={ref}
      onClick={onClick}
      disabled={disabled}
      className={`w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      role="menuitem"
      {...props}
    >
      {icon && <span className="mr-3">{icon}</span>}
      <span className="flex-1">{children}</span>
      <ChevronRight className="h-4 w-4 text-gray-400" />
    </button>
  );
});

const MenuDivider = React.forwardRef(({ className = '', ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={`my-1 border-t border-gray-200 dark:border-gray-700 ${className}`}
      role="separator"
      {...props}
    />
  );
});

Menu.Item = MenuItem;
Menu.Divider = MenuDivider;

Menu.displayName = 'Menu';
MenuItem.displayName = 'Menu.Item';
MenuDivider.displayName = 'Menu.Divider';

export default Menu;
