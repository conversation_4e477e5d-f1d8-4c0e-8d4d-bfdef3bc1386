'use client';

import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Save, X, AlertCircle, Loader2 } from 'lucide-react';
import { useMedicine } from '../../contexts/MedicineContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Input } from '../../components/medicine-components/Input';
import { Label } from '../../components/medicine-components/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/medicine-components/Select';
import { Textarea } from '../../components/medicine-components/Textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/medicine-components/card';

const EditMedicinePage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { medicines, updateMedicine, loading: contextLoading } = useMedicine();
  const { language, translations } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    type: 'vaccine',
    quantity: '',
    price: '',
    supplier: '',
    expiryDate: '',
    batchNumber: '',
    status: 'in-stock',
    description: '',
    dosage: '',
    administration: 'oral',
    storage: 'room-temperature',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    if (contextLoading) return;

    // Extract the numeric ID from the URL parameter
    const numericId = id ? parseInt(id.replace(':', '')) : NaN;

    if (isNaN(numericId)) {
      setError(t('invalid_medicine_id'));
      return;
    }

    // Check if medicines array is loaded and not empty
    if (!medicines || medicines.length === 0) {
      setError(t('no_medicines_loaded'));
      return;
    }

    const medicine = medicines.find((m) => m.id === numericId);
    if (!medicine) {
      setError(t('medicine_not_found'));
      return;
    }

    // Format the expiry date for the date input
    const formattedMedicine = {
      ...medicine,
      expiryDate: medicine.expiryDate ? new Date(medicine.expiryDate).toISOString().split('T')[0] : '',
    };

    setFormData(formattedMedicine);
  }, [id, medicines, contextLoading, t]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const handleTextareaChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.name.trim()) throw new Error(t('name_required'));
    if (!formData.type) throw new Error(t('type_required'));
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('valid_quantity_required'));
    if (!formData.price || formData.price <= 0) throw new Error(t('valid_price_required'));
    if (!formData.supplier.trim()) throw new Error(t('supplier_required'));
    if (!formData.expiryDate) throw new Error(t('expiry_date_required'));
    if (!formData.batchNumber.trim()) throw new Error(t('batch_number_required'));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      // Extract the numeric ID from the URL parameter
      const numericId = parseInt(id.replace(':', ''));

      await updateMedicine(numericId, formData);

      setFeedback({
        type: 'success',
        message: t('medicine_updated_successfully'),
      });

      setTimeout(() => {
        navigate('/admin/medicine');
      }, 1500);
    } catch (error) {
      setFeedback({
        type: 'error',
        message: error.message || t('update_failed'),
      });
    } finally {
      setLoading(false);
    }
  };

  if (contextLoading) {
    return (
      <div
        className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}
      >
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className={`flex items-center justify-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Loader2 className={`h-8 w-8 animate-spin text-primary ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                <p className="text-lg">{t('loading')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}
      >
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div
                className={`flex items-center justify-center text-red-600 dark:text-red-400 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <AlertCircle className={`h-6 w-6 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                <p className="text-lg">{error}</p>
              </div>
              <div className="mt-4 flex justify-center">
                <Button
                  variant="primary"
                  onClick={() => navigate('/admin/medicine')}
                  className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                >
                  {t('back_to_medicines')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto">
        <div className={`flex justify-between items-center mb-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('edit_medicine')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('edit_medicine_description')}</p>
          </div>
          <Button
            variant="cancel"
            onClick={() => navigate('/admin/medicine')}
            className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <X className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            {t('back')}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('medicine_information')}</CardTitle>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-6 p-4 rounded-lg flex items-start ${
                  feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                } ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <AlertCircle className={`h-5 w-5 ${language === 'ps' ? 'ml-2' : 'mr-2'} mt-0.5`} />
                <p>{feedback.message}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">{t('name')}</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name || ''}
                      onChange={handleChange}
                      placeholder={t('medicine_name')}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="type">{t('type')}</Label>
                    <Select
                      value={formData.type || 'vaccine'}
                      onValueChange={(value) => handleSelectChange('type', value)}
                    >
                      <SelectTrigger dir={language === 'ps' ? 'rtl' : 'ltr'}>
                        <SelectValue placeholder={t('select_medicine_type')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="vaccine">{t('vaccine')}</SelectItem>
                        <SelectItem value="antibiotic">{t('antibiotic')}</SelectItem>
                        <SelectItem value="vitamin">{t('vitamin')}</SelectItem>
                        <SelectItem value="supplement">{t('supplement')}</SelectItem>
                        <SelectItem value="other">{t('other')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="quantity">{t('quantity')}</Label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      value={formData.quantity || ''}
                      onChange={handleChange}
                      placeholder={t('medicine_quantity')}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="price">{t('price')}</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      value={formData.price || ''}
                      onChange={handleChange}
                      placeholder={t('medicine_price')}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>
                </div>

                {/* Additional Information */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="supplier">{t('supplier')}</Label>
                    <Input
                      id="supplier"
                      name="supplier"
                      value={formData.supplier || ''}
                      onChange={handleChange}
                      placeholder={t('supplier_name')}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="batchNumber">{t('batch_number')}</Label>
                    <Input
                      id="batchNumber"
                      name="batchNumber"
                      value={formData.batchNumber || ''}
                      onChange={handleChange}
                      placeholder={t('batch_number')}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="expiryDate">{t('expiry_date')}</Label>
                    <Input
                      id="expiryDate"
                      name="expiryDate"
                      type="date"
                      value={formData.expiryDate || ''}
                      onChange={handleChange}
                      required
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="status">{t('status')}</Label>
                    <Select
                      value={formData.status || 'in-stock'}
                      onValueChange={(value) => handleSelectChange('status', value)}
                    >
                      <SelectTrigger dir={language === 'ps' ? 'rtl' : 'ltr'}>
                        <SelectValue placeholder={t('select_status')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="in-stock">{t('in_stock')}</SelectItem>
                        <SelectItem value="low-stock">{t('low_stock')}</SelectItem>
                        <SelectItem value="out-of-stock">{t('out_of_stock')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Medical Details */}
                <div className="md:col-span-2 space-y-4">
                  <div>
                    <Label htmlFor="description">{t('description')}</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description || ''}
                      onChange={handleTextareaChange}
                      placeholder={t('medicine_description')}
                      rows={3}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dosage">{t('dosage')}</Label>
                    <Input
                      id="dosage"
                      name="dosage"
                      value={formData.dosage || ''}
                      onChange={handleChange}
                      placeholder={t('medicine_dosage')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="administration">{t('administration')}</Label>
                      <Select
                        value={formData.administration || 'oral'}
                        onValueChange={(value) => handleSelectChange('administration', value)}
                      >
                        <SelectTrigger dir={language === 'ps' ? 'rtl' : 'ltr'}>
                          <SelectValue placeholder={t('select_administration_method')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="oral">{t('oral')}</SelectItem>
                          <SelectItem value="injection">{t('injection')}</SelectItem>
                          <SelectItem value="topical">{t('topical')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="storage">{t('storage')}</Label>
                      <Select
                        value={formData.storage || 'room-temperature'}
                        onValueChange={(value) => handleSelectChange('storage', value)}
                      >
                        <SelectTrigger dir={language === 'ps' ? 'rtl' : 'ltr'}>
                          <SelectValue placeholder={t('select_storage_condition')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="room-temperature">{t('room_temperature')}</SelectItem>
                          <SelectItem value="refrigerated">{t('refrigerated')}</SelectItem>
                          <SelectItem value="frozen">{t('frozen')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>

              <div className={`flex ${language === 'ps' ? 'justify-start' : 'justify-end'}`}>
                <Button
                  type="submit"
                  disabled={loading}
                  variant="primary"
                  className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                >
                  {loading ? (
                    <>
                      <svg
                        className={`animate-spin h-5 w-5 text-white ${language === 'ps' ? 'ml-2' : 'mr-2'}`}
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      {t('saving')}
                    </>
                  ) : (
                    <>
                      <Save className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                      {t('save_medicine')}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditMedicinePage;
