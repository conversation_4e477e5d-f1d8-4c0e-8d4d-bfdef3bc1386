/* eslint-disable no-unused-vars */
import {
  FaFacebook,
  FaHome,
  FaInfoCircle,
  FaNewspaper,
  FaPhone,
  FaServicestack,
  FaTelegram,
  FaWhatsapp,
} from 'react-icons/fa';
import { Link } from 'react-router-dom';
import Logo from '../Logo';
import { useTranslation } from 'react-i18next';

const Footer = () => {
  const { t, i18n } = useTranslation();
  
  return (
    <footer className="bg-gradient-to-r from-gray-950 to-gray-600 text-white text-center p-6 shadow-lg">
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-4 gap-8 text-left">
        <div className="flex items-center  ">
          <Logo />
        </div>
        {/* Links Section */}
        <div className='flex items-start flex-col'>
          <h3 className="text-lg font-semibold mb-3">{t('footer.links.title')}</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaHome /> {t('footer.links.home')}
              </Link>
            </li>
            <li>
              <Link to="/services" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaServicestack /> {t('footer.links.services')}
              </Link>
            </li>
            <li>
              <Link to="/news" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaNewspaper /> {t('navigation.news')}
              </Link>
            </li>
            <li>
              <Link to="/about" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaInfoCircle /> {t('navigation.about')}
              </Link>
            </li>
            <li>
              <Link to="/contact" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaPhone /> {t('navigation.contact')}
              </Link>
            </li>
          </ul>
        </div>

        {/* Social Media Section */}
        <div className='flex items-start flex-col'>
          <h3 className="text-lg font-semibold mb-3">{t('footer.title2')}</h3>
          <ul className="space-y-2">
            <li>
              <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaFacebook /> {t('footer.social.facebook')}
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaWhatsapp /> {t('footer.social.whatsapp')}
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
                <FaTelegram /> {t('footer.social.telegram')}
              </a>
            </li>
          </ul>
        </div>

        {/* Contact Section */}
        <div className='flex items-start flex-col'>
          <h3 className="text-lg font-semibold mb-3">{t('footer.title3')}</h3>
          <ul className="space-y-2">
            <li className="text-gray-300">{t('footer.phone')}</li>
            <li>
              <a
                href="https://www.ansariltd.com"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-gray-300 transition"
              >
                WWW.ansariltd.com
              </a>
            </li>
            <li>
              <a href="mailto:<EMAIL>" className="hover:text-gray-300 transition">
                {t('footer.email')}
              </a>
            </li>
            <li className="text-gray-300">{t('footer.address')}</li>
          </ul>
        </div>
      </div>

      <div className="border-t border-gray-500 mt-6 pt-4">
        <p className="text-sm">&copy; {new Date().getFullYear()} {t('company.name')} {t('company.ltd')}. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;


// /* eslint-disable no-unused-vars */
// import {
//   FaFacebook,
//   FaHome,
//   FaInfoCircle,
//   FaNewspaper,
//   FaPhone,
//   FaServicestack,
//   FaTelegram,
//   FaWhatsapp,
// } from 'react-icons/fa';
// import { Link } from 'react-router-dom';
// import Logo from '../Logo';
// import { useTranslation } from 'react-i18next';
// const Footer = () => {
//   const { t, i18n } = useTranslation();
//   return (
//     <footer className="bg-gradient-to-r from-gray-950 to-gray-600 text-white text-center p-6 shadow-lg">
//       <div className="container mx-auto grid grid-cols-1 md:grid-cols-4 gap-8 text-left">
//         <div className="flex items-center ">
//           <Logo />
//         </div>
//         {/* Links Section */}
//         <div>
//           <h3 className="text-lg font-semibold mb-3">{t('footer.links.title')}</h3>
//           <ul className="space-y-2">
//             <li>
//               <Link to="/" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaHome /> {t('footer.links.home')}
//               </Link>
//             </li>
//             <li>
//               <Link to="/services" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaServicestack /> {t('footer.links.services')}
//               </Link>
//             </li>
//             <li>
//               <Link to="/news" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaNewspaper /> News & Blog
//               </Link>
//             </li>
//             <li>
//               <Link to="/about" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaInfoCircle /> About Us
//               </Link>
//             </li>
//             <li>
//               <Link to="/contact" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaPhone /> Contact Us
//               </Link>
//             </li>
//           </ul>
//         </div>

//         {/* Social Media Section */}
//         <div>
//           <h3 className="text-lg font-semibold mb-3">Follow Us</h3>
//           <ul className="space-y-2">
//             <li>
//               <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaFacebook /> Facebook
//               </a>
//             </li>
//             <li>
//               <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaWhatsapp /> Whatsapp
//               </a>
//             </li>
//             <li>
//               <a href="#" className="flex items-center gap-3 hover:text-gray-300 transition">
//                 <FaTelegram /> Telegram
//               </a>
//             </li>
//           </ul>
//         </div>

//         {/* Contact Section */}
//         <div>
//           <h3 className="text-lg font-semibold mb-3">Contact Us</h3>
//           <ul className="space-y-2">
//             <li className="text-gray-300">0712345678</li>
//             <li>
//               <a
//                 href="https://www.ansariltd.com"
//                 target="_blank"
//                 rel="noopener noreferrer"
//                 className="hover:text-gray-300 transition"
//               >
//                 WWW.ansariltd.com
//               </a>
//             </li>
//             <li>
//               <a href="mailto:<EMAIL>" className="hover:text-gray-300 transition">
//                 <EMAIL>
//               </a>
//             </li>
//             <li className="text-gray-300">Address: Kandahar, Afghanistan</li>
//           </ul>
//         </div>
//       </div>

//       <div className="border-t border-gray-500 mt-6 pt-4">
//         <p className="text-sm">&copy; {new Date().getFullYear()} My Website. All rights reserved.</p>
//       </div>
//     </footer>
//   );
// };

// export default Footer;
