/* eslint-disable react/prop-types */
// src/components/management-system/ui/Textarea.jsx

const Textarea = ({ value, onChange, placeholder, rows = 4, className = '' }) => {
  return (
    <textarea
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
      className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 ${className}`}
    />
  );
};

export { Textarea };
