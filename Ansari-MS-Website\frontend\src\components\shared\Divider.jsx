import React from 'react';

const Divider = React.forwardRef(({ orientation = 'horizontal', variant = 'solid', className = '', ...props }, ref) => {
  const baseStyles = 'border-gray-200 dark:border-gray-700';

  const orientations = {
    horizontal: 'w-full border-t',
    vertical: 'h-full border-l',
  };

  const variants = {
    solid: 'border-solid',
    dashed: 'border-dashed',
    dotted: 'border-dotted',
  };

  return (
    <div
      ref={ref}
      className={`${baseStyles} ${orientations[orientation]} ${variants[variant]} ${className}`}
      role="separator"
      {...props}
    />
  );
});

Divider.displayName = 'Divider';

export default Divider;
