import express from 'express';
import chickenController from '../controllers/chickenController.js';
import { authenticate } from "../middlewares/authMiddleware.js";

const router = express.Router();

// All routes require authentication but not admin authorization
router.use(authenticate);

// Get distributions for the authenticated user's shop
router.get('/distributions/:shopId', chickenController.getShopDistributions);

export default router;
