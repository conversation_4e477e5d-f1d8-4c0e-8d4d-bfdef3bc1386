import React, { createContext, useState, useContext, useEffect } from 'react';
import { useLanguage } from './LanguageContext';
import { translations } from './LanguageContext';

const ReportContext = createContext();

export const ReportProvider = ({ children }) => {
  const [reports, setReports] = useState([]);
  const { language } = useLanguage();
  const t = (key) => translations[language][key];

  // Load reports from localStorage on initial render
  useEffect(() => {
    const savedReports = localStorage.getItem('reports');
    if (savedReports) {
      setReports(JSON.parse(savedReports));
    }
  }, []);

  // Save reports to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('reports', JSON.stringify(reports));
  }, [reports]);

  const generateReport = (type, data, period) => {
    const report = {
      id: Date.now(),
      type,
      period,
      data,
      timestamp: new Date(),
      title: `${t(type)} - ${period}`,
    };

    setReports((prev) => [report, ...prev]);
    return report;
  };

  const getWeeklyReport = (weekStart, weekEnd) => {
    const weeklyData = {
      chickenSales: calculateChickenSales(weekStart, weekEnd),
      medicineUsage: calculateMedicineUsage(weekStart, weekEnd),
      feedConsumption: calculateFeedConsumption(weekStart, weekEnd),
      revenue: calculateRevenue(weekStart, weekEnd),
      expenses: calculateExpenses(weekStart, weekEnd),
    };

    return generateReport('weekly_report', weeklyData, `${weekStart} to ${weekEnd}`);
  };

  const getMonthlyReport = (month, year) => {
    const monthlyData = {
      chickenSales: calculateChickenSales(month, year),
      medicineUsage: calculateMedicineUsage(month, year),
      feedConsumption: calculateFeedConsumption(month, year),
      revenue: calculateRevenue(month, year),
      expenses: calculateExpenses(month, year),
      profit: calculateProfit(month, year),
    };

    return generateReport('monthly_report', monthlyData, `${month}/${year}`);
  };

  const getAnnualReport = (year) => {
    const annualData = {
      totalChickenSales: calculateTotalChickenSales(year),
      totalMedicineUsage: calculateTotalMedicineUsage(year),
      totalFeedConsumption: calculateTotalFeedConsumption(year),
      totalRevenue: calculateTotalRevenue(year),
      totalExpenses: calculateTotalExpenses(year),
      annualProfit: calculateAnnualProfit(year),
      monthlyBreakdown: generateMonthlyBreakdown(year),
    };

    return generateReport('annual_report', annualData, year.toString());
  };

  // Helper functions for calculations
  const calculateChickenSales = (startDate, endDate) => {
    // Implement chicken sales calculation logic
    return {
      totalSold: 0,
      revenue: 0,
      averagePrice: 0,
    };
  };

  const calculateMedicineUsage = (startDate, endDate) => {
    // Implement medicine usage calculation logic
    return {
      totalUsed: 0,
      cost: 0,
      mostUsed: [],
    };
  };

  const calculateFeedConsumption = (startDate, endDate) => {
    // Implement feed consumption calculation logic
    return {
      totalConsumed: 0,
      cost: 0,
      averagePerChicken: 0,
    };
  };

  const calculateRevenue = (startDate, endDate) => {
    // Implement revenue calculation logic
    return {
      total: 0,
      byCategory: {},
    };
  };

  const calculateExpenses = (startDate, endDate) => {
    // Implement expenses calculation logic
    return {
      total: 0,
      byCategory: {},
    };
  };

  const calculateProfit = (month, year) => {
    // Implement profit calculation logic
    return {
      total: 0,
      margin: 0,
    };
  };

  const calculateTotalChickenSales = (year) => {
    // Implement total chicken sales calculation logic
    return {
      total: 0,
      monthlyBreakdown: {},
    };
  };

  const calculateTotalMedicineUsage = (year) => {
    // Implement total medicine usage calculation logic
    return {
      total: 0,
      monthlyBreakdown: {},
    };
  };

  const calculateTotalFeedConsumption = (year) => {
    // Implement total feed consumption calculation logic
    return {
      total: 0,
      monthlyBreakdown: {},
    };
  };

  const calculateTotalRevenue = (year) => {
    // Implement total revenue calculation logic
    return {
      total: 0,
      monthlyBreakdown: {},
    };
  };

  const calculateTotalExpenses = (year) => {
    // Implement total expenses calculation logic
    return {
      total: 0,
      monthlyBreakdown: {},
    };
  };

  const calculateAnnualProfit = (year) => {
    // Implement annual profit calculation logic
    return {
      total: 0,
      margin: 0,
      monthlyBreakdown: {},
    };
  };

  const generateMonthlyBreakdown = (year) => {
    // Implement monthly breakdown generation logic
    return {};
  };

  return (
    <ReportContext.Provider
      value={{
        reports,
        getWeeklyReport,
        getMonthlyReport,
        getAnnualReport,
      }}
    >
      {children}
    </ReportContext.Provider>
  );
};

export const useReports = () => useContext(ReportContext);
