import React, { createContext, useContext, useState, useEffect } from 'react';
import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';
import { toast } from 'react-hot-toast';
import api from '../utils/api';

const FeedContext = createContext();

export function FeedProvider({ children }) {
  const [feeds, setFeeds] = useState(() => loadFromLocalStorage('feeds', []));
  const [seeds, setSeeds] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    saveToLocalStorage('feeds', feeds);
  }, [feeds]);

  useEffect(() => {
    fetchSeeds();
  }, []);

  const clearFeeds = () => {
    setFeeds([]);
    removeFromLocalStorage('feeds');
  };

  const addFeed = async (feedData) => {
    try {
      setLoading(true);
      const newFeed = {
        ...feedData,
        id: feeds.length + 1,
        createdAt: new Date().toISOString(),
      };
      setFeeds((prev) => [...prev, newFeed]);
      toast.success('Feed added successfully');
    } catch (error) {
      toast.error('Failed to add feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateFeed = async (id, feedData) => {
    try {
      setLoading(true);
      setFeeds((prev) => prev.map((feed) => (feed.id === id ? { ...feed, ...feedData } : feed)));
      toast.success('Feed updated successfully');
    } catch (error) {
      toast.error('Failed to update feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteFeed = async (id) => {
    try {
      setLoading(true);
      setFeeds((prev) => prev.filter((feed) => feed.id !== id));
      toast.success('Feed deleted successfully');
    } catch (error) {
      toast.error('Failed to delete feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Seeds API functions
  const fetchSeeds = async () => {
    try {
      setLoading(true);
      const response = await api.get('/seeds');
      if (response.data.success) {
        setSeeds(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching seeds:', error);
      toast.error('Failed to fetch seeds');
    } finally {
      setLoading(false);
    }
  };

  const addSeed = async (seedData) => {
    try {
      setLoading(true);
      const response = await api.post('/seeds', seedData);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed added successfully');
        return response.data.seed;
      }
    } catch (error) {
      console.error('Error adding seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to add seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateSeed = async (id, seedData) => {
    try {
      setLoading(true);
      const response = await api.put(`/seeds/${id}`, seedData);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed updated successfully');
        return response.data.seed;
      }
    } catch (error) {
      console.error('Error updating seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to update seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteSeed = async (id) => {
    try {
      setLoading(true);
      const response = await api.delete(`/seeds/${id}`);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to delete seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <FeedContext.Provider
      value={{
        feeds,
        seeds,
        loading,
        addFeed,
        updateFeed,
        deleteFeed,
        clearFeeds,
        // Seeds functions
        fetchSeeds,
        addSeed,
        updateSeed,
        deleteSeed,
      }}
    >
      {children}
    </FeedContext.Provider>
  );
}

export function useFeed() {
  const context = useContext(FeedContext);
  if (!context) {
    throw new Error('useFeed must be used within a FeedProvider');
  }
  return context;
}
