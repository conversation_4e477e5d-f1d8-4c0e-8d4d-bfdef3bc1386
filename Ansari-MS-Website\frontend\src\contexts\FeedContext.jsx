import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import api from '../utils/api';

const FeedContext = createContext();

export function FeedProvider({ children }) {
  const [seeds, setSeeds] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSeeds();
  }, []);

  // Seeds API functions
  const fetchSeeds = async () => {
    try {
      setLoading(true);
      const response = await api.get('/seeds');
      if (response.data.success) {
        setSeeds(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching seeds:', error);
      toast.error('Failed to fetch seeds');
    } finally {
      setLoading(false);
    }
  };

  const addSeed = async (seedData) => {
    try {
      setLoading(true);
      const response = await api.post('/seeds', seedData);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed added successfully');
        return response.data.seed;
      }
    } catch (error) {
      console.error('Error adding seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to add seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateSeed = async (id, seedData) => {
    try {
      setLoading(true);
      const response = await api.put(`/seeds/${id}`, seedData);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed updated successfully');
        return response.data.seed;
      }
    } catch (error) {
      console.error('Error updating seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to update seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteSeed = async (id) => {
    try {
      setLoading(true);
      const response = await api.delete(`/seeds/${id}`);
      if (response.data.success) {
        await fetchSeeds(); // Refresh the list
        toast.success('Seed deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting seed:', error);
      const errorMessage = error.response?.data?.error || 'Failed to delete seed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <FeedContext.Provider
      value={{
        seeds,
        loading,
        // Seeds functions
        fetchSeeds,
        addSeed,
        updateSeed,
        deleteSeed,
      }}
    >
      {children}
    </FeedContext.Provider>
  );
}

export function useFeed() {
  const context = useContext(FeedContext);
  if (!context) {
    throw new Error('useFeed must be used within a FeedProvider');
  }
  return context;
}
