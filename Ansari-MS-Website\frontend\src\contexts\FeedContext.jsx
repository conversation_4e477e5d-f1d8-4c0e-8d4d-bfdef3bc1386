import React, { createContext, useContext, useState, useEffect } from 'react';
import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';
import { toast } from 'react-hot-toast';

const FeedContext = createContext();

export function FeedProvider({ children }) {
  const [feeds, setFeeds] = useState(() => loadFromLocalStorage('feeds', []));
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    saveToLocalStorage('feeds', feeds);
  }, [feeds]);

  const clearFeeds = () => {
    setFeeds([]);
    removeFromLocalStorage('feeds');
  };

  const addFeed = async (feedData) => {
    try {
      setLoading(true);
      const newFeed = {
        ...feedData,
        id: feeds.length + 1,
        createdAt: new Date().toISOString(),
      };
      setFeeds((prev) => [...prev, newFeed]);
      toast.success('Feed added successfully');
    } catch (error) {
      toast.error('Failed to add feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateFeed = async (id, feedData) => {
    try {
      setLoading(true);
      setFeeds((prev) => prev.map((feed) => (feed.id === id ? { ...feed, ...feedData } : feed)));
      toast.success('Feed updated successfully');
    } catch (error) {
      toast.error('Failed to update feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteFeed = async (id) => {
    try {
      setLoading(true);
      setFeeds((prev) => prev.filter((feed) => feed.id !== id));
      toast.success('Feed deleted successfully');
    } catch (error) {
      toast.error('Failed to delete feed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <FeedContext.Provider
      value={{
        feeds,
        loading,
        addFeed,
        updateFeed,
        deleteFeed,
        clearFeeds,
      }}
    >
      {children}
    </FeedContext.Provider>
  );
}

export function useFeed() {
  const context = useContext(FeedContext);
  if (!context) {
    throw new Error('useFeed must be used within a FeedProvider');
  }
  return context;
}
