import React from 'react';
import clsx from 'clsx';

// Use forwardRef to allow parent components to access the button DOM node
const Button = React.forwardRef(({ children, className, variant = 'primary', ...props }, ref) => {
  const baseStyles =
    'inline-flex items-center justify-center rounded-lg px-4 py-2 font-medium transition focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variants = {
    primary: 'bg-orange-500 text-white hover:bg-orange-600 focus:ring-orange-500',
    secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-300',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-300',
  };

  return (
    <button ref={ref} className={clsx(baseStyles, variants[variant], className)} {...props}>
      {children}
    </button>
  );
});

// Optional: Set a display name for easier debugging
Button.displayName = 'Button';

export { Button };
