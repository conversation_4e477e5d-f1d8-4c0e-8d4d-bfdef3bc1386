import pool from "../config/db.js";

const chickenModel = {
  // Create initial chicken purchase from supplier
  createPurchase: async (data) => {
    const [rows] = await pool.query(
      `INSERT INTO ChickenPurchases (
        purchaseDate, quantity, pricePerChicken, totalPrice, 
        supplierName, supplierContact, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'purchased')`,
      [
        data.purchaseDate,
        data.quantity,
        data.pricePerChicken,
        data.totalPrice,
        data.supplierName,
        data.supplierContact || '',
        data.notes || '',
      ]
    );
    return { id: rows.insertId, ...data };
  },

  // Allocate chickens to farm
  allocateToFarm: async (data) => {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Create farm allocation record
      const [allocationResult] = await connection.query(
        `INSERT INTO ChickenFarmAllocations (
          purchaseId, farmId, allocationDate, quantity, 
          pricePerChicken, totalPrice, expectedReturnDate, status
        ) VALUES (?, ?, ?, ?, ?, ?, DATE_ADD(?, INTERVAL 45 DAY), 'allocated')`,
        [
          data.purchaseId,
          data.farmId,
          data.allocationDate,
          data.quantity,
          data.pricePerChicken,
          data.totalPrice,
          data.allocationDate
        ]
      );

      // Update purchase status to partially or fully allocated
      await connection.query(
        `UPDATE ChickenPurchases 
         SET status = CASE 
           WHEN (SELECT SUM(quantity) FROM ChickenFarmAllocations WHERE purchaseId = ?) >= quantity 
           THEN 'fully_allocated' 
           ELSE 'partially_allocated' 
         END 
         WHERE id = ?`,
        [data.purchaseId, data.purchaseId]
      );

      await connection.commit();
      return { id: allocationResult.insertId, ...data };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  // Buy back chickens from farm
  buyBackFromFarm: async (data) => {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Create buyback record
      const [buybackResult] = await connection.query(
        `INSERT INTO ChickenBuybacks (
          allocationId, buybackDate, quantity, pricePerChicken, 
          totalPrice, farmId, daysCompleted, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'bought_back')`,
        [
          data.allocationId,
          data.buybackDate,
          data.quantity,
          data.pricePerChicken,
          data.totalPrice,
          data.farmId,
          data.daysCompleted
        ]
      );

      // Update allocation status
      await connection.query(
        `UPDATE ChickenFarmAllocations 
         SET status = 'completed', actualReturnDate = ? 
         WHERE id = ?`,
        [data.buybackDate, data.allocationId]
      );

      await connection.commit();
      return { id: buybackResult.insertId, ...data };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  // Distribute chickens to shops
  distributeToShop: async (data) => {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      // Create shop distribution record
      const [distributionResult] = await connection.query(
        `INSERT INTO ChickenShopDistributions (
          buybackId, shopId, distributionDate, quantity, 
          pricePerChicken, totalPrice, status
        ) VALUES (?, ?, ?, ?, ?, ?, 'distributed')`,
        [
          data.buybackId,
          data.shopId,
          data.distributionDate,
          data.quantity,
          data.pricePerChicken,
          data.totalPrice
        ]
      );

      // Update buyback status
      await connection.query(
        `UPDATE ChickenBuybacks 
         SET status = CASE 
           WHEN (SELECT SUM(quantity) FROM ChickenShopDistributions WHERE buybackId = ?) >= quantity 
           THEN 'fully_distributed' 
           ELSE 'partially_distributed' 
         END 
         WHERE id = ?`,
        [data.buybackId, data.buybackId]
      );

      await connection.commit();
      return { id: distributionResult.insertId, ...data };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  },

  // Get all purchases
  getAllPurchases: async () => {
    const [rows] = await pool.query(`
      SELECT p.*, 
        COALESCE(SUM(a.quantity), 0) as allocatedQuantity,
        (p.quantity - COALESCE(SUM(a.quantity), 0)) as remainingQuantity
      FROM ChickenPurchases p
      LEFT JOIN ChickenFarmAllocations a ON p.id = a.purchaseId
      GROUP BY p.id
      ORDER BY p.purchaseDate DESC
    `);
    return rows;
  },

  // Get all farm allocations
  getAllAllocations: async () => {
    const [rows] = await pool.query(`
      SELECT a.*, p.supplierName, f.FName as farmName, CONCAT(u.U_FirstName, ' ', u.U_LastName) as farmOwner,
        DATEDIFF(CURDATE(), a.allocationDate) as daysElapsed,
        CASE
          WHEN DATEDIFF(CURDATE(), a.allocationDate) >= 45 THEN 'ready_for_buyback'
          ELSE 'in_progress'
        END as readyStatus,
        COALESCE(SUM(b.quantity), 0) as boughtBackQuantity,
        (a.quantity - COALESCE(SUM(b.quantity), 0)) as remainingQuantity
      FROM ChickenFarmAllocations a
      JOIN ChickenPurchases p ON a.purchaseId = p.id
      JOIN Farms f ON a.farmId = f.F_Id
      JOIN users u ON f.owner = u.u_Id
      LEFT JOIN ChickenBuybacks b ON a.id = b.allocationId
      GROUP BY a.id
      ORDER BY a.allocationDate DESC
    `);
    return rows;
  },

  // Get all buybacks
  getAllBuybacks: async () => {
    const [rows] = await pool.query(`
      SELECT b.*, a.allocationDate, f.FName as farmName, CONCAT(u.U_FirstName, ' ', u.U_LastName) as farmOwner,
        COALESCE(SUM(d.quantity), 0) as distributedQuantity,
        (b.quantity - COALESCE(SUM(d.quantity), 0)) as remainingQuantity
      FROM ChickenBuybacks b
      JOIN ChickenFarmAllocations a ON b.allocationId = a.id
      JOIN Farms f ON b.farmId = f.F_Id
      JOIN users u ON f.owner = u.u_Id
      LEFT JOIN ChickenShopDistributions d ON b.id = d.buybackId
      GROUP BY b.id
      ORDER BY b.buybackDate DESC
    `);
    return rows;
  },

  // Get all shop distributions
  getAllDistributions: async () => {
    try {
      console.log('🔍 Fetching all distributions...');

      // First, let's try a simple query to see if distributions exist
      const [simpleRows] = await pool.query(`SELECT * FROM ChickenShopDistributions ORDER BY distributionDate DESC`);
      console.log('📊 Simple distributions count:', simpleRows.length);

      // Now try the complex query with LEFT JOINs to handle missing data
      const [rows] = await pool.query(`
        SELECT d.*,
          b.buybackDate,
          COALESCE(s.SName, 'Unknown Shop') as shopName,
          COALESCE(CONCAT(shop_owner.U_FirstName, ' ', shop_owner.U_LastName), 'Unknown Owner') as shopOwner,
          COALESCE(f.FName, 'Unknown Farm') as farmName
        FROM ChickenShopDistributions d
        LEFT JOIN ChickenBuybacks b ON d.buybackId = b.id
        LEFT JOIN Shop s ON d.shopId = s.SId
        LEFT JOIN users shop_owner ON s.SOwner = shop_owner.u_Id
        LEFT JOIN ChickenFarmAllocations a ON b.allocationId = a.id
        LEFT JOIN Farms f ON a.farmId = f.F_Id
        ORDER BY d.distributionDate DESC
      `);
      console.log('✅ Distributions with JOINs fetched:', rows.length, 'records');
      return rows;
    } catch (error) {
      console.error('❌ SQL Error in getAllDistributions:', error);
      console.error('❌ Error details:', error.message);
      throw error;
    }
  },

  // Get distributions for a specific shop
  getDistributionsByShop: async (shopId) => {
    try {
      const [rows] = await pool.query(`
        SELECT d.*,
          b.buybackDate,
          COALESCE(f.FName, 'Unknown Farm') as farmName,
          COALESCE(CONCAT(farm_owner.U_FirstName, ' ', farm_owner.U_LastName), 'Unknown Farm Owner') as farmOwner
        FROM ChickenShopDistributions d
        LEFT JOIN ChickenBuybacks b ON d.buybackId = b.id
        LEFT JOIN ChickenFarmAllocations a ON b.allocationId = a.id
        LEFT JOIN Farms f ON a.farmId = f.F_Id
        LEFT JOIN users farm_owner ON f.owner = farm_owner.u_Id
        WHERE d.shopId = ?
        ORDER BY d.distributionDate DESC
      `, [shopId]);
      return rows;
    } catch (error) {
      console.error('❌ SQL Error in getDistributionsByShop:', error);
      throw error;
    }
  },

  // Get purchase by ID
  getPurchaseById: async (id) => {
    const [rows] = await pool.query(
      "SELECT * FROM ChickenPurchases WHERE id = ?",
      [id]
    );
    return rows[0];
  },

  // Get allocation by ID
  getAllocationById: async (id) => {
    const [rows] = await pool.query(`
      SELECT a.*, p.supplierName, f.FName as farmName, CONCAT(u.U_FirstName, ' ', u.U_LastName) as farmOwner
      FROM ChickenFarmAllocations a
      JOIN ChickenPurchases p ON a.purchaseId = p.id
      JOIN Farms f ON a.farmId = f.F_Id
      JOIN users u ON f.owner = u.u_Id
      WHERE a.id = ?
    `, [id]);
    return rows[0];
  },

  // Get buyback by ID
  getBuybackById: async (id) => {
    const [rows] = await pool.query(`
      SELECT b.*, a.allocationDate, f.FName as farmName, CONCAT(u.U_FirstName, ' ', u.U_LastName) as farmOwner
      FROM ChickenBuybacks b
      JOIN ChickenFarmAllocations a ON b.allocationId = a.id
      JOIN Farms f ON b.farmId = f.F_Id
      JOIN users u ON f.owner = u.u_Id
      WHERE b.id = ?
    `, [id]);
    return rows[0];
  },

  // Delete records (for admin)
  deletePurchase: async (id) => {
    const [result] = await pool.query("DELETE FROM ChickenPurchases WHERE id = ?", [id]);
    return result;
  },

  deleteAllocation: async (id) => {
    const [result] = await pool.query("DELETE FROM ChickenFarmAllocations WHERE id = ?", [id]);
    return result;
  },

  deleteBuyback: async (id) => {
    const [result] = await pool.query("DELETE FROM ChickenBuybacks WHERE id = ?", [id]);
    return result;
  },

  deleteDistribution: async (id) => {
    const [result] = await pool.query("DELETE FROM ChickenShopDistributions WHERE id = ?", [id]);
    return result;
  },

  // Get dashboard statistics
  getStatistics: async () => {
    const [stats] = await pool.query(`
      SELECT 
        (SELECT COUNT(*) FROM ChickenPurchases) as totalPurchases,
        (SELECT SUM(quantity) FROM ChickenPurchases) as totalChickensPurchased,
        (SELECT COUNT(*) FROM ChickenFarmAllocations WHERE status = 'allocated') as activeAllocations,
        (SELECT COUNT(*) FROM ChickenFarmAllocations WHERE DATEDIFF(CURDATE(), allocationDate) >= 45 AND status = 'allocated') as readyForBuyback,
        (SELECT COUNT(*) FROM ChickenBuybacks) as totalBuybacks,
        (SELECT SUM(quantity) FROM ChickenBuybacks) as totalChickensBoughtBack,
        (SELECT COUNT(*) FROM ChickenShopDistributions) as totalDistributions,
        (SELECT SUM(quantity) FROM ChickenShopDistributions) as totalChickensDistributed
    `);
    return stats[0];
  },

  // Complete Process Report
  getCompleteProcessReport: async () => {
    try {
      console.log('🔍 Generating complete process report...');

      // Get comprehensive process data with all stages
      const [rows] = await pool.query(`
        SELECT
          p.id as purchaseId,
          p.purchaseDate,
          p.supplierName,
          p.quantity as initialQuantity,
          p.totalPrice as purchaseCost,
          p.pricePerChicken as purchasePricePerChicken,
          p.status as purchaseStatus,

          -- Allocation data
          a.id as allocationId,
          a.allocationDate,
          a.quantity as allocatedQuantity,
          a.totalPrice as allocationCost,
          a.status as allocationStatus,
          f.FName as farmName,
          CONCAT(u.U_FirstName, ' ', u.U_LastName) as farmOwner,

          -- Buyback data
          b.id as buybackId,
          b.buybackDate,
          b.quantity as buybackQuantity,
          b.totalPrice as buybackRevenue,
          b.daysCompleted,
          b.status as buybackStatus,

          -- Distribution data
          d.id as distributionId,
          d.distributionDate,
          d.quantity as distributedQuantity,
          d.totalPrice as distributionRevenue,
          d.status as distributionStatus,
          s.SName as shopName,

          -- Process completion status
          CASE
            WHEN d.id IS NOT NULL THEN 'completed'
            WHEN b.id IS NOT NULL THEN 'bought_back'
            WHEN a.id IS NOT NULL THEN 'allocated'
            ELSE 'purchased'
          END as processStatus

        FROM ChickenPurchases p
        LEFT JOIN ChickenFarmAllocations a ON p.id = a.purchaseId
        LEFT JOIN Farms f ON a.farmId = f.F_Id
        LEFT JOIN users u ON f.owner = u.u_Id
        LEFT JOIN ChickenBuybacks b ON a.id = b.allocationId
        LEFT JOIN ChickenShopDistributions d ON b.id = d.buybackId
        LEFT JOIN Shop s ON d.shopId = s.SId
        ORDER BY p.purchaseDate DESC, a.allocationDate, b.buybackDate, d.distributionDate
      `);

      console.log('✅ Process report data fetched:', rows.length, 'records');
      return rows;
    } catch (error) {
      console.error('❌ SQL Error in getCompleteProcessReport:', error);
      console.error('❌ Error details:', error.message);
      throw error;
    }
  }
};

export default chickenModel;
