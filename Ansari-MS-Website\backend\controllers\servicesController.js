import <PERSON><PERSON> from "joi";
import multer from "multer";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { fileURLToPath } from "url";

import asyncHandler from "../middlewares/asyncHandler.js";
import servicesModel from "../models/servicesModel.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Multer Storage & Filter
const multerStorage = multer.memoryStorage();
const multerFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image")) {
    cb(null, true);
  } else {
    cb(new Error("Not an image! Please upload only images."), false);
  }
};

const upload = multer({ storage: multerStorage, fileFilter: multerFilter });
const uploadUserPhoto = upload.single("image");

// Image Processing Middleware
const resizeUserPhoto = asyncHandler(async (req, res, next) => {
  if (!req.file) return next();

  const dir = path.join(__dirname, ".././public/images/services");
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const filename = `service-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}.jpeg`;
  req.file.filename = filename;

  await sharp(req.file.buffer)
    .resize(500, 500)
    .toFormat("jpeg")
    .jpeg({ quality: 90 })
    .toFile(path.join(dir, filename));

  req.body.Image = filename;
  next();
});

const serviceSchema = Joi.object({
  Title: Joi.string().min(3).required(),
  Description: Joi.string().min(5).required(),
  Price: Joi.number().min(0).default(0),
  Image: Joi.string().default("default-service-image.jpg"),
  Category: Joi.string()
    .valid("Premium Hens", "Baby Chickens", "Wholesale")
    .required(),
  Status: Joi.string().valid("active", "inactive").default("active"),
  Features: Joi.array().items(Joi.string()).max(30).optional().allow(null),
});

const servicesController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = serviceSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const newService = await servicesModel.create(value);
    res
      .status(201)
      .json({ success: true, message: "Service created", data: newService });
  }),

  getAll: asyncHandler(async (req, res) => {
    const data = await servicesModel.getAll();
    res.json({ success: true, total: data.length, data });
  }),

  getById: asyncHandler(async (req, res) => {
    const service = await servicesModel.getById(req.params.id);
    if (!service)
      return res
        .status(404)
        .json({ success: false, error: "Service not found" });
    res.json({ success: true, data: service });
  }),

  update: asyncHandler(async (req, res) => {
    const existing = await servicesModel.getById(req.params.id);
    if (!existing)
      return res
        .status(404)
        .json({ success: false, error: "Service not found" });

    const fullData = {
      Title: req.body.Title ?? existing.Title,
      Description: req.body.Description ?? existing.Description,
      Price: req.body.Price ?? existing.Price,
      Image: req.body.Image ?? existing.Image,
      Category: req.body.Category ?? existing.Category,
      Status: req.body.Status ?? existing.Status,
      Features: req.body.Features ?? existing.Features,
      // Lid: req.body.Lid ?? existing.Lid,
    };

    const { error } = serviceSchema.validate(fullData);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const updated = await servicesModel.update(req.params.id, fullData);
    res.json({ success: true, message: "Service updated", data: updated });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await servicesModel.delete(req.params.id);
    if (result.affectedRows === 0)
      return res
        .status(404)
        .json({ success: false, error: "Service not found" });

    res.json({ success: true, message: "Service deleted successfully" });
  }),
};

export { uploadUserPhoto, resizeUserPhoto };
export default servicesController;
