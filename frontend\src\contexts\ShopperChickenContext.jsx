import { createContext, useContext, useState } from 'react';
import api from '../utils/api';
import { toast } from 'react-hot-toast';

const ShopperChickenContext = createContext();

export const useShopperChicken = () => {
  const context = useContext(ShopperChickenContext);
  if (!context) {
    throw new Error('useShopperChicken must be used within a ShopperChickenProvider');
  }
  return context;
};

export const ShopperChickenProvider = ({ children }) => {
  const [loading, setLoading] = useState(false);
  const [distributions, setDistributions] = useState([]);
  const [userShop, setUserShop] = useState(null);

  // Fetch user's shop information
  const fetchUserShop = async (userId) => {
    try {
      const response = await api.get(`/shops`);
      if (response.data.success) {
        // Find the shop owned by this user
        const shop = response.data.data.find(shop => shop.userId === userId);
        setUserShop(shop);
        return shop;
      }
    } catch (error) {
      console.error('Error fetching user shop:', error);
      toast.error('Failed to fetch shop information');
    }
    return null;
  };

  // Fetch distributions for a specific shop
  const fetchShopDistributions = async (shopId) => {
    try {
      setLoading(true);
      const response = await api.get(`/shopper/chickens/distributions/${shopId}`);
      if (response.data.success) {
        setDistributions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching shop distributions:', error);
      toast.error('Failed to fetch chicken distributions');
    } finally {
      setLoading(false);
    }
  };

  // Fetch distributions for the current user
  const fetchUserDistributions = async (userId) => {
    try {
      setLoading(true);
      // First get the user's shop
      const shop = await fetchUserShop(userId);
      if (shop) {
        // Then get distributions for that shop
        await fetchShopDistributions(shop.SId);
      }
    } catch (error) {
      console.error('Error fetching user distributions:', error);
      toast.error('Failed to fetch chicken distributions');
    } finally {
      setLoading(false);
    }
  };

  const value = {
    loading,
    distributions,
    userShop,
    fetchShopDistributions,
    fetchUserShop,
    fetchUserDistributions,
  };

  return (
    <ShopperChickenContext.Provider value={value}>
      {children}
    </ShopperChickenContext.Provider>
  );
};
