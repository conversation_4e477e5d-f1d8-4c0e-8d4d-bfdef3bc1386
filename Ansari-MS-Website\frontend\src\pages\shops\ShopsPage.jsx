import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  MoreVertical,
  Activity,
  AlertTriangle,
  XCircle,
  Trash,
  Pencil,
} from 'lucide-react';
import { useShop } from '../../contexts/ShopContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import LocationMapCell from '../../components/LocationMapCell';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from 'recharts';
import Link from '../../components/feed-components/Link';

const ShopsPage = () => {
  const navigate = useNavigate();
  const { shops, loading, deleteShop } = useShop();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredShops = useMemo(() => {
    let filtered = shops.filter((shop) => {
      const matchesSearch = shop.name.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'owner':
          return a.owner.localeCompare(b.owner);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'phone':
          return a.phone.localeCompare(b.phone);
        default:
          return 0;
      }
    });

    return filtered;
  }, [shops, searchTerm, sortBy]);

  const chartData = useMemo(() => {
    // Create meaningful chart data based on actual shop data
    // Group shops by owner and count them
    const ownerCounts = {};
    filteredShops.forEach((shop) => {
      const owner = shop.owner || 'Unknown';
      ownerCounts[owner] = (ownerCounts[owner] || 0) + 1;
    });

    // Convert to chart format and limit to top 10 owners
    return Object.entries(ownerCounts)
      .map(([owner, count]) => ({
        name: owner.length > 15 ? owner.substring(0, 15) + '...' : owner,
        fullName: owner,
        shops: count,
      }))
      .sort((a, b) => b.shops - a.shops)
      .slice(0, 10); // Show top 10 owners
  }, [filteredShops]);

  const handleEdit = (id) => {
    navigate(`/admin/shops/edit/${id}`);
  };

  const handleDelete = async (id) => {
    if (window.confirm(t('confirm_delete_shop') || 'Are you sure you want to delete this shop?')) {
      try {
        await deleteShop(id);
      } catch (error) {
        console.error('Error deleting shop:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_shops') || 'Loading shops...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('shop_management') || 'Shop Management'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_shops_description') || 'Manage and monitor all shops in the system'}
          </p>
        </div>
        <div className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Link 
            to="/admin/shops/add" 
            className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
            <span className="text-lg">{t('add_new_shop') || 'Add New Shop'}</span>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader
            className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <CardTitle className="text-sm font-medium">{t('total_shops') || 'Total Shops'}</CardTitle>
            <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredShops.length}</div>
            <p className="text-xs text-muted-foreground">
              {searchTerm ? `${t('filtered_results') || 'Filtered results'}` : `${t('registered_shops') || 'Registered shops'}`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader
            className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <CardTitle className="text-sm font-medium">{t('unique_owners') || 'Unique Owners'}</CardTitle>
            <AlertTriangle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredShops.map(shop => shop.owner)).size}
            </div>
            <p className="text-xs text-muted-foreground">{t('different_shop_owners') || 'Different shop owners'}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader
            className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <CardTitle className="text-sm font-medium">{t('avg_shops_per_owner') || 'Avg Shops/Owner'}</CardTitle>
            <XCircle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredShops.length > 0 
                ? (filteredShops.length / new Set(filteredShops.map(shop => shop.owner)).size).toFixed(1)
                : '0'
              }
            </div>
            <p className="text-xs text-muted-foreground">{t('shops_per_owner') || 'Shops per owner'}</p>
          </CardContent>
        </Card>
      </div>

      {/* Shop Distribution by Owner Chart */}
      <Card>
        <CardHeader>
          <CardTitle>{t('shop_distribution_by_owner') || 'Shop Distribution by Owner'}</CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('top_shop_owners') || 'Top 10 shop owners by number of shops'}
          </p>
        </CardHeader>
        <CardContent>
          <div className="h-[350px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  interval={0}
                  fontSize={12}
                />
                <YAxis 
                  stroke="#666"
                  fontSize={12}
                  label={{ value: 'Number of Shops', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip 
                  formatter={(value) => [value, 'Number of Shops']}
                  labelFormatter={(label) => {
                    const item = chartData.find(d => d.name === label);
                    return `Owner: ${item?.fullName || label}`;
                  }}
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #ccc',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar 
                  dataKey="shops" 
                  fill="#FF6B00" 
                  name="Shops"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          {chartData.length === 0 && (
            <div className="flex items-center justify-center h-[200px] text-gray-500">
              <div className="text-center">
                <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>{t('no_shop_data') || 'No shop data available'}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search and Sort Controls */}
      <Card>
        <CardHeader>
          <CardTitle>{t('search_and_filter_shops') || 'Search & Filter Shops'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`flex flex-col lg:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            {/* Search Input */}
            <div className="relative flex-1">
              <Search
                className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
                size={20}
              />
              <input
                type="text"
                placeholder={t('search_shops_by_name') || 'Search shops by name...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
                dir={language === 'ps' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Sort Controls */}
            <div className={`flex gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              {/* Sort By */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div
                    className={`flex items-center justify-center px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600 cursor-pointer transition-all duration-200 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                  >
                    <Activity className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                    <span>{t('sort_by') || 'Sort'}: {t(sortBy) || sortBy}</span>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                  <DropdownMenuItem 
                    onClick={() => setSortBy('name')}
                    className={sortBy === 'name' ? 'bg-[#FF6B00] text-white' : ''}
                  >
                    {t('name') || 'Name'}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setSortBy('owner')}
                    className={sortBy === 'owner' ? 'bg-[#FF6B00] text-white' : ''}
                  >
                    {t('owner') || 'Owner'}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setSortBy('email')}
                    className={sortBy === 'email' ? 'bg-[#FF6B00] text-white' : ''}
                  >
                    {t('email') || 'Email'}
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => setSortBy('phone')}
                    className={sortBy === 'phone' ? 'bg-[#FF6B00] text-white' : ''}
                  >
                    {t('phone') || 'Phone'}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Clear Filters Button */}
              {(searchTerm || sortBy !== 'name') && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSortBy('name');
                  }}
                  className="px-4 py-3 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-all duration-200"
                >
                  <XCircle className="h-4 w-4 inline mr-1" />
                  {t('clear_all') || 'Clear All'}
                </button>
              )}
            </div>
          </div>

          {/* Search Results Info */}
          <div className="mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
            <div>
              {searchTerm && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-blue-700 text-sm">
                    {filteredShops.length > 0 
                      ? `Found ${filteredShops.length} shop${filteredShops.length === 1 ? '' : 's'} with name containing "${searchTerm}"`
                      : `No shops found with name containing "${searchTerm}"`
                    }
                    <button 
                      onClick={() => setSearchTerm('')}
                      className="ml-2 text-blue-600 hover:text-blue-800 underline"
                    >
                      Clear search
                    </button>
                  </p>
                </div>
              )}
            </div>
            
            {/* Results Count */}
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Showing {filteredShops.length} of {shops.length} shops
              {sortBy !== 'name' && (
                <span className="ml-1 text-[#FF6B00]">
                  (sorted by {sortBy})
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shops Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('shops_list') || 'Shops List'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('name') || 'Name'}</TableHead>
                  <TableHead>{t('email') || 'Email'}</TableHead>
                  <TableHead>{t('owner') || 'Owner'}</TableHead>
                  <TableHead>{t('phone') || 'Phone'}</TableHead>
                  <TableHead>{t('location') || 'Location'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredShops.length > 0 ? (
                  filteredShops.map((shop) => (
                    <TableRow key={shop.id}>
                      <TableCell className="font-medium">{shop.name}</TableCell>
                      <TableCell>{shop.email}</TableCell>
                      <TableCell>{shop.owner}</TableCell>
                      <TableCell>{shop.phone}</TableCell>
                      <TableCell>
                        <LocationMapCell location={shop.location} />
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(shop.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('edit') || 'Edit'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(shop.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      {t('no_shops_found') || 'No shops found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ShopsPage;
