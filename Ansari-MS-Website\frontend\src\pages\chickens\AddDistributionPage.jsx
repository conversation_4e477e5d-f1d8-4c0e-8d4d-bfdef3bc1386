import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Save, Store } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';

const AddDistributionPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { distributeToShop, buybacks, fetchBuybacks, shops } = useChicken();
  const { language, translations } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    buybackId: searchParams.get('buybackId') || '',
    shopId: '',
    distributionDate: new Date().toISOString().split('T')[0],
    quantity: '',
    pricePerChicken: '',
    totalPrice: '',
  });

  const [selectedBuyback, setSelectedBuyback] = useState(null);
  const [selectedShop, setSelectedShop] = useState(null);

  useEffect(() => {
    fetchBuybacks();
  }, []);

  useEffect(() => {
    if (formData.buybackId && buybacks.length > 0) {
      const buyback = buybacks.find(b => b.id.toString() === formData.buybackId);
      if (buyback) {
        setSelectedBuyback(buyback);
        setFormData(prev => ({
          ...prev,
          pricePerChicken: buyback.pricePerChicken.toString(),
        }));
      }
    }
  }, [formData.buybackId, buybacks]);

  useEffect(() => {
    if (formData.shopId && shops.length > 0) {
      const shop = shops.find(s => s.SId.toString() === formData.shopId);
      setSelectedShop(shop);
    }
  }, [formData.shopId, shops]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newData = { ...prev, [name]: value };
      
      // Auto-calculate total price when quantity or price per chicken changes
      if (name === 'quantity' || name === 'pricePerChicken') {
        const quantity = parseFloat(name === 'quantity' ? value : newData.quantity) || 0;
        const pricePerChicken = parseFloat(name === 'pricePerChicken' ? value : newData.pricePerChicken) || 0;
        newData.totalPrice = (quantity * pricePerChicken).toFixed(2);
      }
      
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.buybackId) throw new Error(t('buyback_required') || 'Please select a buyback');
    if (!formData.shopId) throw new Error(t('shop_required') || 'Please select a shop');
    if (!formData.distributionDate) throw new Error(t('distribution_date_required') || 'Distribution date is required');
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('quantity_required') || 'Valid quantity is required');
    if (!formData.pricePerChicken || formData.pricePerChicken <= 0) throw new Error(t('price_required') || 'Valid price per chicken is required');
    
    if (selectedBuyback && parseInt(formData.quantity) > selectedBuyback.remainingQuantity) {
      throw new Error(t('quantity_exceeds_available') || `Quantity cannot exceed available chickens (${selectedBuyback.remainingQuantity})`);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      const distributionData = {
        buybackId: parseInt(formData.buybackId),
        shopId: parseInt(formData.shopId),
        distributionDate: formData.distributionDate,
        quantity: parseInt(formData.quantity),
        pricePerChicken: parseFloat(formData.pricePerChicken),
        totalPrice: parseFloat(formData.totalPrice),
      };

      await distributeToShop(distributionData);
      
      setFeedback({
        type: 'success',
        message: t('distribution_created_successfully') || 'Chickens distributed to shop successfully',
      });

      setTimeout(() => {
        navigate('/admin/chickens/distributions');
      }, 1500);
    } catch (error) {
      console.error('Error creating distribution:', error);
      
      let errorMessage = error.message || t('create_distribution_error') || 'Failed to distribute chickens to shop';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const availableBuybacks = buybacks.filter(b => b.remainingQuantity > 0);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <Button
          variant="secondary"
          onClick={() => navigate('/admin/chickens/distributions')}
          className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back_to_distributions') || 'Back to Distributions'}
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('distribute_to_shop') || 'Distribute to Shop'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('distribute_chickens_to_shop_description') || 'Distribute chickens to a shop for sale'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="h-5 w-5" />
            {t('distribution_information') || 'Distribution Information'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Buyback Selection */}
              <div className="md:col-span-2">
                <label htmlFor="buybackId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('select_buyback') || 'Select Buyback (Available Chickens)'}
                </label>
                <select
                  id="buybackId"
                  name="buybackId"
                  value={formData.buybackId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                >
                  <option value="">{t('choose_buyback') || 'Choose a buyback...'}</option>
                  {availableBuybacks.map((buyback) => (
                    <option key={buyback.id} value={buyback.id}>
                      {buyback.farmName} - {buyback.remainingQuantity} chickens available 
                      (Buyback: {new Date(buyback.buybackDate).toLocaleDateString()})
                    </option>
                  ))}
                </select>
                {availableBuybacks.length === 0 && (
                  <p className="mt-2 text-sm text-yellow-600">
                    {t('no_available_buybacks') || 'No buybacks with available chickens'}
                  </p>
                )}
                {selectedBuyback && (
                  <div className="mt-2 p-3 bg-blue-50 rounded-md">
                    <div className="text-sm text-blue-700">
                      <div><strong>Farm:</strong> {selectedBuyback.farmName}</div>
                      <div><strong>Owner:</strong> {selectedBuyback.farmOwner}</div>
                      <div><strong>Buyback Date:</strong> {new Date(selectedBuyback.buybackDate).toLocaleDateString()}</div>
                      <div><strong>Available:</strong> {selectedBuyback.remainingQuantity} chickens</div>
                      <div><strong>Price per chicken:</strong> {selectedBuyback.pricePerChicken} AFN</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Shop Selection */}
              <div className="md:col-span-2">
                <label htmlFor="shopId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('select_shop') || 'Select Shop'}
                </label>
                <select
                  id="shopId"
                  name="shopId"
                  value={formData.shopId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                >
                  <option value="">{t('choose_shop') || 'Choose a shop...'}</option>
                  {shops.map((shop) => (
                    <option key={shop.SId} value={shop.SId}>
                      {shop.SName} - {shop.SOwner}
                    </option>
                  ))}
                </select>
                {selectedShop && (
                  <div className="mt-2 p-3 bg-green-50 rounded-md">
                    <div className="text-sm text-green-700">
                      <div><strong>Shop:</strong> {selectedShop.SName}</div>
                      <div><strong>Owner:</strong> {selectedShop.SOwner}</div>
                      <div><strong>Email:</strong> {selectedShop.SEmail}</div>
                      <div><strong>Phone:</strong> {selectedShop.SPhone}</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Distribution Date */}
              <div>
                <label htmlFor="distributionDate" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('distribution_date') || 'Distribution Date'}
                </label>
                <input
                  type="date"
                  id="distributionDate"
                  name="distributionDate"
                  value={formData.distributionDate}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                />
              </div>

              {/* Quantity */}
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('quantity') || 'Quantity (Number of Chickens)'}
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  required
                  min="1"
                  max={selectedBuyback?.remainingQuantity || ''}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_quantity') || 'Enter number of chickens'}
                />
                {selectedBuyback && (
                  <small className="text-gray-500">
                    Maximum available: {selectedBuyback.remainingQuantity} chickens
                  </small>
                )}
              </div>

              {/* Price Per Chicken */}
              <div>
                <label htmlFor="pricePerChicken" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('price_per_chicken') || 'Price Per Chicken (AFN)'}
                </label>
                <input
                  type="number"
                  id="pricePerChicken"
                  name="pricePerChicken"
                  value={formData.pricePerChicken}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_price_per_chicken') || 'Enter price per chicken'}
                />
              </div>

              {/* Total Price (Auto-calculated) */}
              <div>
                <label htmlFor="totalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('total_price') || 'Total Price (AFN)'}
                </label>
                <input
                  type="number"
                  id="totalPrice"
                  name="totalPrice"
                  value={formData.totalPrice}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-600 dark:text-white dark:border-gray-600"
                  placeholder={t('auto_calculated') || 'Auto-calculated'}
                />
                <small className="text-gray-500">
                  {t('total_price_auto_calculated') || 'This field is automatically calculated'}
                </small>
              </div>
            </div>

            {/* Distribution Summary */}
            {selectedBuyback && selectedShop && formData.quantity && formData.pricePerChicken && (
              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <h4 className="font-medium text-purple-800 mb-2">
                  {t('distribution_summary') || 'Distribution Summary'}
                </h4>
                <div className="text-sm text-purple-700 grid grid-cols-2 gap-4">
                  <div><strong>From Farm:</strong> {selectedBuyback.farmName}</div>
                  <div><strong>To Shop:</strong> {selectedShop.SName}</div>
                  <div><strong>Quantity:</strong> {formData.quantity} chickens</div>
                  <div><strong>Total Value:</strong> {formData.totalPrice} AFN</div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/admin/chickens/distributions')}
              >
                {t('cancel') || 'Cancel'}
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Save className="h-4 w-4" />
                {loading ? (t('distributing') || 'Distributing...') : (t('distribute_to_shop') || 'Distribute to Shop')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddDistributionPage;
