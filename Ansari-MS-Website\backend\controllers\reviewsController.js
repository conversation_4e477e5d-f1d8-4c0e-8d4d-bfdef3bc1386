import <PERSON><PERSON> from "joi";
import async<PERSON>and<PERSON> from "../middlewares/asyncHandler.js";
import ReviewsModel from "../models/reviewsModel.js";

const reviewSchema = Joi.object({
  R_Message: Joi.string().min(5).required(),
  R_Number: Joi.number().min(1).max(5).required(),
});

const ReviewsController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = reviewSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    // Add user ID from authenticated user
    const userId = req.user.u_Id;

    // Check if the user has already reviewed
    const existingReview = await ReviewsModel.getByUserId(userId);
    if (existingReview) {
      return res.status(400).json({
        success: false,
        error: "You have already submitted a review.",
      });
    }

    const review = await ReviewsModel.create({ ...value, u_Id: userId });

    res.status(201).json({
      success: true,
      message: "Review added successfully",
      review,
    });
  }),

  getAll: asyncHandler(async (req, res) => {
    const reviews = await ReviewsModel.getAll();

    const formatted = reviews.map((review) => ({
      ...review,
      fullName: `${review.U_FirstName} ${review.U_LastName}`,
    }));

    res.json({
      success: true,
      message: "Reviews fetched successfully",
      totalReviews: formatted.length,
      data: formatted,
    });
  }),

  getById: asyncHandler(async (req, res) => {
    const review = await ReviewsModel.getById(req.params.id);
    if (!review) {
      return res.status(404).json({
        success: false,
        error: "Review not found",
      });
    }

    res.json({
      success: true,
      data: {
        ...review,
        fullName: `${review.U_FirstName} ${review.U_LastName}`,
      },
    });
  }),

  update: asyncHandler(async (req, res) => {
    const review = await ReviewsModel.getById(req.params.id);
    if (!review) {
      return res.status(404).json({
        success: false,
        error: "Review not found",
      });
    }

    // Check if the logged-in user owns this review
    if (review.u_Id !== req.user.u_Id) {
      return res.status(403).json({
        success: false,
        error: "You are not authorized to update this review",
      });
    }

    // Build full data with old values
    const fullData = {
      R_Message: req.body.R_Message ?? review.R_Message,
      R_Number: req.body.R_Number ?? review.R_Number,
    };

    const { error } = reviewSchema.validate(fullData);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    const updated = await ReviewsModel.update(req.params.id, fullData);
    if (!updated) {
      return res.status(400).json({
        success: false,
        error: "Failed to update review",
      });
    }

    res.json({
      success: true,
      message: "Review updated successfully",
      review: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await ReviewsModel.delete(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: "Review not found",
      });
    }
    res.json({
      success: true,
      message: "Review deleted successfully",
    });
  }),
};

export default ReviewsController;
