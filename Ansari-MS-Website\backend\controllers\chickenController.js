import Joi from 'joi';
import chickenModel from '../models/chickenModel.js';
import asyncHandler from 'express-async-handler';

// Validation schemas
const purchaseSchema = Joi.object({
  purchaseDate: Joi.date().required(),
  quantity: Joi.number().integer().min(1).required(),
  pricePerChicken: Joi.number().min(0).required(),
  totalPrice: Joi.number().min(0).required(),
  supplierName: Joi.string().min(2).max(255).required(),
  supplierContact: Joi.string().max(100).optional(),
  notes: Joi.string().max(1000).optional(),
});

const allocationSchema = Joi.object({
  purchaseId: Joi.number().integer().required(),
  farmId: Joi.number().integer().required(),
  allocationDate: Joi.date().required(),
  quantity: Joi.number().integer().min(1).required(),
  pricePerChicken: Joi.number().min(0).required(),
  totalPrice: Joi.number().min(0).required(),
});

const buybackSchema = Joi.object({
  allocationId: Joi.number().integer().required(),
  farmId: Joi.number().integer().required(),
  buybackDate: Joi.date().required(),
  quantity: Joi.number().integer().min(1).required(),
  pricePerChicken: Joi.number().min(0).required(),
  totalPrice: Joi.number().min(0).required(),
  daysCompleted: Joi.number().integer().min(0).required(),
});

const distributionSchema = Joi.object({
  buybackId: Joi.number().integer().required(),
  shopId: Joi.number().integer().required(),
  distributionDate: Joi.date().required(),
  quantity: Joi.number().integer().min(1).required(),
  pricePerChicken: Joi.number().min(0).required(),
  totalPrice: Joi.number().min(0).required(),
});

const chickenController = {
  // Purchase Management
  createPurchase: asyncHandler(async (req, res) => {
    const { error } = purchaseSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    const purchase = await chickenModel.createPurchase(req.body);
    res.status(201).json({
      success: true,
      message: 'Chicken purchase created successfully',
      data: purchase,
    });
  }),

  getAllPurchases: asyncHandler(async (req, res) => {
    const purchases = await chickenModel.getAllPurchases();
    res.status(200).json({
      success: true,
      message: 'Purchases retrieved successfully',
      data: purchases,
    });
  }),

  getPurchaseById: asyncHandler(async (req, res) => {
    const purchase = await chickenModel.getPurchaseById(req.params.id);
    if (!purchase) {
      return res.status(404).json({
        success: false,
        error: 'Purchase not found',
      });
    }
    res.status(200).json({
      success: true,
      data: purchase,
    });
  }),

  deletePurchase: asyncHandler(async (req, res) => {
    const result = await chickenModel.deletePurchase(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: 'Purchase not found',
      });
    }
    res.status(200).json({
      success: true,
      message: 'Purchase deleted successfully',
    });
  }),

  // Farm Allocation Management
  allocateToFarm: asyncHandler(async (req, res) => {
    const { error } = allocationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    const allocation = await chickenModel.allocateToFarm(req.body);
    res.status(201).json({
      success: true,
      message: 'Chickens allocated to farm successfully',
      data: allocation,
    });
  }),

  getAllAllocations: asyncHandler(async (req, res) => {
    const allocations = await chickenModel.getAllAllocations();
    res.status(200).json({
      success: true,
      message: 'Farm allocations retrieved successfully',
      data: allocations,
    });
  }),

  getAllocationById: asyncHandler(async (req, res) => {
    const allocation = await chickenModel.getAllocationById(req.params.id);
    if (!allocation) {
      return res.status(404).json({
        success: false,
        error: 'Allocation not found',
      });
    }
    res.status(200).json({
      success: true,
      data: allocation,
    });
  }),

  deleteAllocation: asyncHandler(async (req, res) => {
    const result = await chickenModel.deleteAllocation(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: 'Allocation not found',
      });
    }
    res.status(200).json({
      success: true,
      message: 'Allocation deleted successfully',
    });
  }),

  // Buyback Management
  buyBackFromFarm: asyncHandler(async (req, res) => {
    const { error } = buybackSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    const buyback = await chickenModel.buyBackFromFarm(req.body);
    res.status(201).json({
      success: true,
      message: 'Chickens bought back from farm successfully',
      data: buyback,
    });
  }),

  getAllBuybacks: asyncHandler(async (req, res) => {
    const buybacks = await chickenModel.getAllBuybacks();
    res.status(200).json({
      success: true,
      message: 'Buybacks retrieved successfully',
      data: buybacks,
    });
  }),

  getBuybackById: asyncHandler(async (req, res) => {
    const buyback = await chickenModel.getBuybackById(req.params.id);
    if (!buyback) {
      return res.status(404).json({
        success: false,
        error: 'Buyback not found',
      });
    }
    res.status(200).json({
      success: true,
      data: buyback,
    });
  }),

  deleteBuyback: asyncHandler(async (req, res) => {
    const result = await chickenModel.deleteBuyback(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: 'Buyback not found',
      });
    }
    res.status(200).json({
      success: true,
      message: 'Buyback deleted successfully',
    });
  }),

  // Shop Distribution Management
  distributeToShop: asyncHandler(async (req, res) => {
    const { error } = distributionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: error.details[0].message,
      });
    }

    const distribution = await chickenModel.distributeToShop(req.body);
    res.status(201).json({
      success: true,
      message: 'Chickens distributed to shop successfully',
      data: distribution,
    });
  }),

  getAllDistributions: asyncHandler(async (req, res) => {
    const distributions = await chickenModel.getAllDistributions();
    res.status(200).json({
      success: true,
      message: 'Shop distributions retrieved successfully',
      data: distributions,
    });
  }),

  deleteDistribution: asyncHandler(async (req, res) => {
    const result = await chickenModel.deleteDistribution(req.params.id);
    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        error: 'Distribution not found',
      });
    }
    res.status(200).json({
      success: true,
      message: 'Distribution deleted successfully',
    });
  }),

  // Statistics
  getStatistics: asyncHandler(async (req, res) => {
    const stats = await chickenModel.getStatistics();
    res.status(200).json({
      success: true,
      message: 'Statistics retrieved successfully',
      data: stats,
    });
  }),

  // Process Report endpoint
  getProcessReport: asyncHandler(async (req, res) => {
    try {
      console.log('🔍 Fetching complete process report...');

      const processData = await chickenModel.getCompleteProcessReport();
      console.log('✅ Process report fetched successfully');

      res.status(200).json({
        success: true,
        message: 'Process report retrieved successfully',
        data: processData,
      });
    } catch (error) {
      console.error('❌ Error fetching process report:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch process report: ' + error.message,
      });
    }
  }),
};

export default chickenController;
