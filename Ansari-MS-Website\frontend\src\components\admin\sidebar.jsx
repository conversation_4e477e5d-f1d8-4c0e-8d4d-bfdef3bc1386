"use client"

import {
  LayoutDashboard,
  Newspaper,
  Settings,
  Users,
  LogOut,
  ShoppingBag,
  Bell,
  Home,
  ShoppingCart,
  Pill,
  Inbox,
  BarChart,
  MessageSquare,
  Bird,
} from "lucide-react"
import { Link, useNavigate, useLocation } from "react-router-dom"
import { useAuth } from "../../contexts/AuthContext"
import { useLanguage } from "../../contexts/LanguageContext"
import { useNotifications } from "../../contexts/NotificationContext"
import LazyImage from "../../components/LazyImage"

function SideBar({ isOpen, onToggle, darkMode }) {
  const navigate = useNavigate()
  const { logout } = useAuth()
  const location = useLocation()
  const { language, translations } = useLanguage()
  const { unreadCount } = useNotifications()

  const handleLogout = () => {
    logout()
    navigate("/signin")
  }

  // Translation helper function
  const t = (key) => {
    if (!translations || !translations[language]) return key
    return translations[language][key] || key
  }

  const menuItems = [
    {
      key: "/admin",
      icon: <LayoutDashboard size={18} />,
      label: t("dashboard"),
    },
    {
      key: "/admin/users",
      icon: <Users size={18} />,
      label: t("users"),
    },
    {
      key: "/admin/news",
      icon: <Newspaper size={18} />,
      label: t("news"),
    },
    {
      key: "/admin/services",
      icon: <ShoppingBag size={18} />,
      label: t("services"),
    },
    {
      key: "/admin/farms",
      icon: <Home size={18} />,
      label: t("farms"),
    },
    {
      key: "/admin/shops",
      icon: <ShoppingBag size={18} />,
      label: t("shops"),
    },
    {
      key: "/admin/chickens",
      icon: <Bird size={18} />,
      label: t("chickens"),
    },
    {
      key: "/admin/feed",
      icon: <ShoppingCart size={18} />,
      label: t("feed"),
    },
    {
      key: "/admin/medicine",
      icon: <Pill size={18} />,
      label: t("medicine"),
    },
    {
      key: "/admin/inventory",
      icon: <Inbox size={18} />,
      label: t("inventory"),
    },
    {
      key: "/admin/chat",
      icon: <MessageSquare size={18} />,
      label: "Chat",
    },
    {
      key: "/admin/notifications",
      icon: <Bell size={18} />,
      label: t("notifications"),
      badge: unreadCount,
    },
    {
      key: "/admin/reports",
      icon: <BarChart size={18} />,
      label: t("reports"),
    },
    {
      key: "/admin/settings",
      icon: <Settings size={18} />,
      label: t("settings"),
    },
  ]

  const handleMenuClick = (key) => {
    navigate(key)
    // Close sidebar on mobile after navigation
    if (window.innerWidth < 768) {
      onToggle(false)
    }
  }

  // Group menu items by category
  const mainMenuItems = menuItems.slice(0, 4) // Dashboard, Users, News, Services
  const farmMenuItems = menuItems.slice(4, 10) // Farms, Shops, Chickens, Feed, Medicine, Inventory
  const systemMenuItems = menuItems.slice(10, 14) // Chat, Notifications, Reports, Settings

  return (
    <div
      className={`h-screen ${isOpen ? "w-64" : "w-20"} transition-all duration-300 ease-in-out flex flex-col ${
        darkMode ? "bg-secondary text-white" : "bg-white text-textprimary"
      } ${language === "ps" ? "border-l" : "border-r"} border-border-color/10 dark:border-border-dark/10`}
    >
      {/* Sidebar Header with Logo */}
      <div
        className={`h-16 flex items-center px-5 ${darkMode ? "border-b border-white/10" : "border-b border-gray-100"}`}
      >
        <Link to="/" className="flex items-center gap-3">
          
         <div className="flex items-center justify-center gap-16 w-10 h-10 rounded-lg  text-white">
             <LazyImage src="./imgs/logo.png" alt="Logo" className="w-full h-full rounded-full" />
          </div>
          {isOpen && (
            <span
              className={`ml-3 font-semibold text-lg transition-opacity duration-200 ${
                language === "ps" ? "font-headingPs" : "font-headingEn"
              }`}
            >
              {language==="ps" ? "انصاري شرکت" : "Ansari company"}
            </span>
          )}
        </Link>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700">
        {/* Main Navigation */}
        <div className="px-3 mb-6">
          {isOpen && (
            <h3
              className={`text-xs uppercase font-semibold px-3 mb-2 text-gray-500 dark:text-gray-400 ${
                language === "ps" ? "text-right" : "text-left"
              }`}
            >
              {language === "ps" ? "اصلي" : "Main"}
            </h3>
          )}
          <ul className="space-y-1">
            {mainMenuItems.map((item) => (
              <li key={item.key}>
                <button
                  onClick={() => handleMenuClick(item.key)}
                  className={`flex items-center w-full rounded-lg px-3 py-2.5 transition-all duration-200 ${
                    location.pathname === item.key
                      ? "bg-primary/10 text-primary font-medium"
                      : `${
                          darkMode
                            ? "text-gray-300 hover:bg-white/10 hover:text-white"
                            : "text-gray-700 hover:bg-gray-100"
                        }`
                  } ${!isOpen ? "justify-center" : ""}`}
                  dir={language === "ps" ? "rtl" : "ltr"}
                >
                  <span
                    className={`${
                      location.pathname === item.key ? "text-primary" : darkMode ? "text-gray-400" : "text-gray-500"
                    } ${!isOpen ? "mx-0" : ""}`}
                  >
                    {item.icon}
                  </span>
                  {isOpen && (
                    <span
                      className={`truncate transition-opacity duration-200 ${
                        language === "ps" ? "font-bodyPs mr-3" : "font-bodyEn ml-3"
                      }`}
                    >
                      {item.label}
                    </span>
                  )}
                  {isOpen && item.badge && (
                    <span
                      className={`${language === "ps" ? "mr-auto" : "ml-auto"} bg-primary text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center`}
                    >
                      {item.badge}
                    </span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Farm & Chicken Management */}
        <div className="px-3 mb-6">
          {isOpen && (
            <h3
              className={`text-xs uppercase font-semibold px-3 mb-2 text-gray-500 dark:text-gray-400 ${
                language === "ps" ? "text-right" : "text-left"
              }`}
            >
              {language === "ps" ? "چرګان" : "Chickens"}
            </h3>
          )}
          <ul className="space-y-1">
            {farmMenuItems.map((item) => (
              <li key={item.key}>
                <button
                  onClick={() => handleMenuClick(item.key)}
                  className={`flex items-center w-full rounded-lg px-3 py-2.5 transition-all duration-200 ${
                    location.pathname === item.key
                      ? "bg-primary/10 text-primary font-medium"
                      : `${
                          darkMode
                            ? "text-gray-300 hover:bg-white/10 hover:text-white"
                            : "text-gray-700 hover:bg-gray-100"
                        }`
                  } ${!isOpen ? "justify-center" : ""}`}
                  dir={language === "ps" ? "rtl" : "ltr"}
                >
                  <span
                    className={`${
                      location.pathname === item.key ? "text-primary" : darkMode ? "text-gray-400" : "text-gray-500"
                    } ${!isOpen ? "mx-0" : ""}`}
                  >
                    {item.icon}
                  </span>
                  {isOpen && (
                    <span
                      className={`truncate transition-opacity duration-200 ${
                        language === "ps" ? "font-bodyPs mr-3" : "font-bodyEn ml-3"
                      }`}
                    >
                      {item.label}
                    </span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* System */}
        <div className="px-3">
          {isOpen && (
            <h3
              className={`text-xs uppercase font-semibold px-3 mb-2 text-gray-500 dark:text-gray-400 ${
                language === "ps" ? "text-right" : "text-left"
              }`}
            >
              {language === "ps" ? "سیستم" : "System"}
            </h3>
          )}
          <ul className="space-y-1">
            {systemMenuItems.map((item) => (
              <li key={item.key}>
                <button
                  onClick={() => handleMenuClick(item.key)}
                  className={`flex items-center w-full rounded-lg px-3 py-2.5 transition-all duration-200 ${
                    location.pathname === item.key
                      ? "bg-primary/10 text-primary font-medium"
                      : `${
                          darkMode
                            ? "text-gray-300 hover:bg-white/10 hover:text-white"
                            : "text-gray-700 hover:bg-gray-100"
                        }`
                  } ${!isOpen ? "justify-center" : ""}`}
                  dir={language === "ps" ? "rtl" : "ltr"}
                >
                  <span
                    className={`${
                      location.pathname === item.key ? "text-primary" : darkMode ? "text-gray-400" : "text-gray-500"
                    } ${!isOpen ? "mx-0" : ""}`}
                  >
                    {item.icon}
                  </span>
                  {isOpen && (
                    <span
                      className={`truncate transition-opacity duration-200 ${
                        language === "ps" ? "font-bodyPs mr-3" : "font-bodyEn ml-3"
                      }`}
                    >
                      {item.label}
                    </span>
                  )}
                  {isOpen && item.badge && (
                    <span
                      className={`${language === "ps" ? "mr-auto" : "ml-auto"} bg-primary text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center`}
                    >
                      {item.badge}
                    </span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Sidebar Footer */}
      <div
        className={`p-4 ${
          darkMode ? "border-t border-white/10" : "border-t border-gray-100"
        } flex items-center justify-center`}
      >
        <button
          onClick={handleLogout}
          className={`flex items-center w-full rounded-lg px-3 py-2.5 transition-all duration-200 ${
            darkMode ? "bg-white/5 text-white hover:bg-white/10" : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          } ${!isOpen ? "justify-center" : ""}`}
          dir={language === "ps" ? "rtl" : "ltr"}
        >
          <LogOut size={18} className={!isOpen ? "mx-0" : ""} />
          {isOpen && (
            <span className={`${language === "ps" ? "font-bodyPs mr-3" : "font-bodyEn ml-3"}`}>
              {language === "ps" ? "وتل" : "Logout"}
            </span>
          )}
        </button>
      </div>
    </div>
  )
}

export default SideBar




//////////////////////////////////////////////

/////////////////////////////////
// /* eslint-disable no-unused-vars */
// /* eslint-disable react/prop-types */
// import {
//   LayoutDashboard,
//   Newspaper,
//   Settings,
//   Users,
//   Package,
//   LogOut,
//   X,
//   ChevronDown,
//   Tractor,
//   ShoppingBag,
//   FileText,
//   Bell,
// } from 'lucide-react';

// import { useState, useEffect } from 'react';
// import { Link, useNavigate, useLocation } from 'react-router-dom';
// import { useAuth } from '../../contexts/AuthContext';
// import { useLanguage } from '../../contexts/LanguageContext';
// import { useNotifications } from '../../contexts/NotificationContext';
// import {
//   DashboardOutlined,
//   UserOutlined,
//   FileTextOutlined,
//   ShoppingOutlined,
//   HomeOutlined,
//   ShoppingCartOutlined,
//   MedicineBoxOutlined,
//   InboxOutlined,
//   BarChartOutlined,
//   SettingOutlined,
//   MenuFoldOutlined,
//   MenuUnfoldOutlined,
//   BellOutlined,
// } from '@ant-design/icons';
// import { Layout, Menu, Button, Badge, Drawer } from 'antd';
// import { FaSnapchat } from 'react-icons/fa';

// const { Sider } = Layout;

// function SideBar({ isOpen, onToggle }) {
//   const [mobileOpen, setMobileOpen] = useState(false);
//   const [isMobile, setIsMobile] = useState(false);
//   const navigate = useNavigate();
//   const { logout } = useAuth();
//   const location = useLocation();
//   const { language, translations } = useLanguage();
//   const { unreadCount } = useNotifications();

//   useEffect(() => {
//     const checkIfMobile = () => {
//       const mobile = window.innerWidth < 768;
//       setIsMobile(mobile);
//       if (mobile && !mobileOpen) {
//         onToggle(true);
//       }
//     };

//     checkIfMobile();
//     window.addEventListener('resize', checkIfMobile);
//     return () => window.removeEventListener('resize', checkIfMobile);
//   }, [onToggle, mobileOpen]);

//   const handleLogout = () => {
//     logout();
//     navigate('/signin');
//   };

//   const t = (key) => {
//     if (!translations || !translations[language]) return key;
//     return translations[language][key] || key;
//   };

//   const menuItems = [
//     {
//       key: '/admin',
//       icon: <DashboardOutlined />,
//       label: t('dashboard'),
//     },
//     {
//       key: '/admin/users',
//       icon: <UserOutlined />,
//       label: t('users'),
//     },
//     {
//       key: '/admin/news',
//       icon: <FileTextOutlined />,
//       label: t('news'),
//     },
//     {
//       key: '/admin/services',
//       icon: <ShoppingOutlined />,
//       label: t('services'),
//     },
//     {
//       key: '/admin/farms',
//       icon: <HomeOutlined />,
//       label: t('farms'),
//     },
//     {
//       key: '/admin/feed',
//       icon: <ShoppingCartOutlined />,
//       label: t('feed'),
//     },
//     {
//       key: '/admin/medicine',
//       icon: <MedicineBoxOutlined />,
//       label: t('medicine'),
//     },
//     {
//       key: '/admin/inventory',
//       icon: <InboxOutlined />,
//       label: t('inventory'),
//     },
//     {
//       key: '/admin/chat',
//       icon: <FaSnapchat />,
//       label: 'Chat',
//     },
//     {
//       key: '/admin/notifications',
//       icon: (
//         <Badge
//           count={unreadCount}
//           offset={[5, 0]}
//           style={{
//             backgroundColor: '#f5222d',
//             boxShadow: '0 0 0 1px #fff',
//           }}
//         >
//           <BellOutlined style={{ fontSize: '16px' }} />
//         </Badge>
//       ),
//       label: t('notifications'),
//     },
//     {
//       key: '/admin/reports',
//       icon: <BarChartOutlined />,
//       label: t('reports'),
//     },
//     {
//       key: '/admin/settings',
//       icon: <SettingOutlined />,
//       label: t('settings'),
//     },
//     {
//       key: 'logout',
//       icon: <LogOut />,
//       label: t('logout'),
//       onClick: handleLogout,
//     },
//   ];

//   const handleMenuClick = ({ key }) => {
//     if (key === 'logout') {
//       handleLogout();
//       return;
//     }
//     navigate(key);
//     if (isMobile) {
//       setMobileOpen(false);
//     }
//   };

//   const toggleMobile = () => {
//     setMobileOpen(!mobileOpen);
//   };

//   const siderContent = (
//     <>
//       <div
//         className="logo"
//         style={{
//           height: '64px',
//           display: 'flex',
//           alignItems: 'center',
//           justifyContent: 'center',
//           background: '#001529',
//           borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
//         }}
//       >
//         {/* <img src="../.. " alt="Logo" style={{ height: '40px' }} /> */}
//         <Link to="/">
//           <img src="../../../public/imgs/Ansari.png" alt="Logo" className="w-10 h-10 rounded-full" />
//         </Link>
//       </div>
//       <Menu
//         theme="dark"
//         mode="inline"
//         selectedKeys={[location.pathname]}
//         items={menuItems}
//         onClick={handleMenuClick}
//         style={{
//           borderRight: 0,
//         }}
//       />
//     </>
//   );

//   if (isMobile) {
//     return (
//       <>
//         <Button
//           type="text"
//           icon={mobileOpen ? <X /> : <Menu />}
//           onClick={toggleMobile}
//           style={{
//             fontSize: '16px',
//             width: 64,
//             height: 64,
//             position: 'fixed',
//             top: 0,
//             left: 0,
//             zIndex: 1000,
//             color: '#fff',
//             background: 'transparent',
//           }}
//         />
//         <Drawer
//           placement="left"
//           closable={false}
//           onClose={() => setMobileOpen(false)}
//           open={mobileOpen}
//           width={250}
//           bodyStyle={{
//             padding: 0,
//             background: '#001529',
//           }}
//           style={{
//             background: '#001529',
//           }}
//         >
//           {siderContent}
//         </Drawer>
//       </>
//     );
//   }

//   return (
//     <Sider
//       collapsible
//       collapsed={!isOpen}
//       onCollapse={(collapsed) => onToggle(!collapsed)}
//       width={250}
//       style={{
//         overflow: 'auto',
//         height: '100vh',
//         // position: 'fixed',
//         // left: 0,
//         // top: 0,
//         // bottom: 0,
//         background: '#001529',
//         boxShadow: '2px 0 8px rgba(0,0,0,0.15)',
//       }}
//     >
//       {siderContent}
//     </Sider>
//   );
// }

// export default SideBar;
