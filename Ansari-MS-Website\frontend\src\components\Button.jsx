import PropTypes from 'prop-types';

function But<PERSON>({ children, onClick, className, variant = 'primary' }) {
  const baseStyles = 'text-sm py-2 px-4 rounded-lg transition duration-300 ease-in-out font-medium focus:outline-none';

  const variants = {
    primary: 'bg-primary text-white hover:bg-danger shadow-md  rounded-sm flex gap-1',
    secondary: 'bg-[#F5F5F5] text-gray-600 border border-[#CCCCCC] hover:bg-gray-200 flex rounded-sm ',
    destructive:
      'bg-[#D32F2F] text-white hover:bg-[#B71C1C] shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out',
    cancel:
      'bg-transparent ml-2 px-4 py-2  text-white rounded-md text-gray-600 border border-gray-300 hover:bg-gray-100 hover:text-gray-800 transition duration-300 ease-in-out',
    dotsbtn: 'bg-transparent text-gray-600  hover:bg-gray-100 hover:text-gray-800 transition duration-300 ease-in-out',
  };

  return (
    <button onClick={onClick} className={`${baseStyles} ${variants[variant]} ${className}`}>
      {children}
    </button>
  );
}

Button.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'destructive', 'cancel']),
};

export default Button;
