'use client';

import { useState, use<PERSON>emo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreVertical,
  Activity,
  AlertTriangle,
  XCircle,
  Trash,
  Pencil,
} from 'lucide-react';
import { useFeed } from '../../contexts/FeedContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>G<PERSON>, <PERSON> } from 'recharts';
import { format } from 'date-fns';
import Link from '../../components/feed-components/Link';

const FeedPage = () => {
  const navigate = useNavigate();
  const { feeds, deleteFeed } = useFeed();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredFeeds = useMemo(() => {
    return feeds.filter((feed) => {
      const matchesSearch =
        feed.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feed.batchNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feed.supplier.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || feed.status === filterStatus;
      return matchesSearch && matchesStatus;
    });
  }, [feeds, searchTerm, filterStatus]);

  const chartData = useMemo(() => {
    return filteredFeeds.map((feed) => ({
      name: feed.name,
      quantity: feed.quantity,
      value: feed.price,
    }));
  }, [filteredFeeds]);

  const handleDelete = (id) => {
    if (window.confirm(t('confirm_delete_feed'))) {
      deleteFeed(id);
    }
  };

  const handleEdit = (id) => {
    navigate(`/admin/feed/edit/${id}`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'in-stock':
        return 'bg-green-100 text-green-800';
      case 'low-stock':
        return 'bg-yellow-100 text-yellow-800';
      case 'out-of-stock':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('feed_management')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('feed_management_description')}</p>
          </div>
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button variant="secondary" size="sm">
              <Download className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('export')}
            </Button>
            <Button variant="secondary" size="sm">
              <Upload className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('import')}
            </Button>
            <Link to="/admin/feed/add" variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('add_new_feed')}
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className={`flex flex-col sm:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('search_feeds')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div
                className={`flex items-center justify-center w-full sm:w-auto px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Filter className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                {t('filter')}
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
              <DropdownMenuLabel>{t('feed_status')}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterStatus('all')}>{t('all')}</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('in-stock')}>{t('in_stock')}</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('low-stock')}>{t('low_stock')}</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('out-of-stock')}>{t('out_of_stock')}</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('total_feeds')}</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feeds.length}</div>
              <p className="text-xs text-muted-foreground">{t('in_stock')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('low_stock')}</CardTitle>
              <AlertTriangle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feeds.filter((f) => f.status === 'low-stock').length}</div>
              <p className="text-xs text-muted-foreground">{t('needs_replenishment')}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('out_of_stock')}</CardTitle>
              <XCircle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feeds.filter((f) => f.status === 'out-of-stock').length}</div>
              <p className="text-xs text-muted-foreground">{t('needs_replenishment')}</p>
            </CardContent>
          </Card>
        </div>

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('feed_distribution')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis yAxisId="left" orientation="left" stroke="#FF6B00" />
                  <YAxis yAxisId="right" orientation="right" stroke="#2C3E50" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="quantity" fill="#FF6B00" name={t('quantity')} />
                  <Bar yAxisId="right" dataKey="value" fill="#2C3E50" name={t('value')} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Feeds Table */}
        <Card>
          <CardHeader>
            <CardTitle>{t('feed_list')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('name')}</TableHead>
                  <TableHead>{t('type')}</TableHead>
                  <TableHead>{t('quantity')}</TableHead>
                  <TableHead>{t('price')}</TableHead>
                  <TableHead>{t('supplier')}</TableHead>
                  <TableHead>{t('expiry_date')}</TableHead>
                  <TableHead>{t('status')}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFeeds.length > 0 ? (
                  filteredFeeds.map((feed) => (
                    <TableRow key={feed.id}>
                      <TableCell className="font-medium">{feed.name}</TableCell>
                      <TableCell>{t(feed.type)}</TableCell>
                      <TableCell>{feed.quantity}</TableCell>
                      <TableCell>{feed.price}</TableCell>
                      <TableCell>{feed.supplier}</TableCell>
                      <TableCell>{format(new Date(feed.expiryDate), 'yyyy-MM-dd')}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(feed.status)}`}>
                          {t(feed.status)}
                        </span>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(feed.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('edit')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(feed.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      {t('no_feeds_found')}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FeedPage;
