'use client';

import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ShieldCheck, RefreshCcw } from 'lucide-react';
import axios from 'axios';

const VerifyCode = () => {
  const navigate = useNavigate();
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [resending, setResending] = useState(false);
  const [resendAvailable, setResendAvailable] = useState(false);
  const [timeLeft, setTimeLeft] = useState(180);
  const inputRefs = useRef([]);

  // Countdown Timer
  useEffect(() => {
    if (timeLeft <= 0) {
      setResendAvailable(true);
      return;
    }

    const interval = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
    return () => clearInterval(interval);
  }, [timeLeft]);

  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m}:${s < 10 ? '0' : ''}${s}`;
  };

  const handleChange = (value, index) => {
    if (/^\d?$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);
      if (value && index < 5) inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccessMessage('');

    const enteredCode = code.join(''); // join all digits
    const email = localStorage.getItem('userverifyemail'); // get email from localStorage

    if (!email) {
      setError('No email found. Please request a new verification email.');
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post('http://localhost:5432/api/v1/users/verify-email/confirm', {
        email,
        otp: enteredCode,
      });

      if (response.data.success) {
        setSuccessMessage(response.data.message || 'Email verified successfully.');
        setTimeout(() => {
          navigate('/signin');
        }, 2000); // optional small delay after success
      } else {
        setError(response.data.message || 'Invalid verification code.');
      }
    } catch (err) {
      console.error(err);
      setError(err.response?.data?.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    navigate('/verify-email');
    setCode(['', '', '', '', '', '']);
    inputRefs.current[0]?.focus();
    setResending(true);
    setError('');
    setSuccessMessage('');

    try {
      // Replace with actual API call
      await new Promise((res) => setTimeout(res, 1500));
      setSuccessMessage('Verification code resent successfully.');
      setTimeLeft(180);
      setResendAvailable(false);
    } catch {
      setError('Failed to resend code. Try again later.');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-16">
      <motion.div
        initial={{ opacity: 0, y: 12 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white max-w-md w-full p-8 rounded-2xl shadow-xl text-center"
      >
        <ShieldCheck className="mx-auto h-16 w-16 text-primary mb-4" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Verify Your Code</h1>
        <p className="text-gray-600 mb-6">Enter the 6-digit code we sent to your email.</p>

        {successMessage && <p className="text-sm text-green-600 font-medium mb-2">{successMessage}</p>}
        {error && <p className="text-sm text-red-500 font-medium mb-2">{error}</p>}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex justify-center gap-3">
            {code.map((digit, index) => (
              <input
                key={index}
                ref={(el) => (inputRefs.current[index] = el)}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(e.target.value, index)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                className="w-12 h-14 text-xl font-semibold text-center border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary shadow-sm transition"
              />
            ))}
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full py-3 px-4 text-white bg-primary hover:bg-primary-hover rounded-xl font-semibold transition duration-200 ${
              loading ? 'opacity-60 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Verifying...' : 'Verify Code'}
          </button>
        </form>

        <div className="mt-6 text-sm text-gray-600">
          {resendAvailable ? (
            <button
              onClick={handleResend}
              disabled={resending}
              className="flex items-center justify-center gap-2 text-primary font-medium hover:underline disabled:opacity-60 disabled:cursor-not-allowed"
            >
              {resending ? (
                <svg className="animate-spin w-4 h-4" viewBox="0 0 24 24" fill="none">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
              ) : (
                <RefreshCcw className="w-4 h-4" />
              )}
              {resending ? 'Resending...' : 'Resend Code'}
            </button>
          ) : (
            <p>
              Resend available in <span className="font-semibold text-gray-800">{formatTime(timeLeft)}</span>
            </p>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default VerifyCode;

///////////////////////////////////////////////////////////////////
// "use client"

// import { useState, useRef, useEffect } from "react"
// import { useNavigate } from "react-router-dom"
// import { motion } from "framer-motion"
// import { ShieldCheck, RefreshCcw } from "lucide-react"

// const VerifyCode = () => {
//   const navigate = useNavigate()
//   const [code, setCode] = useState(["", "", "", "", "", ""])
//   const [error, setError] = useState("")
//   const [loading, setLoading] = useState(false)
//   const [resendAvailable, setResendAvailable] = useState(false)
//   const [timeLeft, setTimeLeft] = useState(180)
//   const inputRefs = useRef([])

//   // ⏳ Countdown timer effect
//   useEffect(() => {
//     if (timeLeft <= 0) {
//       setResendAvailable(true)
//       return
//     }

//     const interval = setInterval(() => {
//       setTimeLeft((prev) => prev - 1)
//     }, 1000)

//     return () => clearInterval(interval)
//   }, [timeLeft])

//   const formatTime = (seconds) => {
//     const mins = Math.floor(seconds / 60)
//     const secs = seconds % 60
//     return `${mins}:${secs < 10 ? "0" : ""}${secs}`
//   }

//   const handleChange = (value, index) => {
//     if (/^\d?$/.test(value)) {
//       const newCode = [...code]
//       newCode[index] = value
//       setCode(newCode)

//       if (value && index < 5) {
//         inputRefs.current[index + 1]?.focus()
//       }
//     }
//   }

//   const handleKeyDown = (e, index) => {
//     if (e.key === "Backspace" && !code[index] && index > 0) {
//       inputRefs.current[index - 1]?.focus()
//     }
//   }

//   const handleSubmit = async (e) => {
//     e.preventDefault()
//     setLoading(true)
//     setError("")

//     const enteredCode = code.join("")

//     try {
//       await new Promise((res) => setTimeout(res, 1000)) // mock delay

//       if (enteredCode === "123456") {
//         navigate("/signin")
//       } else {
//         setError("Invalid verification code.")
//       }
//     } catch {
//       setError("Something went wrong. Try again.")
//     } finally {
//       setLoading(false)
//     }
//   }

//   const handleResend = () => {
//     setCode(["", "", "", "", "", ""])
//     inputRefs.current[0]?.focus()
//     setTimeLeft(180)
//     setResendAvailable(false)
//     setError("")

//     // Replace with actual API call
//     console.log("Resending verification code...")
//   }

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-16">
//       <motion.div
//         initial={{ opacity: 0, y: 12 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.4 }}
//         className="bg-white max-w-md w-full p-8 rounded-2xl shadow-xl text-center"
//       >
//         <ShieldCheck className="mx-auto h-16 w-16 text-primary mb-4" />
//         <h1 className="text-2xl font-bold text-gray-800 mb-2">Verify Your Code</h1>
//         <p className="text-gray-600 mb-6">Enter the 6-digit code we sent to your email.</p>

//         <form onSubmit={handleSubmit} className="space-y-6">
//           <div className="flex justify-center gap-3">
//             {code.map((digit, index) => (
//               <input
//                 key={index}
//                 ref={(el) => (inputRefs.current[index] = el)}
//                 type="text"
//                 inputMode="numeric"
//                 maxLength={1}
//                 value={digit}
//                 onChange={(e) => handleChange(e.target.value, index)}
//                 onKeyDown={(e) => handleKeyDown(e, index)}
//                 className="w-12 h-14 text-xl font-semibold text-center border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary shadow-sm transition"
//               />
//             ))}
//           </div>

//           {error && <p className="text-sm text-red-500 font-medium -mt-2">{error}</p>}

//           <button
//             type="submit"
//             disabled={loading}
//             className={`w-full py-3 px-4 text-white bg-primary hover:bg-primary-hover rounded-xl font-semibold transition duration-200 ${
//               loading ? "opacity-60 cursor-not-allowed" : ""
//             }`}
//           >
//             {loading ? "Verifying..." : "Verify Code"}
//           </button>
//         </form>

//         {/* Timer or resend */}
//         <div className="mt-6 text-sm text-gray-600">
//           {resendAvailable ? (
//             <button
//               onClick={handleResend}
//               className="flex items-center gap-2 text-primary font-medium hover:underline"
//             >
//               <RefreshCcw className="w-4 h-4" />
//               Resend Code
//             </button>
//           ) : (
//             <p>Resend available in <span className="font-semibold text-gray-800">{formatTime(timeLeft)}</span></p>
//           )}
//         </div>
//       </motion.div>
//     </div>
//   )
// }

// export default VerifyCode

/////////////////////////////////////////
// "use client"

// import { useState, useRef } from "react"
// import { useNavigate } from "react-router-dom"
// import { motion } from "framer-motion"
// import { ShieldCheck } from "lucide-react"

// const VerifyCode = () => {
//   const navigate = useNavigate()
//   const [code, setCode] = useState(["", "", "", "", "", ""])
//   const [error, setError] = useState("")
//   const [loading, setLoading] = useState(false)
//   const inputRefs = useRef([])

//   const handleChange = (value, index) => {
//     if (/^\d?$/.test(value)) {
//       const newCode = [...code]
//       newCode[index] = value
//       setCode(newCode)

//       if (value && index < 5) {
//         inputRefs.current[index + 1]?.focus()
//       }
//     }
//   }

//   const handleKeyDown = (e, index) => {
//     if (e.key === "Backspace" && !code[index] && index > 0) {
//       inputRefs.current[index - 1]?.focus()
//     }
//   }

//   const handleSubmit = async (e) => {
//     e.preventDefault()
//     setLoading(true)
//     setError("")

//     const enteredCode = code.join("")

//     try {
//       await new Promise((res) => setTimeout(res, 1000)) // mock API delay

//       if (enteredCode === "123456") { // mock valid code
//         navigate("/signin")
//       } else {
//         setError("Invalid verification code.")
//       }
//     } catch (err) {
//       setError("Something went wrong. Try again.")
//     } finally {
//       setLoading(false)
//     }
//   }

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-16">
//       <motion.div
//         initial={{ opacity: 0, y: 12 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.4 }}
//         className="bg-white max-w-md w-full p-8 rounded-2xl shadow-xl text-center"
//       >
//         <ShieldCheck className="mx-auto h-16 w-16 text-primary mb-4" />
//         <h1 className="text-2xl font-bold text-gray-800 mb-2">Verify Your Code</h1>
//         <p className="text-gray-600 mb-6">Enter the 6-digit code we sent to your email.</p>

//         <form onSubmit={handleSubmit} className="space-y-6">
//           <div className="flex justify-center gap-3">
//             {code.map((digit, index) => (
//               <input
//                 key={index}
//                 ref={(el) => (inputRefs.current[index] = el)}
//                 type="text"
//                 inputMode="numeric"
//                 maxLength={1}
//                 value={digit}
//                 onChange={(e) => handleChange(e.target.value, index)}
//                 onKeyDown={(e) => handleKeyDown(e, index)}
//                 className="w-12 h-14 text-xl font-semibold text-center border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary shadow-sm transition"
//               />
//             ))}
//           </div>

//           {error && <p className="text-sm text-red-500 font-medium -mt-2">{error}</p>}

//           <button
//             type="submit"
//             disabled={loading}
//             className={`w-full py-3 px-4 text-white bg-primary hover:bg-primary-hover rounded-xl font-semibold transition duration-200 ${
//               loading ? "opacity-60 cursor-not-allowed" : ""
//             }`}
//           >
//             {loading ? "Verifying..." : "Verify Code"}
//           </button>
//         </form>
//       </motion.div>
//     </div>
//   )
// }

// export default VerifyCode
