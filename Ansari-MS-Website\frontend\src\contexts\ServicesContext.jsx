"use client"

import { createContext, useContext, useState, useEffect } from "react"
import axios from "axios"
import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from "../utils/localStorage"

// Create an axios instance with default config
const api = axios.create({
  baseURL: "http://localhost:5432/api/v1", // Updated to match your actual API URL
  timeout: 30000, // 30 seconds timeout
})

// Request interceptor to add auth token to all requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

// Helper function to log FormData contents
const logFormData = (formData) => {
  console.log("FormData contents:")
  for (const [key, value] of formData.entries()) {
    console.log(`${key}: ${value}`)
  }
}

export const ServicesContext = createContext()

export const useServices = () => {
  const context = useContext(ServicesContext)
  if (!context) {
    throw new Error("useServices must be used within a ServicesProvider")
  }
  return context
}

export const ServicesProvider = ({ children }) => {
  const [services, setServices] = useState(() => loadFromLocalStorage("services", []))
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Fetch all services on mount
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true)
        const response = await api.get("/services")
        const data = response.data.data || []
        setServices(data)
        saveToLocalStorage("services", data)
        setError(null)
      } catch (err) {
        console.error("Error fetching services:", err)
        setError("Failed to load services. Please try again.")
        // Fallback to local storage if API fails
        const localData = loadFromLocalStorage("services", [])
        if (localData.length > 0) {
          setServices(localData)
        }
      } finally {
        setLoading(false)
      }
    }

    fetchServices()
  }, [])

  // Add a new service
  const addService = async (serviceData) => {
    try {
      setLoading(true)

      // Check if we have an image to upload
      const hasImageUpload = serviceData.image && serviceData.image.startsWith("data:")
      let response

      if (hasImageUpload) {
        // Use FormData for image upload
        const formData = new FormData()

        // Convert base64 to file
        const blob = await fetch(serviceData.image).then((r) => r.blob())
        const file = new File([blob], `service-${Date.now()}.jpeg`, { type: "image/jpeg" })
        formData.append("image", file)

        // Filter out empty features
        const filteredFeatures = serviceData.features?.filter((f) => f.trim() !== "") || []

        // Append each field individually
        formData.append("Title", serviceData.title)
        formData.append("Description", serviceData.description)
        formData.append("Price", serviceData.price)
        formData.append("Category", serviceData.category)
        formData.append("Status", serviceData.status)

        // For the Features array, append each item individually with array notation
        filteredFeatures.forEach((feature, index) => {
          formData.append(`Features[${index}]`, feature)
        })

        // Log FormData contents for debugging
        logFormData(formData)

        // Send the request
        response = await api.post("/services", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
      } else {
        // No image upload, use regular JSON request
        // Filter out empty features
        const filteredFeatures = serviceData.features?.filter((f) => f.trim() !== "") || []

        // Create a data object with the correct field names
        const data = {
          Title: serviceData.title,
          Description: serviceData.description,
          Price: serviceData.price,
          Category: serviceData.category,
          Status: serviceData.status,
          Features: filteredFeatures, // Keep as array
        }

        console.log("Sending JSON data to API:", data)

        // Send the request
        response = await api.post("/services", data)
      }

      const newService = response.data.data

      // Update local state and storage
      const updatedServices = [...services, newService]
      setServices(updatedServices)
      saveToLocalStorage("services", updatedServices)

      return newService
    } catch (err) {
      console.error("Error adding service:", err)
      const errorMessage = err.response?.data?.error || err.message || "Failed to add service"
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Update an existing service
  const updateService = async (id, updatedService) => {
    try {
      setLoading(true)

      // Check if we have an image to upload
      const hasImageUpload = updatedService.image && updatedService.image.startsWith("data:")
      let response

      if (hasImageUpload) {
        // Use FormData for image upload
        const formData = new FormData()

        // Convert base64 to file
        const blob = await fetch(updatedService.image).then((r) => r.blob())
        const file = new File([blob], `service-${Date.now()}.jpeg`, { type: "image/jpeg" })
        formData.append("image", file)

        // Filter out empty features
        const filteredFeatures = updatedService.features?.filter((f) => f.trim() !== "") || []

        // Append each field individually
        formData.append("Title", updatedService.title)
        formData.append("Description", updatedService.description)
        formData.append("Price", updatedService.price)
        formData.append("Category", updatedService.category)
        formData.append("Status", updatedService.status)

        // For the Features array, append each item individually with array notation
        filteredFeatures.forEach((feature, index) => {
          formData.append(`Features[${index}]`, feature)
        })

        // Log FormData contents for debugging
        logFormData(formData)

        // Send the request
        response = await api.put(`/services/${id}`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
      } else {
        // No image upload, use regular JSON request
        // Filter out empty features
        const filteredFeatures = updatedService.features?.filter((f) => f.trim() !== "") || []

        // Create a data object with the correct field names
        const data = {
          Title: updatedService.title,
          Description: updatedService.description,
          Price: updatedService.price,
          Category: updatedService.category,
          Status: updatedService.status,
          Features: filteredFeatures, // Keep as array
        }

        console.log("Sending JSON data to API for update:", data)

        // Send the request
        response = await api.put(`/services/${id}`, data)
      }

      const updatedServiceData = response.data.data

      // Update local state and storage
      const updatedServices = services.map((service) => (service.SR_Id === id ? updatedServiceData : service))
      setServices(updatedServices)
      saveToLocalStorage("services", updatedServices)

      return updatedServiceData
    } catch (err) {
      console.error("Error updating service:", err)
      const errorMessage = err.response?.data?.error || err.message || "Failed to update service"
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Delete a service
  const deleteService = async (id) => {
    try {
      setLoading(true)

      // Add debugging logs to help troubleshoot API issues
      console.log("Deleting service with ID:", id)

      await api.delete(`/services/${id}`)

      // Update local state and storage
      const updatedServices = services.filter((service) => service.SR_Id !== id)
      setServices(updatedServices)
      saveToLocalStorage("services", updatedServices)
    } catch (err) {
      console.error("Error deleting service:", err)
      const errorMessage = err.response?.data?.error || err.message || "Failed to delete service"
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearServices = () => {
    setServices([])
    removeFromLocalStorage("services")
  }

  const getServiceById = (id) => {
    // Try to find by numeric ID first
    const numericId = Number.parseInt(id)
    const service = services.find((service) => service.SR_Id === numericId)
    if (service) return service

    // If not found, try to find by string ID
    return services.find((service) => service.id === id)
  }

  // Refresh services data from API
  const refreshServices = async () => {
    try {
      setLoading(true)
      const response = await api.get("/services")
      const data = response.data.data || []
      setServices(data)
      saveToLocalStorage("services", data)
      setError(null)
      return data
    } catch (err) {
      console.error("Error refreshing services:", err)
      setError("Failed to refresh services. Please try again.")
      throw err
    } finally {
      setLoading(false)
    }
  }

  return (
    <ServicesContext.Provider
      value={{
        services,
        loading,
        error,
        setServices,
        addService,
        updateService,
        deleteService,
        clearServices,
        getServiceById,
        refreshServices,
      }}
    >
      {children}
    </ServicesContext.Provider>
  )
}


///////////////////
// // import React, { createContext, useContext, useState, useEffect } from 'react';
// import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';

// export const ServicesContext = createContext();

// export const useServices = () => {
//   const context = useContext(ServicesContext);
//   if (!context) {
//     throw new Error('useServices must be used within a ServicesProvider');
//   }
//   return context;
// };

// export const ServicesProvider = ({ children }) => {
//   const [services, setServices] = useState(() => loadFromLocalStorage('services', []));

//   useEffect(() => {
//     saveToLocalStorage('services', services);
//   }, [services]);

//   const clearServices = () => {
//     setServices([]);
//     removeFromLocalStorage('services');
//   };

//   const addService = (service) => {
//     setServices((prev) => [...prev, { ...service, id: Date.now() }]);
//   };

//   const updateService = (id, updatedService) => {
//     setServices((prev) => prev.map((service) => (service.id === id ? { ...service, ...updatedService } : service)));
//   };

//   const deleteService = (id) => {
//     setServices((prev) => prev.filter((service) => service.id !== id));
//   };

//   const getServiceById = (id) => {
//     // Try to find by numeric ID first
//     const numericId = parseInt(id);
//     const service = services.find((service) => service.id === numericId);
//     if (service) return service;

//     // If not found, try to find by string ID
//     return services.find((service) => service.id === id);
//   };

//   return (
//     <ServicesContext.Provider
//       value={{
//         services,
//         setServices,
//         addService,
//         updateService,
//         deleteService,
//         clearServices,
//         getServiceById,
//       }}
//     >
//       {children}
//     </ServicesContext.Provider>
//   );
// };
