# Modern Full-Stack Application

A modern full-stack application built with React, Express, and MySQL.

## Features

- 🚀 Modern React with Vite
- 🎨 Beautiful UI with Tailwind CSS and Radix UI
- 🔒 Secure backend with Express
- 📊 Data visualization with Chart.js and Recharts
- 🗺️ Interactive maps with Leaflet
- 🌐 Internationalization support
- 📱 Responsive design
- 🔍 Advanced search functionality
- 📈 Real-time data updates
- 🔐 Authentication and authorization

## Prerequisites

- Node.js (v18 or higher)
- MySQL (v8 or higher)
- npm or yarn

## Getting Started

### Backend Setup

1. Navigate to the backend directory:

   ```bash
   cd backend
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Create a `.env` file with the following variables:

   ```
   PORT=5000
   DATABASE_URL=mysql://user:password@localhost:3306/database_name
   JWT_SECRET=your_jwt_secret
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to the frontend directory:

   ```bash
   cd frontend
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

The application will be available at:

- Frontend: http://localhost:3000
- Backend: http://localhost:5000

## Project Structure

```
.
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── App.jsx
│   ├── public/
│   └── package.json
│
└── backend/
    ├── src/
    │   ├── controllers/
    │   ├── models/
    │   ├── routes/
    │   ├── middlewares/
    │   ├── utils/
    │   └── server.js
    └── package.json
```

## Available Scripts

### Backend

- `npm run dev`: Start development server
- `npm start`: Start production server
- `npm run lint`: Run ESLint

### Frontend

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run preview`: Preview production build
- `npm run lint`: Run ESLint

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
