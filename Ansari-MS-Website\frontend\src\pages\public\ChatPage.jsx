'use client';

import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Chat from '../../components/Chat';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorBoundary from '../../components/ErrorBoundary';

function ChatPage() {
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('authToken');
    if (!token) {
      navigate('/signin');
      return;
    }

    // Set loading to false after authentication check
    setIsLoading(false);
  }, [navigate]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <ErrorBoundary>
      <div className="w-full h-full">
        <Chat />
      </div>
    </ErrorBoundary>
  );
}

export default ChatPage;
