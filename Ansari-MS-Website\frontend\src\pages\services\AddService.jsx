"use client"

import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useServices } from "../../contexts/ServicesContext"
import { ArrowLeft, Upload, X, Plus, Minus } from "lucide-react"

const SERVICES_CATEGORIES = ["Premium Hens", "Baby Chickens", "Wholesale"]

const AddService = () => {
  const navigate = useNavigate()
  const { addService, loading: contextLoading } = useServices()
  const [formData, setFormData] = useState({
    title: "",
    category: "",
    description: "",
    price: "",
    status: "active",
    features: [""],
  })
  const [imageFile, setImageFile] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleFeatureChange = (index, value) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures[index] = value
    setFormData((prev) => ({
      ...prev,
      features: updatedFeatures,
    }))
  }

  const addFeature = () => {
    setFormData((prev) => ({
      ...prev,
      features: [...prev.features, ""],
    }))
  }

  const removeFeature = (index) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures.splice(index, 1)
    setFormData((prev) => ({
      ...prev,
      features: updatedFeatures,
    }))
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    if (file.size > 5 * 1024 * 1024) {
      setError("Image size should be less than 5MB")
      return
    }

    const reader = new FileReader()
    reader.onloadend = () => {
      setImagePreview(reader.result)
      setFormData((prev) => ({
        ...prev,
        image: reader.result,
      }))
    }
    reader.readAsDataURL(file)
  }

  const removeImage = () => {
    setImagePreview(null)
    setImageFile(null)
    setFormData((prev) => ({
      ...prev,
      image: null,
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError("")

    // Validate title
    if (!formData.title.trim()) {
      setError("Title is required")
      return
    }
    if (formData.title.trim().length < 3) {
      setError("Title must be at least 3 characters long")
      return
    }

    // Validate category
    const validCategories = ["Premium Hens", "Baby Chickens", "Wholesale"]
    if (!formData.category.trim() || !validCategories.includes(formData.category)) {
      setError("Valid category is required")
      return
    }

    // Validate price
    if (formData.price === "" || formData.price === null) {
      setError("Price is required")
      return
    }
    const priceValue = Number.parseFloat(formData.price)
    if (isNaN(priceValue) || priceValue < 0) {
      setError("Price must be a valid number equal or greater than 0")
      return
    }

    // Validate status
    const validStatuses = ["active", "inactive"]
    if (formData.status && !validStatuses.includes(formData.status)) {
      setError("Status must be either active or inactive")
      return
    }

    // Validate description
    if (!formData.description.trim()) {
      setError("Description is required")
      return
    }
    if (formData.description.trim().length < 5) {
      setError("Description must be at least 5 characters long")
      return
    }

    try {
      setLoading(true)
      setError("")
      setSuccess("")

      // Prepare data for API
      const serviceData = {
        ...formData,
        price: priceValue,
      }

      await addService(serviceData)

      // Show success message
      setSuccess("Service added successfully!")

      // Auto-redirect after 2 seconds
      setTimeout(() => {
        navigate("/admin/services")
      }, 2000)

    } catch (error) {
      console.error("Error adding service:", error)
      setError(error.message || "Failed to add service. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const isSubmitting = loading || contextLoading

  return (
    <div className="min-h-screen bg-muted dark:bg-gray-900 p-4 sm:p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6 flex items-center">
          <button
            onClick={() => navigate("/admin/services")}
            className="mr-4 p-2 rounded-lg hover:bg-card dark:hover:bg-gray-700 transition-colors border border-border-color"
          >
            <ArrowLeft size={20} className="text-textprimary dark:text-gray-300" />
          </button>
          <h1 className="text-2xl font-headingEn font-bold text-secondary dark:text-white">Add Service</h1>
        </div>

        {error && <div className="mb-6 p-4 bg-destructive/10 text-destructive rounded-lg border border-destructive/20 font-bodyEn">{error}</div>}
        {success && <div className="mb-6 p-4 bg-success/10 text-success rounded-lg border border-success/20 font-bodyEn">{success}</div>}

        <form onSubmit={handleSubmit} className="bg-card dark:bg-gray-800 rounded-lg shadow-card border border-border-color p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="title" className="block text-sm font-headingEn font-medium text-secondary dark:text-gray-300 mb-2">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-border-color dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white font-bodyEn"
                placeholder="Enter service title"
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-headingEn font-medium text-secondary dark:text-gray-300 mb-2">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-border-color dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white font-bodyEn"
              >
                <option value="">Select a category</option>
                {SERVICES_CATEGORIES.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Price *
              </label>
              <input
                type="text"
                id="price"
                name="price"
                value={formData.price}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                placeholder="Enter price (e.g., 450)"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows="4"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              placeholder="Enter service description"
            ></textarea>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Features
              <button
                type="button"
                onClick={addFeature}
                className="ml-2 inline-flex items-center p-1 bg-[#FF6B00] text-white rounded-full hover:bg-[#FF6B00]/90"
              >
                <Plus size={16} />
              </button>
            </label>
            {formData.features.map((feature, index) => (
              <div key={index} className="flex items-center mb-2">
                <input
                  type="text"
                  value={feature}
                  onChange={(e) => handleFeatureChange(index, e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder={`Feature ${index + 1}`}
                />
                {formData.features.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeFeature(index)}
                    className="ml-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <Minus size={16} />
                  </button>
                )}
              </div>
            ))}
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Service Image</label>
            {!imagePreview ? (
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600 dark:text-gray-400">
                    <label
                      htmlFor="image-upload"
                      className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#FF6B00] hover:text-[#FF6B00]/80 focus-within:outline-none"
                    >
                      <span>Upload an image</span>
                      <input
                        id="image-upload"
                        name="image-upload"
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 5MB</p>
                </div>
              </div>
            ) : (
              <div className="mt-1 relative">
                <img
                  src={imagePreview || "/placeholder.svg"}
                  alt="Preview"
                  className="h-64 w-full object-cover rounded-lg"
                />
                <button
                  type="button"
                  onClick={removeImage}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate("/admin/services")}
              className="px-6 py-3 text-sm font-bodyEn font-medium text-textprimary dark:text-gray-300 bg-muted dark:bg-gray-700 rounded-lg hover:bg-border-color dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary border border-border-color transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-3 text-sm font-bodyEn font-medium text-white bg-primary rounded-lg hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary flex items-center shadow-card transition-colors"
            >
              {isSubmitting ? (
                <>
                  <span className="animate-spin mr-2">⟳</span>
                  Saving...
                </>
              ) : (
                "Save Service"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddService


////////////////////////
// // 'use client';

// import { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { useServices } from '../../contexts/ServicesContext';
// import { ArrowLeft, Upload, X, Plus, Minus } from 'lucide-react';

// const SERVICES_CATEGORIES = ['Premium Hens', 'Baby Chickens', 'Wholesale'];

// const AddService = () => {
//   const navigate = useNavigate();
//   const { addService } = useServices();
//   const [formData, setFormData] = useState({
//     title: '',
//     category: '',
//     description: '',
//     price: '',
//     status: 'active',
//     image: '',
//     features: [''],
//   });
//   const [imagePreview, setImagePreview] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState('');

//   const handleChange = (e) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }));
//   };

//   const handleFeatureChange = (index, value) => {
//     const updatedFeatures = [...formData.features];
//     updatedFeatures[index] = value;
//     setFormData((prev) => ({
//       ...prev,
//       features: updatedFeatures,
//     }));
//   };

//   const addFeature = () => {
//     setFormData((prev) => ({
//       ...prev,
//       features: [...prev.features, ''],
//     }));
//   };

//   const removeFeature = (index) => {
//     const updatedFeatures = [...formData.features];
//     updatedFeatures.splice(index, 1);
//     setFormData((prev) => ({
//       ...prev,
//       features: updatedFeatures,
//     }));
//   };

//   const handleImageChange = (e) => {
//     const file = e.target.files[0];
//     if (!file) return;

//     if (file.size > 5 * 1024 * 1024) {
//       setError('Image size should be less than 5MB');
//       return;
//     }

//     const reader = new FileReader();
//     reader.onloadend = () => {
//       setImagePreview(reader.result);
//       setFormData((prev) => ({
//         ...prev,
//         image: reader.result,
//       }));
//     };
//     reader.readAsDataURL(file);
//   };

//   const removeImage = () => {
//     setImagePreview(null);
//     setFormData((prev) => ({
//       ...prev,
//       image: '',
//     }));
//   };

//   const handleSubmit = async (e) => {
//   e.preventDefault();
//   setError('');

//   // Validate title
//   if (!formData.title.trim()) {
//     setError('Title is required');
//     return;
//   }
//   if (formData.title.trim().length < 3) {
//     setError('Title must be at least 3 characters long');
//     return;
//   }

//   // Validate category
//   const validCategories = ['Premium Hens', 'Baby Chickens', 'Wholesale'];
//   if (!formData.category.trim() || !validCategories.includes(formData.category)) {
//     setError('Valid category is required');
//     return;
//   }




//   // Validate price
//   if (formData.price === '' || formData.price === null) {
//     setError('Price is required');
//     return;
//   }
//   const priceValue = parseFloat(formData.price);
//   if (isNaN(priceValue) || priceValue < 0) {
//     setError('Price must be a valid number equal or greater than 0');
//     return;
//   }

//     // Validate status
//   const validStatuses = ['active', 'inactive'];
//   if (formData.status && !validStatuses.includes(formData.status)) {
//     setError('Status must be either active or inactive');
//     return;
//   }

//   // Validate description
//   if (!formData.description.trim()) {
//     setError('Description is required');
//     return;
//   }
//   if (formData.description.trim().length < 5) {
//     setError('Description must be at least 5 characters long');
//     return;
//   }




//   // Filter out empty features
//   const filteredFeatures = formData.features.filter((feature) => feature.trim() !== '');

//   if (filteredFeatures.length > 30) {
//     setError('You can only add up to 30 features');
//     return;
//   }



//   try {
//     setLoading(true);
//     await addService({
//       ...formData,
//       price: priceValue,
//       features: filteredFeatures.length > 0 ? filteredFeatures : null,
//       date: new Date().toISOString(),
//     });
//     navigate('/admin/services');
//   } catch (error) {
//     console.error('Error adding service:', error);
//     setError('Failed to add service. Please try again.');
//   } finally {
//     setLoading(false);
//   }
// };


//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
//       <div className="max-w-4xl mx-auto">
//         <div className="mb-6 flex items-center">
//           <button
//             onClick={() => navigate('/admin/services')}
//             className="mr-4 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
//           >
//             <ArrowLeft size={20} className="text-gray-600 dark:text-gray-300" />
//           </button>
//           <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Service</h1>
//         </div>

//         {error && <div className="mb-6 p-4 bg-red-100 text-red-800 rounded-lg">{error}</div>}

//         <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
//             <div>
//               <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Title *
//               </label>
//               <input
//                 type="text"
//                 id="title"
//                 name="title"
//                 value={formData.title}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 placeholder="Enter service title"
//               />
//             </div>

//             <div>
//               <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Category *
//               </label>
//               <select
//                 id="category"
//                 name="category"
//                 value={formData.category}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               >
//                 <option value="">Select a category</option>
//                 {SERVICES_CATEGORIES.map((category) => (
//                   <option key={category} value={category}>
//                     {category}
//                   </option>
//                 ))}
//               </select>
//             </div>

//             <div>
//               <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Price *
//               </label>
//               <input
//                 type="text"
//                 id="price"
//                 name="price"
//                 value={formData.price}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                 placeholder="Enter price (e.g., 450 AF per hen)"
//               />
//             </div>

//             <div>
//               <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                 Status
//               </label>
//               <select
//                 id="status"
//                 name="status"
//                 value={formData.status}
//                 onChange={handleChange}
//                 className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               >
//                 <option value="active">Active</option>
//                 <option value="inactive">Inactive</option>
//               </select>
//             </div>
//           </div>

//           <div className="mb-6">
//             <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//               Description *
//             </label>
//             <textarea
//               id="description"
//               name="description"
//               value={formData.description}
//               onChange={handleChange}
//               rows="4"
//               className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               placeholder="Enter service description"
//             ></textarea>
//           </div>

//           <div className="mb-6">
//             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//               Features
//               <button
//                 type="button"
//                 onClick={addFeature}
//                 className="ml-2 inline-flex items-center p-1 bg-[#FF6B00] text-white rounded-full hover:bg-[#FF6B00]/90"
//               >
//                 <Plus size={16} />
//               </button>
//             </label>
//             {formData.features.map((feature, index) => (
//               <div key={index} className="flex items-center mb-2">
//                 <input
//                   type="text"
//                   value={feature}
//                   onChange={(e) => handleFeatureChange(index, e.target.value)}
//                   className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//                   placeholder={`Feature ${index + 1}`}
//                 />
//                 {formData.features.length > 1 && (
//                   <button
//                     type="button"
//                     onClick={() => removeFeature(index)}
//                     className="ml-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
//                   >
//                     <Minus size={16} />
//                   </button>
//                 )}
//               </div>
//             ))}
//           </div>

//           <div className="mb-6">
//             <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Service Image</label>
//             {!imagePreview ? (
//               <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-lg">
//                 <div className="space-y-1 text-center">
//                   <Upload className="mx-auto h-12 w-12 text-gray-400" />
//                   <div className="flex text-sm text-gray-600 dark:text-gray-400">
//                     <label
//                       htmlFor="image-upload"
//                       className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-[#FF6B00] hover:text-[#FF6B00]/80 focus-within:outline-none"
//                     >
//                       <span>Upload an image</span>
//                       <input
//                         id="image-upload"
//                         name="image-upload"
//                         type="file"
//                         className="sr-only"
//                         accept="image/*"
//                         onChange={handleImageChange}
//                       />
//                     </label>
//                     <p className="pl-1">or drag and drop</p>
//                   </div>
//                   <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 5MB</p>
//                 </div>
//               </div>
//             ) : (
//               <div className="mt-1 relative">
//                 <img
//                   src={imagePreview || '/placeholder.svg'}
//                   alt="Preview"
//                   className="h-64 w-full object-cover rounded-lg"
//                 />
//                 <button
//                   type="button"
//                   onClick={removeImage}
//                   className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
//                 >
//                   <X size={16} />
//                 </button>
//               </div>
//             )}
//           </div>

//           <div className="flex justify-end space-x-4">
//             <button
//               type="button"
//               onClick={() => navigate('/admin/services')}
//               className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
//             >
//               Cancel
//             </button>
//             <button
//               type="submit"
//               disabled={loading}
//               className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] flex items-center"
//             >
//               {loading ? (
//                 <>
//                   <span className="animate-spin mr-2">⟳</span>
//                   Saving...
//                 </>
//               ) : (
//                 'Save Service'
//               )}
//             </button>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default AddService;
