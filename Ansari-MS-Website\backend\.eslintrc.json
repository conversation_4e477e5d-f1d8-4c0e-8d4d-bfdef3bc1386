{"env": {"node": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "no-console": "warn"}}