/* eslint-disable react/prop-types */
// src/context/FarmContext.js
import { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:5432/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Create Context
const FarmContext = createContext();

// Create a custom hook to use the context
export const useFarm = () => {
  const context = useContext(FarmContext);
  if (!context) {
    throw new Error('useFarm must be used within a FarmProvider');
  }
  return context;
};

// Create a provider component
export const FarmProvider = ({ children }) => {
  const [farms, setFarms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [farmers, setFarmers] = useState([]);

  // Fetch farmers from API
  const fetchFarmers = async () => {
    try {
      const response = await api.get('/users/farmers');
      if (response.data.success) {
        setFarmers(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching farmers:', error);
    }
  };

  // Fetch farms from API
  const fetchFarms = async () => {
    try {
      setLoading(true);

      // Fetch both farms and farmers data
      const [farmsResponse, farmersResponse] = await Promise.all([
        api.get('/farms'),
        api.get('/users/farmers')
      ]);

      if (farmsResponse.data.success) {
        // Store farmers data
        const farmersData = farmersResponse.data.success ? farmersResponse.data.data : [];
        setFarmers(farmersData);

        // Transform backend data to frontend format
        const transformedFarms = farmsResponse.data.farms.map(farm => {
          // Parse location from MySQL POINT format to "latitude longitude"
          let location = farm.F_Location;
          if (location && location.includes('POINT(')) {
            // Extract coordinates from POINT(longitude latitude) format
            const coords = location.match(/POINT\(([^)]+)\)/);
            if (coords && coords[1]) {
              const [longitude, latitude] = coords[1].split(' ');
              location = `${latitude} ${longitude}`; // Convert to "latitude longitude" format
            }
          }

          // Find farmer name by User_Id
          const farmer = farmersData.find(f => f.u_Id === farm.User_Id);
          const ownerName = farmer ? farmer.name : `User ${farm.User_Id}`;

          return {
            id: farm.F_Id,
            name: farm.FName,
            email: farm.F_Email,
            owner: ownerName, // Use farmer name instead of ID
            phone: farm.F_Phone,
            location: location,
            userId: farm.User_Id,
            // Add default values for frontend compatibility
            type: 'broiler',
            capacity: 1000,
            currentStock: 0,
            status: 'active',
            lastInspection: '',
            nextInspection: '',
            notes: '',
          };
        });
        setFarms(transformedFarms);
      }
    } catch (error) {
      console.error('Error fetching farms:', error);
      toast.error('Failed to fetch farms');
    } finally {
      setLoading(false);
    }
  };

  // Load farms on component mount
  useEffect(() => {
    fetchFarms();
  }, []);

  const addFarm = async (farmData) => {
    try {
      setLoading(true);

      // Transform frontend data to backend format
      const backendData = {
        FName: farmData.name,
        F_Email: farmData.email,
        owner: farmData.ownerId || 1, // Use ownerId if provided, otherwise default to 1
        F_Phone: farmData.phone,
        F_Location: farmData.location,
        User_Id: parseInt(farmData.userId),
      };

      console.log('Sending farm data to backend:', backendData);
      const response = await api.post('/farms', backendData);

      if (response.data.success) {
        // Refresh farms list
        await fetchFarms();
      }
    } catch (error) {
      console.error('Error adding farm:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateFarm = async (id, farmData) => {
    try {
      setLoading(true);

      // Transform frontend data to backend format
      const backendData = {
        FName: farmData.name,
        F_Email: farmData.email,
        owner: farmData.ownerId || 1, // Use ownerId if provided, otherwise default to 1
        F_Phone: farmData.phone,
        F_Location: farmData.location,
        User_Id: parseInt(farmData.userId),
      };

      const response = await api.put(`/farms/${id}`, backendData);

      if (response.data.success) {
        // Refresh farms list
        await fetchFarms();
      }
    } catch (error) {
      console.error('Error updating farm:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteFarm = async (id) => {
    try {
      setLoading(true);

      const response = await api.delete(`/farms/${id}`);

      if (response.data.success) {
        // Refresh farms list
        await fetchFarms();
        toast.success('Farm deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting farm:', error);
      const errorMessage = error.response?.data?.error || 'Failed to delete farm';
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <FarmContext.Provider
      value={{
        farms,
        loading,
        addFarm,
        updateFarm,
        deleteFarm,
        fetchFarms,
      }}
    >
      {children}
    </FarmContext.Provider>
  );
};
