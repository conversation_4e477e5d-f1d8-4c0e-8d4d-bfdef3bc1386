import pool from '../config/db.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createFarmsTable() {
  try {
    console.log('Creating Farms table...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../migrations/create_farms_table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await pool.query(sql);
    
    console.log('✅ Farms table created successfully!');
    
    // Check if table was created
    const [tables] = await pool.query("SHOW TABLES LIKE 'Farms'");
    if (tables.length > 0) {
      console.log('✅ Farms table exists in database');
      
      // Show table structure
      const [structure] = await pool.query("DESCRIBE Farms");
      console.log('📋 Farms table structure:');
      console.table(structure);
    } else {
      console.log('❌ Farms table was not created');
    }
    
  } catch (error) {
    console.error('❌ Error creating Farms table:', error);
  } finally {
    await pool.end();
  }
}

createFarmsTable();
