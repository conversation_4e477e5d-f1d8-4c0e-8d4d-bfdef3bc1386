/* eslint-disable no-unused-vars */
'use client';

import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Eye, EyeOff, UserPlus, Upload, User, Mail, MapPin, UserCircle, Lock, CheckCircle } from 'lucide-react';
import { useUser } from '../../contexts/UsersContext';
import axios from 'axios';

import Modal from '../../components/Modal';

const Register = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    profileImage: null,
    address: '',
    role: 'user',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [redirectCountdown, setRedirectCountdown] = useState(3);
  const navigate = useNavigate();
  const { updateUserData } = useUser();

  // Handle countdown and redirect after successful registration
  useEffect(() => {
    let timer;
    if (showSuccessModal && redirectCountdown > 0) {
      timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);
      }, 1000);
    } else if (showSuccessModal && redirectCountdown === 0) {
      navigate('/verify-email');
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showSuccessModal, redirectCountdown, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (file) {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        setError('Only JPG, JPEG, or PNG images are allowed.');
        setFormData((prev) => ({ ...prev, profileImage: null }));
        setImagePreview(null);
        return;
      }

      setFormData((prev) => ({
        ...prev,
        profileImage: file,
      }));

      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    setLoading(true);

    const fieldLabels = {
      U_FirstName: 'First Name',
      U_LastName: 'Last Name',
      U_Email: 'Email Address',
      address: 'Address',
      image: 'Profile Image',
      Role: 'Role',
      password: 'Password',
      confirmPassword: 'Confirm Password',
    };

    try {
      const fd = new FormData();
      fd.append('U_FirstName', formData.firstName);
      fd.append('U_LastName', formData.lastName);
      fd.append('U_Email', formData.email);
      fd.append('address', formData.address);
      fd.append('image', formData.profileImage);
      fd.append('Role', formData.role);
      fd.append('password', formData.password);

      const response = await axios.post('http://localhost:5432/api/v1/users/register', fd, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setError('');
        setRedirectCountdown(3); // Reset countdown to 3 seconds
        setShowSuccessModal(true);
        // Store email in localStorage for the verification page
        localStorage.setItem('registeredEmail', formData.email);
      } else {
        setError(response.data.message || 'Registration failed');
      }
    } catch (err) {
      console.error('Registration Error:', err);

      const backendError = err.response?.data?.error;

      const friendlyMessage =
        typeof backendError === 'string'
          ? backendError.replace(/"([^"]+)"/g, (_, field) => fieldLabels[field] || field)
          : 'An error occurred during registration from backend.';

      setError(friendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-muted">
      {/* Left side - Form */}
      <div className="w-full md:w-1/2 px-6 py-8 md:px-12 md:py-12 flex items-center justify-center">
        <div className="w-full max-w-lg">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold font-heading text-text-primary">Create your account</h1>
            <p className="mt-2 text-muted-foreground">Join our community and start your journey</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="bg-card rounded-lg shadow-md py-8 px-8 sm:px-10 border border-card w-full"
          >
            <form onSubmit={handleSubmit} className="space-y-6 w-full">
              {/* Profile Image Upload */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col items-center mb-6"
              >
                <div className="relative group">
                  <div
                    className={`w-24 h-24 rounded-full overflow-hidden bg-muted flex items-center justify-center border-2 ${
                      imagePreview ? 'border-primary shadow-md' : 'border-dashed border-border-dark'
                    }`}
                  >
                    {imagePreview ? (
                      <img
                        src={imagePreview || '/placeholder.svg'}
                        alt="Profile Preview"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <UserCircle className="w-12 h-12 text-muted-foreground/60" />
                    )}
                  </div>
                  <label
                    htmlFor="profileImage"
                    className="absolute inset-0 flex items-center justify-center bg-secondary bg-opacity-50 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                  >
                    <Upload className="w-5 h-5" />
                  </label>
                  <input
                    type="file"
                    id="profileImage"
                    name="profileImage"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </div>
                <p className="mt-2 text-xs text-muted-foreground">Upload profile picture</p>
              </motion.div>

              {/* Name Fields */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-card-foreground mb-1">
                    First Name
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-4 w-4 text-muted-foreground/60" />
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      required
                      className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                      placeholder="John"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-card-foreground mb-1">
                    Last Name
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-4 w-4 text-muted-foreground/60" />
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      required
                      className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                      placeholder="Doe"
                    />
                  </div>
                </div>
              </motion.div>

              {/* Email Field */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <label htmlFor="email" className="block text-sm font-medium text-card-foreground mb-1">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-muted-foreground/60" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                    placeholder="<EMAIL>"
                  />
                </div>
              </motion.div>

              {/* Address Field */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              >
                <label htmlFor="address" className="block text-sm font-medium text-card-foreground mb-1">
                  Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="h-4 w-4 text-muted-foreground/60" />
                  </div>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    required
                    className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                    placeholder="Your full address"
                  />
                </div>
              </motion.div>

              {/* Role Selection */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.4 }}
              >
                <label htmlFor="role" className="block text-sm font-medium text-card-foreground mb-1">
                  Select Your Role
                </label>
                <div className="relative">
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    className="w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50 appearance-none"
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                    <option value="farmer">Farmer</option>
                    <option value="shopper">Shopper</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg
                      className="h-5 w-5 text-muted-foreground/60"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </motion.div>

              {/* Password Fields */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
                className="space-y-4"
              >
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-1">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-4 w-4 text-muted-foreground/60" />
                    </div>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-card-foreground mb-1">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-4 w-4 text-muted-foreground/60" />
                    </div>
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      required
                      className="pl-10 w-full py-2.5 px-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-card-foreground text-sm bg-muted/50"
                      placeholder="••••••••"
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground/60 hover:text-muted-foreground" />
                      )}
                    </button>
                  </div>
                </div>
              </motion.div>

              {/* Terms and Conditions */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.6 }}
                className="flex items-center"
              >
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  required
                  className="h-4 w-4 text-primary focus:ring-primary/50 border-border rounded"
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-muted-foreground">
                  I agree to the{' '}
                  <a href="#" className="text-primary hover:text-primary-hover font-medium">
                    Terms of Service
                  </a>{' '}
                  and{' '}
                  <a href="#" className="text-primary hover:text-primary-hover font-medium">
                    Privacy Policy
                  </a>
                </label>
              </motion.div>

              {/* Submit Button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.7 }}
              >
                <button
                  type="submit"
                  disabled={loading}
                  className={`w-full flex justify-center items-center py-2.5 px-4 border border-transparent rounded-lg text-primary-foreground font-medium
  bg-primary hover:bg-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50
  transition-colors duration-200 shadow-md ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {loading ? (
                    <div className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Creating your account...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      Create Account
                      <UserPlus className="ml-2 h-5 w-5" />
                    </div>
                  )}
                </button>
              </motion.div>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mb-6 bg-destructive/10 border border-destructive/20 rounded-xl p-4 text-destructive"
                >
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-destructive" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium">{error}</p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Sign In Link */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.8 }}
                className="text-center"
              >
                <p className="text-sm text-muted-foreground">
                  Already have an account?{' '}
                  <Link to="/signin" className="text-primary hover:text-primary-hover font-medium">
                    Sign in
                  </Link>
                </p>
              </motion.div>
            </form>
          </motion.div>
        </div>
      </div>

      {/* Right side - Image/Illustration */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="hidden md:block md:w-1/2 bg-gradient-to-br from-primary to-primary-hover"
      >
        <div className="h-full flex flex-col justify-center items-center text-primary-foreground p-12">
          <div className="max-w-md">
            <h2 className="text-3xl font-bold font-heading mb-6">Welcome to Our Platform</h2>
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Join our community of users, farmers, and shoppers to access exclusive features</p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.4 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Connect with local farmers and discover fresh produce directly from the source</p>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
                className="flex items-start"
              >
                <CheckCircle className="h-6 w-6 mr-3 flex-shrink-0" />
                <p>Shop for high-quality products with secure transactions and reliable delivery</p>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Success Modal - Now with auto-redirect */}
      {showSuccessModal && (
        <Modal
          isOpen={showSuccessModal}
          onClose={() => {
            setShowSuccessModal(false);
            navigate('/verify-email');
          }}
          title="Registration Successful"
        >
          <div className="text-center">
            <CheckCircle className="mx-auto h-12 w-12 text-success mb-4" />
            <p className="text-card-foreground mb-2">Your account has been created successfully!</p>
            <p className="text-muted-foreground">
              Redirecting to email verification in <span className="font-bold text-primary">{redirectCountdown}</span>{' '}
              seconds...
            </p>

            {/* Progress bar for visual feedback */}
            <div className="w-full bg-muted rounded-full h-2.5 mt-4">
              <div
                className="bg-primary h-2.5 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${((3 - redirectCountdown) / 3) * 100}%` }}
              ></div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default Register;
