import { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:5432/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Create the context
const ChickenContext = createContext();

// Custom hook to use the context
export const useChicken = () => {
  const context = useContext(ChickenContext);
  if (!context) {
    throw new Error('useChicken must be used within a ChickenProvider');
  }
  return context;
};

// Create a provider component
export const ChickenProvider = ({ children }) => {
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  
  // State for different chicken lifecycle stages
  const [purchases, setPurchases] = useState([]);
  const [allocations, setAllocations] = useState([]);
  const [buybacks, setBuybacks] = useState([]);
  const [distributions, setDistributions] = useState([]);
  
  // State for farms and shops data
  const [farms, setFarms] = useState([]);
  const [shops, setShops] = useState([]);

  // Fetch farms and shops data
  const fetchFarmsAndShops = async () => {
    try {
      const [farmsResponse, shopsResponse] = await Promise.all([
        api.get('/farms'),
        api.get('/shops')
      ]);
      
      if (farmsResponse.data.success) {
        // Farm API returns data in 'farms' property, not 'data'
        setFarms(farmsResponse.data.farms || farmsResponse.data.data);
      }

      if (shopsResponse.data.success) {
        // Shop API might also have different structure
        setShops(shopsResponse.data.shops || shopsResponse.data.data);
      }
    } catch (error) {
      console.error('Error fetching farms and shops:', error);
      console.error('Farms response error:', error.response?.data);
      console.error('Shops response error:', error.response?.data);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await api.get('/chickens/statistics');
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Purchase Management
  const fetchPurchases = async () => {
    try {
      setLoading(true);
      const response = await api.get('/chickens/purchases');
      if (response.data.success) {
        setPurchases(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching purchases:', error);
      toast.error('Failed to fetch purchases');
    } finally {
      setLoading(false);
    }
  };

  const createPurchase = async (purchaseData) => {
    try {
      const response = await api.post('/chickens/purchases', purchaseData);
      if (response.data.success) {
        await fetchPurchases();
        await fetchStatistics();
        toast.success('Purchase created successfully');
        return response.data.data;
      }
    } catch (error) {
      console.error('Error creating purchase:', error);
      throw error;
    }
  };

  const deletePurchase = async (id) => {
    try {
      const response = await api.delete(`/chickens/purchases/${id}`);
      if (response.data.success) {
        await fetchPurchases();
        await fetchStatistics();
        toast.success('Purchase deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting purchase:', error);
      toast.error('Failed to delete purchase');
      throw error;
    }
  };

  // Farm Allocation Management
  const fetchAllocations = async () => {
    try {
      setLoading(true);
      const response = await api.get('/chickens/allocations');
      if (response.data.success) {
        setAllocations(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching allocations:', error);
      console.error('Allocations error details:', error.response?.data);
      console.error('Allocations error status:', error.response?.status);
      toast.error('Failed to fetch allocations');
    } finally {
      setLoading(false);
    }
  };

  const allocateToFarm = async (allocationData) => {
    try {
      const response = await api.post('/chickens/allocations', allocationData);
      if (response.data.success) {
        await fetchAllocations();
        await fetchPurchases();
        await fetchStatistics();
        toast.success('Chickens allocated to farm successfully');
        return response.data.data;
      }
    } catch (error) {
      console.error('Error allocating to farm:', error);
      throw error;
    }
  };

  const deleteAllocation = async (id) => {
    try {
      const response = await api.delete(`/chickens/allocations/${id}`);
      if (response.data.success) {
        await fetchAllocations();
        await fetchPurchases();
        await fetchStatistics();
        toast.success('Allocation deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting allocation:', error);
      toast.error('Failed to delete allocation');
      throw error;
    }
  };

  // Buyback Management
  const fetchBuybacks = async () => {
    try {
      setLoading(true);
      const response = await api.get('/chickens/buybacks');
      if (response.data.success) {
        setBuybacks(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching buybacks:', error);
      toast.error('Failed to fetch buybacks');
    } finally {
      setLoading(false);
    }
  };

  const buyBackFromFarm = async (buybackData) => {
    try {
      const response = await api.post('/chickens/buybacks', buybackData);
      if (response.data.success) {
        await fetchBuybacks();
        await fetchAllocations();
        await fetchStatistics();
        toast.success('Chickens bought back from farm successfully');
        return response.data.data;
      }
    } catch (error) {
      console.error('Error buying back from farm:', error);
      throw error;
    }
  };

  const deleteBuyback = async (id) => {
    try {
      const response = await api.delete(`/chickens/buybacks/${id}`);
      if (response.data.success) {
        await fetchBuybacks();
        await fetchAllocations();
        await fetchStatistics();
        toast.success('Buyback deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting buyback:', error);
      toast.error('Failed to delete buyback');
      throw error;
    }
  };

  // Shop Distribution Management
  const fetchDistributions = async () => {
    try {
      console.log('🔍 Fetching distributions...');
      setLoading(true);
      const response = await api.get('/chickens/distributions');
      console.log('📦 Distributions API response:', response.data);

      if (response.data.success) {
        console.log('✅ Distributions data:', response.data.data);
        setDistributions(response.data.data);
      } else {
        console.error('❌ API returned success: false');
        toast.error('Failed to fetch distributions');
      }
    } catch (error) {
      console.error('❌ Error fetching distributions:', error);
      console.error('❌ Error details:', error.response?.data);
      toast.error('Failed to fetch distributions');
    } finally {
      setLoading(false);
    }
  };

  const distributeToShop = async (distributionData) => {
    try {
      const response = await api.post('/chickens/distributions', distributionData);
      if (response.data.success) {
        await fetchDistributions();
        await fetchBuybacks();
        await fetchStatistics();
        toast.success('Chickens distributed to shop successfully');
        return response.data.data;
      }
    } catch (error) {
      console.error('Error distributing to shop:', error);
      throw error;
    }
  };

  const deleteDistribution = async (id) => {
    try {
      const response = await api.delete(`/chickens/distributions/${id}`);
      if (response.data.success) {
        await fetchDistributions();
        await fetchBuybacks();
        await fetchStatistics();
        toast.success('Distribution deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting distribution:', error);
      toast.error('Failed to delete distribution');
      throw error;
    }
  };

  // Load initial data
  useEffect(() => {
    fetchFarmsAndShops();
    fetchStatistics();
  }, []);

  const value = {
    loading,
    statistics,
    purchases,
    allocations,
    buybacks,
    distributions,
    farms,
    shops,
    
    // Purchase methods
    fetchPurchases,
    createPurchase,
    deletePurchase,
    
    // Allocation methods
    fetchAllocations,
    allocateToFarm,
    deleteAllocation,
    
    // Buyback methods
    fetchBuybacks,
    buyBackFromFarm,
    deleteBuyback,
    
    // Distribution methods
    fetchDistributions,
    distributeToShop,
    deleteDistribution,
    
    // Utility methods
    fetchStatistics,
    fetchFarmsAndShops,
  };

  return <ChickenContext.Provider value={value}>{children}</ChickenContext.Provider>;
};
