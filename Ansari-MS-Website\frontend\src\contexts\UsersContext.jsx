
/* eslint-disable react/prop-types */
"use client"

import { createContext, useContext, useState } from "react"
import axios from "axios"

// Create axios instance
const api = axios.create({
  baseURL: "http://localhost:5432/api/v1",
  headers: {
    "Content-Type": "application/json",
  },
})

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // Clear auth data and redirect to login
      localStorage.removeItem("authToken")
      localStorage.removeItem("user")
      window.location.href = "/login"
    }
    return Promise.reject(error)
  },
)

// Create the context
export const UsersContext = createContext(undefined)

export function UsersProvider({ children }) {
  // Users list state
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Current user state
  const [currentUser, setCurrentUser] = useState(() => {
    const savedUser = localStorage.getItem("user")
    return savedUser ? JSON.parse(savedUser) : null
  })

  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    return !!localStorage.getItem("authToken")
  })

  // Fetch all users
  const fetchUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await api.get("/users")
      if (response.data.success && response.data.users) {
        setUsers(response.data.users)
      } else {
        setError(response.data.error || "Failed to fetch users")
      }
    } catch (err) {
      setError(err.message || "An error occurred while fetching users")
    } finally {
      setLoading(false)
    }
  }

  // Add a new user
  const addUser = async (userData) => {
    setLoading(true)
    setError(null)
    try {
      const formData = new FormData()

      // Add user data to form data (excluding image to handle separately)
      Object.entries(userData).forEach(([key, value]) => {
        if (key !== 'image' && value !== undefined && value !== null) {
          formData.append(key, value)
        }
      })

      // Handle image separately to avoid duplication
      if (userData.image instanceof File) {
        formData.append("image", userData.image)
        console.log("FormData: Image file added:", userData.image.name)
      } else {
        console.log("FormData: No image file to add")
      }

      // Log FormData contents for debugging
      console.log("FormData contents:")
      for (let [key, value] of formData.entries()) {
        console.log(key, ":", value instanceof File ? `File: ${value.name}` : value)
      }

      const response = await api.post("/users/register", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })

      if (response.data.success && response.data.user) {
        setUsers((prev) => [...prev, response.data.user])
      }
      return response.data
    } catch (err) {
      setError(err.message || "An error occurred while adding user")
      return {
        success: false,
        message: err.response?.data?.error || err.message || "An error occurred while adding user",
        error: err.response?.data?.error || err.message,
      }
    } finally {
      setLoading(false)
    }
  }

  // Update a user
  const updateUser = async (id, userData) => {
    setLoading(true)
    setError(null)
    try {
      const formData = new FormData()

      // Add user data to form data (excluding image to handle separately)
      Object.entries(userData).forEach(([key, value]) => {
        if (key !== 'image' && value !== undefined && value !== null) {
          formData.append(key, value)
        }
      })

      // Handle image separately to avoid duplication
      if (userData.image instanceof File) {
        formData.append("image", userData.image)
      }

      const response = await api.put(`/users/${id}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })

      if (response.data.success) {
        setUsers((prev) => prev.map((user) => (user.u_Id === id ? { ...user, ...userData } : user)))

        // If updating current user, update currentUser state too
        if (currentUser && currentUser.u_Id === id) {
          const updatedUser = { ...currentUser, ...userData }
          setCurrentUser(updatedUser)
          localStorage.setItem("user_data", JSON.stringify(updatedUser))
        }
      }
      return response.data
    } catch (err) {
      setError(err.message || "An error occurred while updating user")
      return {
        success: false,
        message: err.response?.data?.error || err.message || "An error occurred while updating user",
        error: err.response?.data?.error || err.message,
      }
    } finally {
      setLoading(false)
    }
  }

  // Delete a user
  const deleteUser = async (id) => {
    setLoading(true)
    setError(null)
    try {
      const response = await api.delete(`/users/${id}`)
      if (response.data.success) {
        setUsers((prev) => prev.filter((user) => user.u_Id !== id))
      }
      return response.data
    } catch (err) {
      setError(err.message || "An error occurred while deleting user")
      return {
        success: false,
        message: err.response?.data?.error || err.message || "An error occurred while deleting user",
        error: err.response?.data?.error || err.message,
      }
    } finally {
      setLoading(false)
    }
  }

  // Clear users
  const clearUsers = () => {
    setUsers([])
  }

  // Update current user
  const updateCurrentUser = async (userData) => {
    if (!currentUser) return null

    try {
      const response = await updateUser(currentUser.u_Id, userData)
      if (response.success) {
        const updatedUser = { ...currentUser, ...userData }
        setCurrentUser(updatedUser)
        localStorage.setItem("user", JSON.stringify(updatedUser))
      }
      return response
    } catch (err) {
      return {
        success: false,
        message: err.response?.data?.error || err.message || "An error occurred while updating profile",
        error: err.response?.data?.error || err.message,
      }
    }
  }

  // Login
  const login = async (email, password) => {
    setLoading(true)
    setError(null)
    try {
      const response = await api.post("/users/login", {
        U_Email: email,
        password,
      })

      if (response.data.success && response.data.user && response.data.token) {
        setCurrentUser(response.data.user)
        setIsAuthenticated(true)
        localStorage.setItem("authToken", response.data.token)
        localStorage.setItem("user", JSON.stringify(response.data.user))
      } else {
        setError(response.data.error || "Login failed")
      }
      return response.data
    } catch (err) {
      setError(err.message || "An error occurred during login")
      return {
        success: false,
        message: err.response?.data?.error || err.message || "An error occurred during login",
        error: err.response?.data?.error || err.message,
      }
    } finally {
      setLoading(false)
    }
  }

  // Logout
  const logout = async () => {
    try {
      await api.post("/users/logout")
    } catch (err) {
      console.error("Logout error:", err)
    } finally {
      setCurrentUser(null)
      setIsAuthenticated(false)
      localStorage.removeItem("authToken")
      localStorage.removeItem("user")
    }
  }

  // Context value
  const contextValue = {
    // Users list
    users,
    loading,
    error,
    fetchUsers,
    addUser,
    updateUser,
    deleteUser,
    clearUsers,

    // Current user
    currentUser,
    updateCurrentUser,
    setCurrentUser,

    // Auth operations
    login,
    logout,
    isAuthenticated,
  }

  return <UsersContext.Provider value={contextValue}>{children}</UsersContext.Provider>
}

// Custom hooks to use the context
export function useUsers() {
  const context = useContext(UsersContext)
  if (context === undefined) {
    throw new Error("useUsers must be used within a UserProvider")
  }

  // Return only the users-related properties
  const { users, loading, error, fetchUsers, addUser, updateUser, deleteUser, clearUsers } = context
  return { users, loading, error, fetchUsers, addUser, updateUser, deleteUser, clearUsers }
}

export function useUser() {
  const context = useContext(UsersContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }

  // Return only the current user-related properties
  const { currentUser, updateCurrentUser, setCurrentUser } = context
  return {
    userData: currentUser,
    updateUserData: updateCurrentUser,
    setCurrentUser,
  }
}

export function useAuth() {
  const context = useContext(UsersContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within a UserProvider")
  }

  // Return only the auth-related properties
  const { login, logout, isAuthenticated, currentUser } = context
  return { login, logout, isAuthenticated, user: currentUser }
}

// Combined hook if you need access to everything
export function useUserContext() {
  const context = useContext(UsersContext)
  if (context === undefined) {
    throw new Error("useUserContext must be used within a UserProvider")
  }
  return context
}




///////////////////////////////////
// /* eslint-disable react/prop-types */
// 'use client';

// import { createContext, useContext, useState, useEffect } from 'react';
// import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '@/utils/localStorage';

// // Create the context
// export const UsersContext = createContext(undefined);

// export function UsersProvider({ children }) {
//   // Users list state
//   const [users, setUsers] = useState(() => loadFromLocalStorage('users', []));

//   // Current user state
//   const [currentUser, setCurrentUser] = useState(() =>
//     loadFromLocalStorage('currentUser', {
//       profileImage: null,
//       firstName: '',
//       lastName: '',
//       email: '',
//       address: '',
//       role: 'user',
//     })
//   );

//   // Save users to localStorage when they change
//   useEffect(() => {
//     saveToLocalStorage('users', users);
//   }, [users]);

//   // Save current user to localStorage when it changes
//   useEffect(() => {
//     saveToLocalStorage('currentUser', currentUser);
//   }, [currentUser]);

//   // Users list operations
//   const clearUsers = () => {
//     setUsers([]);
//     removeFromLocalStorage('users');
//   };

//   const addUser = (user) => {
//     const newUser = { ...user, id: Date.now() };
//     setUsers((prev) => [...prev, newUser]);
//     return newUser;
//   };

//   const updateUser = (id, updatedUser) => {
//     setUsers((prev) => prev.map((user) => (user.id === id ? { ...user, ...updatedUser } : user)));
//   };

//   const deleteUser = (id) => {
//     setUsers((prev) => prev.filter((user) => user.id !== id));
//   };

//   // Current user operations
//   const updateCurrentUser = (newData) => {
//     setCurrentUser((prev) => {
//       const updated = { ...prev, ...newData };
//       return updated;
//     });
//   };

//   // Set the entire current user (useful for login/logout)
//   const setFullCurrentUser = (user) => {
//     setCurrentUser(user);
//   };

//   const contextValue = {
//     // Users list
//     users,
//     addUser,
//     updateUser,
//     deleteUser,
//     clearUsers,

//     // Current user
//     currentUser,
//     updateCurrentUser,
//     setCurrentUser: setFullCurrentUser,
//   };

//   return <UsersContext.Provider value={contextValue}>{children}</UsersContext.Provider>;
// }

// // Custom hooks to use the context
// export function useUsers() {
//   const context = useContext(UsersContext);
//   if (context === undefined) {
//     throw new Error('useUsers must be used within a UserProvider');
//   }

//   // Return only the users-related properties
//   const { users, addUser, updateUser, deleteUser, clearUsers } = context;
//   return { users, addUser, updateUser, deleteUser, clearUsers };
// }

// export function useUser() {
//   const context = useContext(UsersContext);
//   if (context === undefined) {
//     throw new Error('useUser must be used within a UserProvider');
//   }

//   // Return only the current user-related properties
//   const { currentUser, updateCurrentUser, setCurrentUser } = context;
//   return {
//     userData: currentUser,
//     updateUserData: updateCurrentUser,
//     setCurrentUser,
//   };
// }

// // Combined hook if you need access to everything
// export function useUserContext() {
//   const context = useContext(UsersContext);
//   if (context === undefined) {
//     throw new Error('useUserContext must be used within a UserProvider');
//   }
//   return context;
// }

// ////////////////////////////////////////////////
// // import React, { createContext, useContext, useState, useEffect } from 'react';
// // import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';

// // const UsersContext = createContext();

// // export const useUsers = () => {
// //   const context = useContext(UsersContext);
// //   if (!context) {
// //     throw new Error('useUsers must be used within a UsersProvider');
// //   }
// //   return context;
// // };

// // export const UsersProvider = ({ children }) => {
// //   const [users, setUsers] = useState(() => loadFromLocalStorage('users', []));

// //   useEffect(() => {
// //     saveToLocalStorage('users', users);
// //   }, [users]);

// //   const clearUsers = () => {
// //     setUsers([]);
// //     removeFromLocalStorage('users');
// //   };

// //   const addUser = (user) => {
// //     setUsers(prev => [...prev, { ...user, id: Date.now() }]);
// //   };

// //   const updateUser = (id, updatedUser) => {
// //     setUsers(prev => prev.map(user =>
// //       user.id === id ? { ...user, ...updatedUser } : user
// //     ));
// //   };

// //   const deleteUser = (id) => {
// //     setUsers(prev => prev.filter(user => user.id !== id));
// //   };

// //   return (
// //     <UsersContext.Provider value={{
// //       users,
// //       setUsers,
// //       addUser,
// //       updateUser,
// //       deleteUser,
// //       clearUsers
// //     }}>
// //       {children}
// //     </UsersContext.Provider>
// //   );
// // };
