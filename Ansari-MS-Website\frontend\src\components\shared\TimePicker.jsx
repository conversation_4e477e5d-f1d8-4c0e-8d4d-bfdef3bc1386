import React from 'react';
import { Clock } from 'lucide-react';

const TimePicker = React.forwardRef(
  (
    {
      label,
      value,
      onChange,
      error,
      helperText,
      placeholder = 'Select time',
      className = '',
      disabled = false,
      required = false,
      ...props
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const [selectedTime, setSelectedTime] = React.useState(value || '');
    const timePickerRef = React.useRef(null);

    React.useEffect(() => {
      const handleClickOutside = (event) => {
        if (timePickerRef.current && !timePickerRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleTimeSelect = (time) => {
      setSelectedTime(time);
      if (onChange) {
        onChange(time);
      }
      setIsOpen(false);
    };

    const generateTimeOptions = () => {
      const times = [];
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          times.push(time);
        }
      }
      return times;
    };

    const baseStyles =
      'w-full px-3 py-2 rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed';
    const borderStyles = error ? 'border-red-500 dark:border-red-500' : 'border-gray-300 dark:border-gray-600';

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          <input
            ref={ref}
            type="text"
            value={selectedTime}
            placeholder={placeholder}
            onClick={() => !disabled && setIsOpen(true)}
            readOnly
            className={`${baseStyles} ${borderStyles} ${className}`}
            disabled={disabled}
            required={required}
            {...props}
          />
          <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        </div>
        {isOpen && (
          <div
            ref={timePickerRef}
            className="absolute z-50 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 max-h-60 overflow-y-auto"
          >
            {generateTimeOptions().map((time) => (
              <button
                key={time}
                className={`w-full px-3 py-2 text-left text-sm rounded-md ${
                  selectedTime === time
                    ? 'bg-[#FF6B00] text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={() => handleTimeSelect(time)}
              >
                {time}
              </button>
            ))}
          </div>
        )}
        {(error || helperText) && (
          <p className={`mt-1 text-sm ${error ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

TimePicker.displayName = 'TimePicker';

export default TimePicker;
