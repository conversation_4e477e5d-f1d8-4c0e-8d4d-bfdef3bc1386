import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Save, RotateCcw } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';

const AddBuybackPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { buyBackFromFarm, allocations, fetchAllocations } = useChicken();
  const { language, translations } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    allocationId: searchParams.get('allocationId') || '',
    farmId: '',
    buybackDate: new Date().toISOString().split('T')[0],
    quantity: '',
    pricePerChicken: '',
    totalPrice: '',
    daysCompleted: '',
  });

  const [selectedAllocation, setSelectedAllocation] = useState(null);

  useEffect(() => {
    fetchAllocations();
  }, []);

  useEffect(() => {
    if (formData.allocationId && allocations.length > 0) {
      const allocation = allocations.find(a => a.id.toString() === formData.allocationId);
      if (allocation) {
        setSelectedAllocation(allocation);
        const quantity = allocation.remainingQuantity || allocation.quantity;
        const pricePerChicken = allocation.pricePerChicken;
        const totalPrice = (quantity * pricePerChicken).toFixed(2);

        setFormData(prev => ({
          ...prev,
          farmId: allocation.farmId,
          quantity: quantity.toString(),
          pricePerChicken: pricePerChicken.toString(),
          totalPrice: totalPrice,
          daysCompleted: allocation.daysElapsed?.toString() || '0',
        }));
      }
    }
  }, [formData.allocationId, allocations]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newData = { ...prev, [name]: value };
      
      // Auto-calculate total price when quantity or price per chicken changes
      if (name === 'quantity' || name === 'pricePerChicken') {
        const quantity = parseFloat(name === 'quantity' ? value : newData.quantity) || 0;
        const pricePerChicken = parseFloat(name === 'pricePerChicken' ? value : newData.pricePerChicken) || 0;
        const calculatedTotal = quantity * pricePerChicken;
        newData.totalPrice = calculatedTotal.toFixed(2);
      }
      
      // Auto-calculate days completed when buyback date changes
      if (name === 'buybackDate' && selectedAllocation) {
        const allocationDate = new Date(selectedAllocation.allocationDate);
        const buybackDate = new Date(value);
        const daysDiff = Math.floor((buybackDate - allocationDate) / (1000 * 60 * 60 * 24));
        newData.daysCompleted = Math.max(0, daysDiff).toString();
      }
      
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.allocationId) throw new Error(t('allocation_required') || 'Please select an allocation');
    if (!formData.buybackDate) throw new Error(t('buyback_date_required') || 'Buyback date is required');
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('quantity_required') || 'Valid quantity is required');
    if (!formData.pricePerChicken || formData.pricePerChicken <= 0) throw new Error(t('price_required') || 'Valid price per chicken is required');
    if (!formData.totalPrice || isNaN(parseFloat(formData.totalPrice)) || parseFloat(formData.totalPrice) <= 0) throw new Error(t('total_price_required') || 'Valid total price is required');
    
    if (selectedAllocation && parseInt(formData.quantity) > (selectedAllocation.remainingQuantity || selectedAllocation.quantity)) {
      const availableQuantity = selectedAllocation.remainingQuantity || selectedAllocation.quantity;
      throw new Error(t('quantity_exceeds_remaining') || `Quantity cannot exceed remaining chickens (${availableQuantity})`);
    }
    
    const daysCompleted = parseInt(formData.daysCompleted);
    if (daysCompleted < 45) {
      throw new Error(t('minimum_45_days_required') || 'Minimum 45 days required before buyback');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      const buybackData = {
        allocationId: parseInt(formData.allocationId),
        farmId: parseInt(formData.farmId),
        buybackDate: formData.buybackDate,
        quantity: parseInt(formData.quantity),
        pricePerChicken: parseFloat(formData.pricePerChicken),
        totalPrice: parseFloat(formData.totalPrice),
        daysCompleted: parseInt(formData.daysCompleted),
      };

      await buyBackFromFarm(buybackData);
      
      setFeedback({
        type: 'success',
        message: t('buyback_created_successfully') || 'Chickens bought back from farm successfully',
      });

      setTimeout(() => {
        navigate('/admin/chickens/buybacks');
      }, 1500);
    } catch (error) {
      console.error('Error creating buyback:', error);
      
      let errorMessage = error.message || t('create_buyback_error') || 'Failed to buy back chickens from farm';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const readyAllocations = allocations.filter(a =>
    a.daysElapsed >= 45 &&
    (a.remainingQuantity || a.quantity) > 0
  );

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <Button
          variant="secondary"
          onClick={() => navigate('/admin/chickens/buybacks')}
          className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back_to_buybacks') || 'Back to Buybacks'}
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('buyback_from_farm') || 'Buyback from Farm'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('buyback_chickens_from_farm_description') || 'Buy back chickens from farm after 45 days'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            {t('buyback_information') || 'Buyback Information'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Allocation Selection */}
              <div className="md:col-span-2">
                <label htmlFor="allocationId" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('select_allocation') || 'Select Allocation (Ready for Buyback)'}
                </label>
                <select
                  id="allocationId"
                  name="allocationId"
                  value={formData.allocationId}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                >
                  <option value="">{t('choose_allocation') || 'Choose an allocation...'}</option>
                  {readyAllocations.map((allocation) => (
                    <option key={allocation.id} value={allocation.id}>
                      {allocation.farmName} - {allocation.remainingQuantity || allocation.quantity} available
                      ({allocation.daysElapsed} days completed)
                    </option>
                  ))}
                </select>
                {readyAllocations.length === 0 && (
                  <p className="mt-2 text-sm text-yellow-600">
                    {t('no_ready_allocations') || 'No allocations available for buyback (45+ days required and remaining quantity > 0)'}
                  </p>
                )}
                {selectedAllocation && (
                  <div className="mt-2 p-3 bg-blue-50 rounded-md">
                    <div className="text-sm text-blue-700">
                      <div><strong>Farm:</strong> {selectedAllocation.farmName}</div>
                      <div><strong>Owner:</strong> {selectedAllocation.farmOwner}</div>
                      <div><strong>Allocated:</strong> {new Date(selectedAllocation.allocationDate).toLocaleDateString()}</div>
                      <div><strong>Days Elapsed:</strong> {selectedAllocation.daysElapsed} days</div>
                      <div><strong>Total Allocated:</strong> {selectedAllocation.quantity} chickens</div>
                      <div><strong>Already Bought Back:</strong> {selectedAllocation.boughtBackQuantity || 0} chickens</div>
                      <div><strong>Available for Buyback:</strong> {selectedAllocation.remainingQuantity || selectedAllocation.quantity} chickens</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Buyback Date */}
              <div>
                <label htmlFor="buybackDate" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('buyback_date') || 'Buyback Date'}
                </label>
                <input
                  type="date"
                  id="buybackDate"
                  name="buybackDate"
                  value={formData.buybackDate}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                />
              </div>

              {/* Days Completed (Auto-calculated) */}
              <div>
                <label htmlFor="daysCompleted" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('days_completed') || 'Days Completed'}
                </label>
                <input
                  type="number"
                  id="daysCompleted"
                  name="daysCompleted"
                  value={formData.daysCompleted}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-600 dark:text-white dark:border-gray-600"
                />
                <small className="text-gray-500">
                  {parseInt(formData.daysCompleted) >= 45 
                    ? '✅ Ready for buyback' 
                    : `❌ Need ${45 - parseInt(formData.daysCompleted || 0)} more days`
                  }
                </small>
              </div>

              {/* Quantity */}
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('quantity') || 'Quantity (Number of Chickens)'}
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  required
                  min="1"
                  max={selectedAllocation ? (selectedAllocation.remainingQuantity || selectedAllocation.quantity) : ''}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_quantity') || 'Enter number of chickens'}
                />
                {selectedAllocation && (
                  <small className="text-gray-500">
                    Maximum available for buyback: {selectedAllocation.remainingQuantity || selectedAllocation.quantity} chickens
                  </small>
                )}
              </div>

              {/* Price Per Chicken */}
              <div>
                <label htmlFor="pricePerChicken" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('price_per_chicken') || 'Price Per Chicken (AFN)'}
                </label>
                <input
                  type="number"
                  id="pricePerChicken"
                  name="pricePerChicken"
                  value={formData.pricePerChicken}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_price_per_chicken') || 'Enter price per chicken'}
                />
              </div>

              {/* Total Price (Auto-calculated) */}
              <div className="md:col-span-2">
                <label htmlFor="totalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('total_price') || 'Total Price (AFN)'}
                </label>
                <input
                  type="number"
                  id="totalPrice"
                  name="totalPrice"
                  value={formData.totalPrice}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-600 dark:text-white dark:border-gray-600"
                  placeholder={t('auto_calculated') || 'Auto-calculated'}
                />
                <small className="text-gray-500">
                  {t('total_price_auto_calculated') || 'This field is automatically calculated'}
                </small>
              </div>
            </div>

            {/* Buyback Summary */}
            {selectedAllocation && formData.quantity && formData.pricePerChicken && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">
                  {t('buyback_summary') || 'Buyback Summary'}
                </h4>
                <div className="text-sm text-green-700 grid grid-cols-2 gap-4">
                  <div><strong>Farm:</strong> {selectedAllocation.farmName}</div>
                  <div><strong>Days Completed:</strong> {formData.daysCompleted} days</div>
                  <div><strong>Quantity:</strong> {formData.quantity} chickens</div>
                  <div><strong>Total Cost:</strong> {formData.totalPrice} AFN</div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/admin/chickens/buybacks')}
              >
                {t('cancel') || 'Cancel'}
              </Button>
              <Button
                type="submit"
                disabled={loading || parseInt(formData.daysCompleted) < 45}
                className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Save className="h-4 w-4" />
                {loading ? (t('buying_back') || 'Buying Back...') : (t('buyback_from_farm') || 'Buyback from Farm')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddBuybackPage;
