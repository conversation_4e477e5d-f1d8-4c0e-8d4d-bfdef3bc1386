import pool from '../config/db.js';

async function checkTables() {
  try {
    console.log('Checking existing tables...');
    
    // Show all tables
    const [tables] = await pool.query("SHOW TABLES");
    console.log('📋 All tables in database:');
    console.table(tables);
    
    // Check farm table structure
    try {
      const [farmStructure] = await pool.query("DESCRIBE farms");
      console.log('\n🏠 Farm table structure:');
      console.table(farmStructure);
    } catch (error) {
      console.log('❌ Farm table not found, checking for other farm-related tables...');
      
      // Check for other possible farm table names
      const farmTables = tables.filter(table => 
        Object.values(table)[0].toLowerCase().includes('farm')
      );
      console.log('Farm-related tables:', farmTables);
    }
    
    // Check shop table structure
    try {
      const [shopStructure] = await pool.query("DESCRIBE Shop");
      console.log('\n🏪 Shop table structure:');
      console.table(shopStructure);
    } catch (error) {
      console.log('❌ Shop table not found');
    }
    
  } catch (error) {
    console.error('❌ Error checking tables:', error);
  } finally {
    await pool.end();
  }
}

checkTables();
