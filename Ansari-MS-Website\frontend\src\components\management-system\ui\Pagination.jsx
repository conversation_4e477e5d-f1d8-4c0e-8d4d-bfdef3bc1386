/* eslint-disable react/prop-types */
import { cn } from '../lib/utils';
import { Button } from './Button';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

const Pagination = ({ className, ...props }) => {
  return (
    <nav
      role="navigation"
      aria-label="pagination"
      className={cn('mx-auto flex w-full justify-center', className)}
      {...props}
    />
  );
};

const PaginationContent = ({ className, ...props }) => {
  return <ul className={cn('flex flex-row items-center gap-1', className)} {...props} />;
};

const PaginationItem = ({ className, ...props }) => {
  return <li className={cn('', className)} {...props} />;
};

const PaginationLink = ({ className, isActive, size = 'icon', ...props }) => {
  return (
    <Button
      aria-current={isActive ? 'page' : undefined}
      variant={isActive ? 'outline' : 'ghost'}
      size={size}
      className={cn('w-9 h-9', isActive && 'bg-accent pointer-events-none', className)}
      {...props}
    />
  );
};

const PaginationPrevious = ({ className, ...props }) => {
  return (
    <Button variant="ghost" size="icon" className={cn('gap-1 pl-2.5', className)} {...props}>
      <ChevronLeft className="h-4 w-4" />
      <span className="sr-only">Previous page</span>
    </Button>
  );
};

const PaginationNext = ({ className, ...props }) => {
  return (
    <Button variant="ghost" size="icon" className={cn('gap-1 pr-2.5', className)} {...props}>
      <span className="sr-only">Next page</span>
      <ChevronRight className="h-4 w-4" />
    </Button>
  );
};

const PaginationEllipsis = ({ className, ...props }) => {
  return (
    <div aria-hidden className={cn('flex h-9 w-9 items-center justify-center', className)} {...props}>
      <MoreHorizontal className="h-4 w-4" />
      <span className="sr-only">More pages</span>
    </div>
  );
};

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
};
