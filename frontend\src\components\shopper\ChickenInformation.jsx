import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useShopperChicken } from '../../contexts/ShopperChickenContext';
import { Package, Calendar, MapPin, TrendingUp, Eye } from 'lucide-react';

const ChickenInformation = () => {
  const { user } = useAuth();
  const { distributions, loading, fetchUserDistributions } = useShopperChicken();
  const [stats, setStats] = useState({
    totalDistributions: 0,
    totalChickens: 0,
    totalValue: 0,
    recentDistributions: []
  });

  useEffect(() => {
    // Fetch distributions for the user's shop
    if (user?.u_Id) {
      fetchUserDistributions(user.u_Id);
    }
  }, [user]);

  useEffect(() => {
    if (distributions.length > 0) {
      const totalDistributions = distributions.length;
      const totalChickens = distributions.reduce((sum, dist) => sum + (dist.quantity || 0), 0);
      const totalValue = distributions.reduce((sum, dist) => sum + (parseFloat(dist.totalPrice) || 0), 0);
      const recentDistributions = distributions.slice(0, 3); // Get 3 most recent

      setStats({
        totalDistributions,
        totalChickens,
        totalValue,
        recentDistributions
      });
    }
  }, [distributions]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-gradient-to-br from-orange-50 to-yellow-50 p-6 dark:from-gray-800 dark:to-gray-900 dark:border-gray-700 shadow">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 flex items-center">
          <Package className="h-5 w-5 mr-2 text-orange-600" />
          Chicken Inventory
        </h3>
        <button className="text-sm text-orange-600 hover:text-orange-800 flex items-center">
          <Eye className="h-4 w-4 mr-1" />
          View All
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-3 rounded-lg shadow-sm dark:bg-gray-700">
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.totalDistributions}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Deliveries</div>
          </div>
        </div>
        <div className="bg-white p-3 rounded-lg shadow-sm dark:bg-gray-700">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.totalChickens}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Chickens</div>
          </div>
        </div>
        <div className="bg-white p-3 rounded-lg shadow-sm dark:bg-gray-700">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{formatCurrency(stats.totalValue)}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Total Value</div>
          </div>
        </div>
      </div>

      {/* Recent Distributions */}
      <div>
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-300 mb-3">Recent Deliveries</h4>
        {stats.recentDistributions.length > 0 ? (
          <div className="space-y-2">
            {stats.recentDistributions.map((distribution) => (
              <div key={distribution.id} className="bg-white p-3 rounded-lg shadow-sm dark:bg-gray-700 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium dark:text-white">
                      {distribution.quantity} chickens
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(distribution.distributionDate)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-green-600">
                    {formatCurrency(distribution.totalPrice)}
                  </div>
                  {distribution.farmName && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {distribution.farmName}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <Package className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500 dark:text-gray-400">No chicken deliveries yet</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChickenInformation;
