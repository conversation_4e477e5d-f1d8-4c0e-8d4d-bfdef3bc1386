import <PERSON><PERSON> from "joi";
import multer from "multer";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { fileURLToPath } from "url";

import asyncHandler from "../middlewares/asyncHandler.js";
import ArticleModel from "../models/articleModel.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Multer Storage & Filter
const multerStorage = multer.memoryStorage();
const multerFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image")) {
    cb(null, true);
  } else {
    cb(new Error("Not an image! Please upload only images."), false);
  }
};

const upload = multer({ storage: multerStorage, fileFilter: multerFilter });
const uploadUserPhoto = upload.single("A_Image");

// Image Processing Middleware
const resizeUserPhoto = asyncHandler(async (req, res, next) => {
  if (!req.file) return next();

  const dir = path.join(__dirname, ".././public/images/news"); // Corrected path
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const filename = `artical-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}.jpeg`;
  req.file.filename = filename;

  await sharp(req.file.buffer)
    .resize(500, 500)
    .toFormat("jpeg")
    .jpeg({ quality: 90 })
    .toFile(path.join(dir, filename));

  req.body.A_Image = filename;
  next();
});

const articleSchema = Joi.object({
  A_Title: Joi.string().min(5).required(),
  A_Body: Joi.string().min(8).required(),
  A_Image: Joi.string().optional(),
  u_Id: Joi.number().required(),

  Excerpt: Joi.string().min(5).optional(),
  Category: Joi.string()
    .valid(
      "Farm Updates",
      "Poultry Care",
      "Events",
      "Nutrition",
      "Health",
      "Sustainability"
    )
    .required(),
  Status: Joi.string().valid("Published", "Draft").default("Published"),
  FeaturedNews: Joi.boolean().default(false),
});

const ArticleController = {
  create: asyncHandler(async (req, res) => {
    console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
    console.log(req.body);
    console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");

    const { error, value } = articleSchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    if (!value.A_Image) value.A_Image = "default-article-image.jpg";

    const article = await ArticleModel.create(value);
    res.status(201).json({
      success: true,
      message: "Article created successfully",
      article,
    });
  }),

  getAll: asyncHandler(async (req, res) => {
    const articles = await ArticleModel.getAll();
    res.json({
      success: true,
      message: "Articles fetched successfully",
      total: articles.length,
      data: articles,
    });
  }),

  getById: asyncHandler(async (req, res) => {
    const article = await ArticleModel.getById(req.params.id);
    if (!article) {
      return res
        .status(404)
        .json({ success: false, error: "Article not found" });
    }
    res.json({ success: true, data: article });
  }),
  update: asyncHandler(async (req, res) => {
    const { error, value } = articleSchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    const article = await ArticleModel.getById(req.params.id);
    if (!article) {
      return res
        .status(404)
        .json({ success: false, error: "Article not found" });
    }

    const updated = await ArticleModel.update(req.params.id, value);
    res.json({
      success: true,
      message: "Article updated successfully",
      article: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await ArticleModel.delete(req.params.id);
    if (result.affectedRows === 0) {
      return res
        .status(404)
        .json({ success: false, error: "Article not found" });
    }
    res.json({ success: true, message: "Article deleted successfully" });
  }),
};

export default ArticleController;
export { uploadUserPhoto, resizeUserPhoto };
