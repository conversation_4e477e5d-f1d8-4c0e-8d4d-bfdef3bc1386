/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-vars */
'use client';

import { motion } from 'framer-motion';
import { MailCheck, RefreshCcw, Mail } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import axios from 'axios';

const EmailVerification = () => {
  const [resending, setResending] = useState(false);
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const navigate = useNavigate();

  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const handleResend = async () => {
    // Reset errors first
    setEmailError('');
    setMessage('');

    // Validate email input
    if (!email) {
      setEmailError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    setResending(true);

    try {
      const response = await axios.post('http://localhost:5432/api/v1/users/verify-email/send', { email });

      if (response.data.success) {
        setMessage(`Verification email sent to ${email}`);

        localStorage.setItem('userverifyemail', email);
        setTimeout(() => {
          navigate('/verify-code');
        }, 3000); // optional small delay
      } else {
        setMessage(response.data.message || 'Failed to send verification email');
      }
    } catch (error) {
      console.error('API error:', error);
      setMessage(error.response?.data?.message || 'Failed to resend email. Try again later.');
    } finally {
      setResending(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-16">
      <motion.div
        initial={{ opacity: 0, y: 12 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white max-w-md w-full p-8 rounded-xl shadow-lg text-center"
      >
        <MailCheck className="mx-auto h-16 w-16 text-primary mb-4" />
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Verify Your Email</h1>
        <p className="text-gray-600 mb-6">
          Enter your email address below and we&apos;ll send you a verification link to complete your registration.
        </p>

        <div className="mb-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Your email address"
              className="pl-10 w-full py-2 px-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          {emailError && <p className="text-red-500 text-sm mt-1 text-left">{emailError}</p>}
        </div>

        {message && <div className="text-sm text-green-600 font-medium mt-2 mb-4">{message}</div>}

        <button
          onClick={handleResend}
          disabled={resending}
          className={`w-full flex justify-center items-center gap-2 py-2 px-4 text-white bg-primary hover:bg-primary-hover rounded-lg font-medium transition duration-200 ${
            resending ? 'opacity-60 cursor-not-allowed' : ''
          }`}
        >
          {resending ? (
            <>
              <svg className="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24" fill="none">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Sending...
            </>
          ) : (
            <>
              <RefreshCcw className="w-5 h-5" />
              Send Verification Email
            </>
          )}
        </button>

        <div className="mt-6">
          <p className="text-sm text-gray-600">
            Already verified?{' '}
            <Link to="/signin" className="text-primary hover:text-primary-hover font-medium">
              Sign In
            </Link>
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default EmailVerification;

/////////////////////////////////////////////////////////
// "use client"

// import { motion } from "framer-motion"
// import { MailCheck, RefreshCcw } from "lucide-react"
// import { Link, useNavigate } from "react-router-dom"
// import { useState } from "react"

// const EmailVerification = () => {
//   const [resending, setResending] = useState(false)
//   const [message, setMessage] = useState("")
//   const navigate=useNavigate()

//   const handleResend = async () => {
//     setResending(true)
//     setMessage("")

//     try {
//       // Replace with your actual API call
//       // await axios.post('/api/resend-verification', { email: userEmail });
//       // setTimeout(() => {
//       // }, 1500)
//       setTimeout(() => {
//         setMessage("Verification email resent successfully.")
//         setResending(false)
//         navigate("/verify-code")
//       }, 2000)

//     } catch (err) {
//       setMessage("Failed to resend email. Try again later.")
//       setResending(false)
//     }
//   }

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 px-4 py-16">
//       <motion.div
//         initial={{ opacity: 0, y: 12 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ duration: 0.4 }}
//         className="bg-white max-w-md w-full p-8 rounded-xl shadow-lg text-center"
//       >
//         <MailCheck className="mx-auto h-16 w-16 text-primary mb-4" />
//         <h1 className="text-2xl font-bold text-gray-800 mb-2">Verify Your Email</h1>
//         <p className="text-gray-600 mb-4">
//           We&apos;ve sent a verification link to your email address. Please check your inbox and follow the instructions to complete your registration.
//         </p>

//         {message && (
//           <div className="text-sm text-green-600 font-medium mt-2 mb-4">
//             {message}
//           </div>
//         )}

//         <button
//           onClick={handleResend}
//           disabled={resending}
//           className={`w-full flex justify-center items-center gap-2 py-2 px-4 text-white bg-primary hover:bg-primary-hover rounded-lg font-medium transition duration-200 ${
//             resending ? "opacity-60 cursor-not-allowed" : ""
//           }`}
//         >
//           {resending ? (
//             <>
//               <svg
//                 className="animate-spin h-5 w-5 text-white"
//                 viewBox="0 0 24 24"
//                 fill="none"
//               >
//                 <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
//                 <path
//                   className="opacity-75"
//                   fill="currentColor"
//                   d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                 />
//               </svg>
//               Sending...
//             </>
//           ) : (
//             <>
//               <RefreshCcw className="w-5 h-5" />
//               Resend Verification Email
//             </>
//           )}
//         </button>

//         <div className="mt-6">
//           <p className="text-sm text-gray-600">
//             Already verified?{" "}
//             <Link to="/signin" className="text-primary hover:text-primary-hover font-medium">
//               Sign In
//             </Link>
//           </p>
//         </div>
//       </motion.div>
//     </div>
//   )
// }

// export default EmailVerification
