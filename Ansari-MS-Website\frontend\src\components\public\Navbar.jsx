import { useState, useEffect, useRef } from 'react';
import { NavLink, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Menu, X, Home, Newspaper, Phone, HelpCircle, Globe, ShoppingBag } from 'lucide-react';
import Logo from '../logo';
import Button from '../shared/Button';

const Navbar = () => {
  const { t, i18n } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangOpen, setIsLangOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const langDropdownRef = useRef(null);

  useEffect(() => {
    const dir = i18n.language === 'ps' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', dir);
    document.documentElement.setAttribute('lang', i18n.language);
  }, [i18n.language]);

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 50);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    document.body.style.overflow = isMenuOpen ? 'hidden' : 'auto';
  }, [isMenuOpen]);

  // Close language dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (langDropdownRef.current && !langDropdownRef.current.contains(event.target)) {
        setIsLangOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLangSelect = (lang) => {
    i18n.changeLanguage(lang);
    setIsLangOpen(false);
    setIsMenuOpen(false);
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'bg-[#2C3E50]/95 backdrop-blur-sm shadow-lg py-3' : 'bg-[#2C3E50] py-3'
      }`}
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between px-4">
        {/* Logo */}
        <Link to="/" className="z-20 flex-shrink-0">
          <Logo />
        </Link>

        {/* Desktop Nav */}
        <div className="hidden md:flex flex-1 justify-center items-center gap-1 text-white text-base font-bold">
          {[
            { to: '/', icon: <Home size={20} />, label: t('navigation.home') },
            {
              to: '/services',
              icon: <ShoppingBag size={20} />,
              label: t('navigation.services'),
            },
            {
              to: '/news',
              icon: <Newspaper size={20} />,
              label: t('navigation.news'),
            },
            {
              to: '/about',
              icon: <HelpCircle size={20} />,
              label: t('navigation.about'),
            },
            {
              to: '/contact',
              icon: <Phone size={20} />,
              label: t('navigation.contact'),
            },
          ].map(({ to, icon, label }) => (
            <NavLink
              key={to}
              to={to}
              className={({ isActive }) => `
                flex items-center gap-1 text-base rtl:text-xl px-2 font-extrabold py-1 transition-colors duration-200
                ${isActive ? 'text-primary' : 'text-white hover:text-primary'}
              `}
            >
              <span className="md:hidden">{icon}</span>
              {label}
            </NavLink>
          ))}
        </div>

        {/* Desktop Lang + Auth */}
        <div className="hidden md:flex items-center gap-2 flex-shrink-0">
          <div className="relative" ref={langDropdownRef}>
            <button
              onClick={() => setIsLangOpen(!isLangOpen)}
              className="flex items-center gap-2 px-4 py-2 text-white bg-primary hover:bg-primary/90 rounded-sm transition-all duration-300 shadow-lg hover:shadow-xl border border-white/20 font-sans"
              title="Language / ژبه"
            >
              <Globe size={20} className="text-white" />
              <span className="text-sm font-medium">
                {i18n.language === 'en' ? 'EN' : 'PS'}
              </span>
              <svg
                className={`w-4 h-4 transition-transform duration-300 ${isLangOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {isLangOpen && (
              <div className="absolute right-0 mt-2 min-w-[180px] z-50 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
                {['en', 'ps'].map((lang) => (
                  <button
                    key={lang}
                    onClick={() => handleLangSelect(lang)}
                    className={`block w-full text-left px-4 py-3 transition-all duration-200 ${
                      lang === i18n.language
                        ? 'bg-primary text-white'
                        : 'text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Globe size={18} className={lang === i18n.language ? 'text-white' : 'text-primary'} />
                        <span className="font-semibold text-sm">
                          {lang === 'en' ? t('languages.english') : t('languages.pashto')}
                        </span>
                      </div>
                      {lang === i18n.language && (
                        <span className="text-white text-sm font-bold">✓</span>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
          <Button
            variant="primary"
            size="md"
            className="rounded-sm font-sans"
            onClick={() => (window.location.href = '/signin')}
          >
            {t('navigation.signin')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="py-2 rounded-sm"
            onClick={() => (window.location.href = '/signup')}
          >
            {t('navigation.signup')}
          </Button>
        </div>

        {/* Mobile Menu Toggle */}
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="md:hidden p-2 rounded-full text-white hover:bg-white/10 transition-colors z-20"
          aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      <div
        className={`md:hidden fixed top-16 inset-0 z-40 bg-[#2C3E50] transform transition-transform duration-300 ${
          isMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col items-start gap-4 p-6 bg-muted text-primary">
          {[
            { to: '/', icon: <Home size={20} />, label: t('navigation.home') },
            {
              to: '/services',
              icon: <ShoppingBag size={20} />,
              label: t('navigation.services'),
            },
            {
              to: '/news',
              icon: <Newspaper size={20} />,
              label: t('navigation.news'),
            },
            {
              to: '/about',
              icon: <HelpCircle size={20} />,
              label: t('navigation.about'),
            },
            {
              to: '/contact',
              icon: <Phone size={20} />,
              label: t('navigation.contact'),
            },
          ].map(({ to, icon, label }) => (
            <NavLink
              key={to}
              to={to}
              onClick={() => setIsMenuOpen(false)}
              className={({ isActive }) => `
                flex items-center gap-2 w-full text-lg font-bold
                ${isActive ? 'text-primary' : 'text-textSecondary hover:text-primary'}
              `}
            >
              {icon}
              {label}
            </NavLink>
          ))}

          {/* Mobile Lang */}
          <div className="mt-6 w-full">
            <div className="flex items-center gap-4 text-white font-bold mb-6 text-xl">
              <Globe size={28} />
              {t('navigation.language')}
            </div>
            <div className="grid grid-cols-1 gap-4">
              {['en', 'ps'].map((lang) => (
                <button
                  key={lang}
                  onClick={() => handleLangSelect(lang)}
                  className={`flex items-center justify-between gap-4 px-8 py-6 rounded-2xl font-bold text-xl transition-all duration-200 ${
                    lang === i18n.language
                      ? 'bg-primary text-white shadow-xl transform scale-105'
                      : 'bg-white/10 text-white hover:bg-white/20 border-2 border-white/20'
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <Globe size={28} className={lang === i18n.language ? 'text-white' : 'text-primary'} />
                    <span className="text-xl">{lang === 'en' ? t('languages.english') : t('languages.pashto')}</span>
                  </div>
                  {lang === i18n.language && <span className="text-2xl">✓</span>}
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Auth */}
          <div className="flex flex-col w-full gap-2 mt-4">
            <Button
              variant="primary"
              size="md"
              onClick={() => {
                setIsMenuOpen(false);
                window.location.href = '/signin';
              }}
            >
              {t('navigation.signin')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsMenuOpen(false);
                window.location.href = '/signup';
              }}
            >
              {t('navigation.signup')}
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
