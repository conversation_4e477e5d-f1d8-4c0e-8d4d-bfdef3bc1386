import pool from '../config/db.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createChickenTables() {
  try {
    console.log('Creating chicken management tables...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../migrations/create_chicken_tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        await pool.query(statement);
      }
    }
    
    console.log('✅ Chicken management tables created successfully!');
    
    // Show table structures
    console.log('\n📋 Table structures created:');
    
    const tables = ['ChickenPurchases', 'ChickenFarmAllocations', 'ChickenBuybacks', 'ChickenShopDistributions'];
    
    for (const table of tables) {
      try {
        const [structure] = await pool.query(`DESCRIBE ${table}`);
        console.log(`\n${table}:`);
        console.table(structure);
      } catch (error) {
        console.log(`❌ Error describing ${table}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error creating chicken tables:', error);
  } finally {
    await pool.end();
  }
}

createChickenTables();
