import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, MoreVertical, Trash, Calendar, DollarSign, Package } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import Link from '../../components/feed-components/Link';

const DistributionsPage = () => {
  const navigate = useNavigate();
  const { distributions, fetchDistributions, deleteDistribution, loading } = useChicken();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    console.log('🔍 DistributionsPage: Component mounted, fetching distributions...');
    fetchDistributions();
  }, []);

  useEffect(() => {
    console.log('📊 DistributionsPage: Distributions data updated:', distributions);
    console.log('📊 DistributionsPage: Distributions length:', distributions?.length);
  }, [distributions]);

  const filteredDistributions = (distributions || []).filter((distribution) =>
    (distribution.shopName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (distribution.shopOwner || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (distribution.farmName || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id) => {
    if (window.confirm(t('confirm_delete_distribution') || 'Are you sure you want to delete this distribution?')) {
      try {
        await deleteDistribution(id);
      } catch (error) {
        console.error('Error deleting distribution:', error);
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    const numericAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  const getStatusBadge = (distribution) => {
    if (distribution.status === 'sold') {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Sold</span>;
    } else {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Distributed</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_distributions') || 'Loading distributions...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('shop_distributions') || 'Shop Distributions'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_shop_distributions_description') || 'Manage chickens distributed to shops for sale'}
          </p>
        </div>
        <div className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Link 
            to="/admin/chickens" 
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('back_to_dashboard') || 'Back to Dashboard'}
          </Link>
          <Link 
            to="/admin/chickens/distributions/add" 
            className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
            <span>{t('distribute_to_shop') || 'Distribute to Shop'}</span>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('search_distributions') || 'Search distributions by shop name, owner, or farm...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-500/5 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_distributions') || 'Total Distributions'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredDistributions.length}
                </h3>
              </div>
              <div className="p-3 bg-purple-500/10 rounded-full">
                <Calendar className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_chickens') || 'Total Chickens'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredDistributions.reduce((sum, distribution) => sum + (distribution.quantity || 0), 0)}
                </h3>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Package className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_value') || 'Total Value'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(filteredDistributions.reduce((sum, distribution) => {
                    const totalPrice = parseFloat(distribution.totalPrice) || 0;
                    return sum + totalPrice;
                  }, 0))}
                </h3>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distributions Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('distributions_list') || 'Distributions List'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('distribution_date') || 'Distribution Date'}</TableHead>
                  <TableHead>{t('shop') || 'Shop'}</TableHead>
                  <TableHead>{t('from_farm') || 'From Farm'}</TableHead>
                  <TableHead>{t('quantity') || 'Quantity'}</TableHead>
                  <TableHead>{t('price_per_chicken') || 'Price/Chicken'}</TableHead>
                  <TableHead>{t('total_price') || 'Total Price'}</TableHead>
                  <TableHead>{t('status') || 'Status'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDistributions.length > 0 ? (
                  filteredDistributions.map((distribution) => (
                    <TableRow key={distribution.id}>
                      <TableCell>{formatDate(distribution.distributionDate)}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{distribution.shopName}</div>
                          <div className="text-sm text-gray-500">{distribution.shopOwner}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{distribution.farmName}</div>
                          <div className="text-gray-500">
                            Buyback: {formatDate(distribution.buybackDate)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{distribution.quantity}</TableCell>
                      <TableCell>{formatCurrency(distribution.pricePerChicken)}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(distribution.totalPrice)}</TableCell>
                      <TableCell>{getStatusBadge(distribution)}</TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleDelete(distribution.id)}>
                              <Trash className={`h-4 w-4 text-red-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      {t('no_distributions_found') || 'No distributions found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DistributionsPage;
