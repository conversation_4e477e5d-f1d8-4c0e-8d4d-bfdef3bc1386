/* eslint-disable react/prop-types */
'use client';

import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Home, LogOut, User, Sun, Moon,Bell,Settings } from 'lucide-react';
import { FaSnapchat } from 'react-icons/fa';

import { useAuth } from '../contexts/AuthContext';
import {
  SidebarProvider,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarGroup,
  SidebarGroupContent,
} from '../components/management-system/ui/sidebar';

const classNames = (...classes) => classes.filter(Boolean).join(' ');

const ShopperLayout = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('theme') === 'dark' ||
        window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });

  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  useEffect(() => {
    const root = document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      root.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [isDarkMode]);

  const toggleDarkMode = () => setIsDarkMode(!isDarkMode);

  const handleLogout = () => {
    logout();
    navigate('/signin');
  };

  const navItems = [
    { name: 'Dashboard', icon: Home, path: '/shopper' },
    { name: 'Chat', icon: FaSnapchat, path: '/shopper/chat' },
  ];

  const isActive = (path) => location.pathname === path;

  
  const UserAvatar = ({ size = 8, className = '' }) => (
    <div
      className={classNames(
        `rounded-full bg-primary/10 flex items-center justify-center overflow-hidden`,
        size === 10 ? 'h-10 w-10' : `h-${size} w-${size}`,
        className
      )}
    >
      {user?.image ? (
        <img
          src={`http://localhost:5432/public/images/users/${user.image}`}
          alt={`${user.firstName || 'Shopper'} ${user.lastName || 'User'}`}
          className="h-full w-full object-cover"
          loading="lazy"
        />
      ) : (
        <User className={`text-primary ${size === 10 ? 'h-6 w-6' : 'h-5 w-5'}`} />
      )}
    </div>
  );

  return (
    <SidebarProvider defaultOpen>
      <div className="flex bg-muted/30 dark:bg-gray-900 text-textprimary dark:text-gray-100 min-h-screen transition-colors">
        {/* Desktop Sidebar */}
        <aside className="hidden md:flex flex-col w-64 bg-muted dark:bg-gray-800 border-r border-border dark:border-gray-700 p-4 gap-10">
          {/* User Info */}
          <div className="flex items-center gap-3 px-2">
            <UserAvatar />
            <span className="text-primary dark:text-white font-medium text-sm truncate">
              {user?.firstName || 'Shopper'} {user?.lastName || 'User'}
            </span>
          </div>

          {/* Title */}
          <div className="border-y border-gray-500 my-2 py-2 px-2">
            <h3 className="text-lg font-bold">Shopper Dashboard</h3>
          </div>

          {/* Navigation */}
          <SidebarContent className="flex-1 px-2 overflow-auto">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="flex flex-col gap-2">
                  {navItems.map(({ name, icon: Icon, path }) => (
                    <Link
                      key={name}
                      to={path}
                      className={classNames(
                        'flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors',
                        isActive(path)
                          ? 'bg-primary text-primary-foreground'
                          : 'text-textprimary dark:text-gray-300 hover:bg-secondary hover:text-secondary-foreground'
                      )}
                    >
                      <Icon className="h-5 w-5" aria-hidden="true" />
                      <span>{name}</span>
                    </Link>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          {/* Footer */}
          <SidebarFooter className="px-2 py-4 border-t border-border dark:border-gray-700">
            <div className="flex items-center gap-3">
              <UserAvatar size={10} />
              <div className="flex-1 min-w-0 overflow-hidden">
                <p className="text-sm font-medium dark:text-muted truncate">
                  {user?.firstName || 'Shopper'} {user?.lastName || 'User'}
                </p>
                <p className="text-xs text-muted-foreground dark:text-muted truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
              <button
                onClick={toggleDarkMode}
                className="p-2 rounded-md hover:bg-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
                title="Toggle dark mode"
              >
                {isDarkMode ? <Sun className="h-5 w-5 text-yellow-400" /> : <Moon className="h-5 w-5" />}
              </button>
              <button
                onClick={handleLogout}
                title="Logout"
                aria-label="Logout"
                className="p-2 rounded-md hover:bg-muted dark:text-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </SidebarFooter>
        </aside>

        {/* Main content */}
        <div className="flex flex-col flex-1 w-full overflow-hidden">
          <header className="flex items-center justify-between px-4 md:px-6 h-16 bg-muted dark:bg-secondary border-b border-border z-30">
        <div className="md:hidden">
          <button
            onClick={() => setIsMobileMenuOpen((v) => !v)}
            aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
            className="p-2 rounded-md hover:bg-muted dark:bg-secondary focus:outline-none"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        <div className="flex-1" />
        <div className="flex items-center space-x-4 ml-4">
          <button
            onClick={toggleDarkMode}
            aria-label="Toggle Dark Mode"
            className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            {isDarkMode ? <Sun className="h-6 w-6 text-yellow-400" /> : <Moon className="h-6 w-6 text-gray-600" />}
          </button>
          <button
            onClick={logout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
          >
            Logout
          </button>
        </div>
      </header>
          {/* Mobile Sidebar */}
          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.nav
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <motion.div
                  className="fixed inset-y-0 left-0 w-3/4 max-w-xs bg-background dark:bg-gray-800 shadow-xl p-6 overflow-y-auto"
                  initial={{ x: '-100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '-100%' }}
                  transition={{ type: 'spring', damping: 20 }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex items-center mb-6 gap-4">
                    <UserAvatar size={10} />
                    <div>
                      <p className="text-sm font-medium truncate">
                        {user?.firstName || 'Shopper'} {user?.lastName || 'User'}
                      </p>
                      <p className="text-xs text-muted-foreground dark:text-muted truncate">
                        {user?.email || '<EMAIL>'}
                      </p>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="ml-auto p-2 rounded-md hover:bg-muted dark:text-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
                    >
                      <LogOut className="h-5 w-5" />
                    </button>
                  </div>

                  <nav className="flex flex-col gap-2">
                    {navItems.map(({ name, icon: Icon, path }) => (
                      <Link
                        key={name}
                        to={path}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={classNames(
                          'flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors',
                          isActive(path)
                            ? 'bg-primary text-primary-foreground'
                            : 'text-textprimary dark:text-gray-300 hover:bg-secondary hover:text-secondary-foreground'
                        )}
                      >
                        <Icon className="h-5 w-5" aria-hidden="true" />
                        <span>{name}</span>
                      </Link>
                    ))}
                  </nav>
                </motion.div>
              </motion.nav>
            )}
          </AnimatePresence>

          {/* Main content */}
          <main className="flex-1 overflow-auto px-6 py-4">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default ShopperLayout;



////////////////////////////////
// /* eslint-disable no-unused-vars */
// /* eslint-disable react/prop-types */
// 'use client';

// import { useState, useEffect } from 'react';
// import { Link, useLocation, useNavigate } from 'react-router-dom';
// import { motion } from 'framer-motion';
// import {
//   Menu,
//   X,
//   Home,
//   ShoppingCart,
//   ShoppingBag,
//   Heart,
//   Clock,
//   CreditCard,
//   Settings,
//   Bell,
//   LogOut,
//   User,
//   HelpCircle,
//   MessageSquare,
//   Truck,
//   Store,
// } from 'lucide-react';
// import { useAuth } from '../contexts/AuthContext';
// import {
//   SidebarProvider,
//   Sidebar,
//   SidebarContent,
//   SidebarHeader,
//   SidebarFooter,
//   SidebarMenu,
//   SidebarMenuItem,
//   SidebarMenuButton,
//   SidebarSeparator,
//   SidebarGroup,
//   SidebarGroupLabel,
//   SidebarGroupContent,
// } from '../components/management-system/ui/sidebar';

// const ShopperLayout = ({ children }) => {
//   const [isSidebarOpen, setIsSidebarOpen] = useState(true);
//   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
//   const { user, logout } = useAuth();
//   const location = useLocation();
//   const navigate = useNavigate();

//   // Close mobile menu on route change
//   useEffect(() => {
//     setIsMobileMenuOpen(false);
//   }, [location]);

//   const handleLogout = () => {
//     logout();
//     navigate('/signin');
//   };

//   const toggleSidebar = () => {
//     setIsSidebarOpen(!isSidebarOpen);
//   };

//   const toggleMobileMenu = () => {
//     setIsMobileMenuOpen(!isMobileMenuOpen);
//   };

//   // Navigation items specific to shoppers
//   const navItems = [
//     { name: 'Dashboard', icon: Home, path: '/shopper' },
//     { name: 'Browse Products', icon: Store, path: '/shopper/products' },
//     { name: 'My Cart', icon: ShoppingCart, path: '/shopper/cart' },
//     { name: 'Orders', icon: ShoppingBag, path: '/shopper/orders' },
//     { name: 'Wishlist', icon: Heart, path: '/shopper/wishlist' },
//     { name: 'Order History', icon: Clock, path: '/shopper/history' },
//     { name: 'Track Orders', icon: Truck, path: '/shopper/track' },
//     { name: 'Payment Methods', icon: CreditCard, path: '/shopper/payment' },
//     { name: 'Messages', icon: MessageSquare, path: '/shopper/messages' },
//   ];

//   const isActive = (path) => {
//     return location.pathname === path || location.pathname.startsWith(`${path}/`);
//   };

//   return (
//     <SidebarProvider defaultOpen={true}>
//       <div className="flex h-screen bg-muted/30">
//         {/* Sidebar for desktop */}
//         <Sidebar className="hidden md:flex">
//           <SidebarHeader className="flex items-center justify-between p-4">
//             <Link to="/shopper" className="flex items-center space-x-2">
//               <img src="/placeholder.svg?height=40&width=40" alt="Logo" className="h-10 w-10" />
//               <span className="text-xl font-bold text-primary">FarmMarket</span>
//             </Link>
//           </SidebarHeader>

//           <SidebarContent>
//             <SidebarGroup>
//               <SidebarGroupLabel>Shopping</SidebarGroupLabel>
//               <SidebarGroupContent>
//                 <SidebarMenu>
//                   {navItems.slice(0, 5).map((item) => (
//                     <SidebarMenuItem key={item.name}>
//                       <SidebarMenuButton asChild isActive={isActive(item.path)} tooltip={item.name}>
//                         <Link to={item.path}>
//                           <item.icon className="h-5 w-5" />
//                           <span>{item.name}</span>
//                         </Link>
//                       </SidebarMenuButton>
//                     </SidebarMenuItem>
//                   ))}
//                 </SidebarMenu>
//               </SidebarGroupContent>
//             </SidebarGroup>

//             <SidebarSeparator />

//             <SidebarGroup>
//               <SidebarGroupLabel>Account</SidebarGroupLabel>
//               <SidebarGroupContent>
//                 <SidebarMenu>
//                   {navItems.slice(5).map((item) => (
//                     <SidebarMenuItem key={item.name}>
//                       <SidebarMenuButton asChild isActive={isActive(item.path)} tooltip={item.name}>
//                         <Link to={item.path}>
//                           <item.icon className="h-5 w-5" />
//                           <span>{item.name}</span>
//                         </Link>
//                       </SidebarMenuButton>
//                     </SidebarMenuItem>
//                   ))}
//                 </SidebarMenu>
//               </SidebarGroupContent>
//             </SidebarGroup>

//             <SidebarSeparator />

//             <SidebarGroup>
//               <SidebarGroupLabel>Support</SidebarGroupLabel>
//               <SidebarGroupContent>
//                 <SidebarMenu>
//                   <SidebarMenuItem>
//                     <SidebarMenuButton asChild isActive={isActive('/shopper/help')} tooltip="Help & Support">
//                       <Link to="/shopper/help">
//                         <HelpCircle className="h-5 w-5" />
//                         <span>Help & Support</span>
//                       </Link>
//                     </SidebarMenuButton>
//                   </SidebarMenuItem>
//                 </SidebarMenu>
//               </SidebarGroupContent>
//             </SidebarGroup>
//           </SidebarContent>

//           <SidebarFooter className="p-4">
//             <div className="flex items-center space-x-3">
//               <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden">
//                 {user?.image ? (
//                   <img
//                     src={user.image || '/placeholder.svg'}
//                     alt={user?.firstName}
//                     className="h-full w-full object-cover"
//                   />
//                 ) : (
//                   <User className="h-6 w-6 text-primary" />
//                 )}
//               </div>
//               <div className="flex-1 min-w-0">
//                 <p className="text-sm font-medium truncate">
//                   {user?.firstName || 'Shopper'} {user?.lastName || 'User'}
//                 </p>
//                 <p className="text-xs text-muted-foreground truncate">{user?.email || '<EMAIL>'}</p>
//               </div>
//               <button onClick={handleLogout} className="p-2 rounded-md hover:bg-muted transition-colors" title="Logout">
//                 <LogOut className="h-5 w-5 text-muted-foreground" />
//               </button>
//             </div>
//           </SidebarFooter>
//         </Sidebar>

//         {/* Mobile header and menu */}
//         <div className="flex flex-col flex-1 w-full overflow-hidden">
//           <header className="bg-background border-b border-border z-30 flex h-16 items-center justify-between px-4 md:px-6">
//             <div className="flex items-center md:hidden">
//               <button
//                 onClick={toggleMobileMenu}
//                 className="p-2 rounded-md text-muted-foreground hover:bg-muted transition-colors"
//               >
//                 {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
//               </button>
//               <Link to="/shopper" className="ml-3 flex items-center space-x-2">
//                 <img src="/placeholder.svg?height=32&width=32" alt="Logo" className="h-8 w-8" />
//                 <span className="text-lg font-bold text-primary">FarmMarket</span>
//               </Link>
//             </div>

//             <div className="flex items-center space-x-4">
//               <Link
//                 to="/shopper/cart"
//                 className="p-2 rounded-md text-muted-foreground hover:bg-muted transition-colors relative"
//               >
//                 <ShoppingCart className="h-5 w-5" />
//                 <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-primary"></span>
//               </Link>
//               <Link
//                 to="/shopper/notifications"
//                 className="p-2 rounded-md text-muted-foreground hover:bg-muted transition-colors relative"
//               >
//                 <Bell className="h-5 w-5" />
//                 <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-destructive"></span>
//               </Link>
//               <Link
//                 to="/shopper/settings"
//                 className="p-2 rounded-md text-muted-foreground hover:bg-muted transition-colors"
//               >
//                 <Settings className="h-5 w-5" />
//               </Link>
//               <div className="hidden md:flex items-center space-x-2">
//                 <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden">
//                   {user?.image ? (
//                     <img
//                       src={user.image || '/placeholder.svg'}
//                       alt={user?.firstName}
//                       className="h-full w-full object-cover"
//                     />
//                   ) : (
//                     <User className="h-5 w-5 text-primary" />
//                   )}
//                 </div>
//                 <span className="text-sm font-medium">{user?.firstName || 'Shopper'}</span>
//               </div>
//             </div>
//           </header>

//           {/* Mobile menu */}
//           {isMobileMenuOpen && (
//             <motion.div
//               initial={{ opacity: 0, x: -100 }}
//               animate={{ opacity: 1, x: 0 }}
//               exit={{ opacity: 0, x: -100 }}
//               className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
//               onClick={toggleMobileMenu}
//             >
//               <motion.div
//                 initial={{ x: '-100%' }}
//                 animate={{ x: 0 }}
//                 transition={{ type: 'spring', damping: 20 }}
//                 className="fixed inset-y-0 left-0 z-50 w-3/4 max-w-xs bg-background shadow-xl p-4"
//                 onClick={(e) => e.stopPropagation()}
//               >
//                 <div className="flex items-center justify-between mb-6">
//                   <Link to="/shopper" className="flex items-center space-x-2">
//                     <img src="/placeholder.svg?height=40&width=40" alt="Logo" className="h-10 w-10" />
//                     <span className="text-xl font-bold text-primary">FarmMarket</span>
//                   </Link>
//                   <button
//                     onClick={toggleMobileMenu}
//                     className="p-2 rounded-md text-muted-foreground hover:bg-muted transition-colors"
//                   >
//                     <X className="h-5 w-5" />
//                   </button>
//                 </div>

//                 <div className="mb-6">
//                   <div className="flex items-center space-x-3 mb-4">
//                     <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden">
//                       {user?.image ? (
//                         <img
//                           src={user.image || '/placeholder.svg'}
//                           alt={user?.firstName}
//                           className="h-full w-full object-cover"
//                         />
//                       ) : (
//                         <User className="h-6 w-6 text-primary" />
//                       )}
//                     </div>
//                     <div>
//                       <p className="text-sm font-medium">
//                         {user?.firstName || 'Shopper'} {user?.lastName || 'User'}
//                       </p>
//                       <p className="text-xs text-muted-foreground">{user?.email || '<EMAIL>'}</p>
//                     </div>
//                   </div>
//                 </div>

//                 <nav className="space-y-1">
//                   {navItems.map((item) => (
//                     <Link
//                       key={item.name}
//                       to={item.path}
//                       className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm ${
//                         isActive(item.path)
//                           ? 'bg-primary text-primary-foreground'
//                           : 'text-muted-foreground hover:bg-muted transition-colors'
//                       }`}
//                     >
//                       <item.icon className="h-5 w-5" />
//                       <span>{item.name}</span>
//                     </Link>
//                   ))}
//                   <Link
//                     to="/shopper/help"
//                     className={`flex items-center space-x-3 px-3 py-2 rounded-md text-sm ${
//                       isActive('/shopper/help')
//                         ? 'bg-primary text-primary-foreground'
//                         : 'text-muted-foreground hover:bg-muted transition-colors'
//                     }`}
//                   >
//                     <HelpCircle className="h-5 w-5" />
//                     <span>Help & Support</span>
//                   </Link>
//                 </nav>

//                 <div className="absolute bottom-4 left-4 right-4">
//                   <button
//                     onClick={handleLogout}
//                     className="w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-md bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors"
//                   >
//                     <LogOut className="h-5 w-5" />
//                     <span>Logout</span>
//                   </button>
//                 </div>
//               </motion.div>
//             </motion.div>
//           )}

//           {/* Main content */}
//           <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
//         </div>
//       </div>
//     </SidebarProvider>
//   );
// };

// export default ShopperLayout;
