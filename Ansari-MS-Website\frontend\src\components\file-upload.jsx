"use client"

import { useState, useRef } from "react"
import { Upload } from "lucide-react"

const FileUploadComponent = ({ onFileChange, previewImage, setPreviewImage }) => {
  const [isDragging, setIsDragging] = useState(false)
  const [error, setError] = useState("")
  const fileInputRef = useRef(null)

  const handleDragOver = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(false)
    setError("")

    const files = e.dataTransfer.files
    if (files && files.length > 0) {
      validateAndProcessFile(files[0])
    }
  }

  const handleFileInputChange = (e) => {
    setError("")
    const file = e.target.files[0]
    if (file) {
      validateAndProcessFile(file)
    }
  }

  const validateAndProcessFile = (file) => {
    // Check file type
    const validTypes = ["image/jpeg", "image/png", "image/gif"]
    if (!validTypes.includes(file.type)) {
      setError("Please upload a PNG, JPG, or GIF file")
      return
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setError("File size should be less than 10MB")
      return
    }

    // Process valid file
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewImage(reader.result)
    }
    reader.readAsDataURL(file)
    onFileChange(file)
  }

  const handleRemoveImage = () => {
    setPreviewImage(null)
    onFileChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="mt-1">
      {previewImage ? (
        <div className="text-center">
          <img
            src={previewImage || "/placeholder.svg"}
            alt="Preview"
            className="mx-auto h-32 w-32 rounded-full object-cover"
          />
          <button type="button" onClick={handleRemoveImage} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Remove
          </button>
        </div>
      ) : (
        <div
          className={`flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-lg ${
            isDragging ? "border-[#FF6B00] bg-orange-50" : error ? "border-red-300" : "border-gray-300"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="space-y-1 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="file-upload"
                className="relative cursor-pointer bg-white rounded-md font-medium text-[#FF6B00] hover:text-[#D32F2F] focus-within:outline-none"
              >
                <span>Upload a file</span>
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  className="sr-only"
                  accept="image/png,image/jpeg,image/gif"
                  onChange={handleFileInputChange}
                  ref={fileInputRef}
                />
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
            {error && <p className="text-xs text-red-500 mt-2">{error}</p>}
          </div>
        </div>
      )}
    </div>
  )
}

export default FileUploadComponent
