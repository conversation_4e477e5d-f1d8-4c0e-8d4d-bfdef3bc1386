/* eslint-disable no-unused-vars */
import { useState, useEffect } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import { ArrowLeft, Calendar, User, Eye, Share2 } from 'lucide-react';
import { useNews } from '../../contexts/NewsContext';

const NewsDetail = () => {
  const { id } = useParams();
  const location = useLocation();
  const { news } = useNews();
  const [selectedNews, setSelectedNews] = useState(null);
  const [loading, setLoading] = useState(true);
  const [relatedNews, setRelatedNews] = useState([]);

  useEffect(() => {
    // First check if we have the news item in the location state
    if (location.state?.selectedNews) {
      setSelectedNews(location.state.selectedNews);
      setLoading(false);
    } else {
      // If not, find it from the news context
      const foundNews = news.find((item) => item.id === parseInt(id));
      if (foundNews) {
        setSelectedNews(foundNews);
      }
      setLoading(false);
    }
  }, [id, news, location.state]);

  // Populate related articles when selectedNews changes
  useEffect(() => {
    if (selectedNews && news.length > 0) {
      // Get all published articles excluding current article
      const availableArticles = news.filter((item) =>
        item.id !== selectedNews.id && item.status === 'Published'
      );

      // First, find articles with the same category as the current article
      const sameCategoryArticles = availableArticles.filter((item) =>
        item.category === selectedNews.category
      );

      // PRIORITY 1: Always prioritize showing more cards from the selected category first
      if (sameCategoryArticles.length > 0) {
        // Show all available articles from the same category (up to 3)
        if (sameCategoryArticles.length >= 3) {
          // If we have 3 or more from same category, show only same category
          setRelatedNews(sameCategoryArticles.slice(0, 3));
        } else {
          // If we have 1-2 from same category, show them all + fill with others
          const otherCategoryArticles = availableArticles
            .filter((item) => item.category !== selectedNews.category)
            .slice(0, 3 - sameCategoryArticles.length);

          // Put same category articles first, then others
          const combinedRelated = [...sameCategoryArticles, ...otherCategoryArticles];
          setRelatedNews(combinedRelated);
        }
      }
      // PRIORITY 3: If current category has no other articles, show from other categories
      else {
        // Get all available categories (excluding current category since it has no other articles)
        const otherCategories = [...new Set(availableArticles.map(item => item.category))];

        if (otherCategories.length > 0) {
          // Group articles by category and prioritize categories with more articles
          const categoryGroups = otherCategories.map(category => ({
            category,
            articles: availableArticles.filter(item => item.category === category),
            count: availableArticles.filter(item => item.category === category).length
          }));

          // Sort by article count (descending) - categories with more content first
          categoryGroups.sort((a, b) => b.count - a.count);

          // Strategy: Show variety first, then fill from categories with most content
          const selectedArticles = [];

          // First, take one article from each category to show variety
          for (const group of categoryGroups) {
            if (selectedArticles.length < 3) {
              selectedArticles.push(group.articles[0]);
            }
          }

          // If we still need more articles, take additional from categories with most content
          if (selectedArticles.length < 3) {
            for (const group of categoryGroups) {
              if (selectedArticles.length < 3 && group.articles.length > 1) {
                const remainingFromCategory = group.articles.slice(1);
                const needed = 3 - selectedArticles.length;
                selectedArticles.push(...remainingFromCategory.slice(0, needed));
              }
            }
          }

          setRelatedNews(selectedArticles.slice(0, 3));
        } else {
          setRelatedNews([]);
        }
      }
    }
  }, [selectedNews, news]);

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-10 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="h-64 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedNews) {
    return (
      <div className="min-h-screen pt-20 pb-10 px-4 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold font-heading text-[#2C3E50] mb-4">News Article Not Found</h2>
          <p className="text-gray-600 font-sans mb-6">
            The article you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Link
            to="/news"
            className="bg-[#FF6B00] text-white font-sans font-bold px-6 py-3 rounded-lg hover:bg-[#D32F2F] transition-all duration-300"
          >
            Back to News
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted to-white">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-secondary to-[#34495e] pt-20 pb-16">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-6xl mx-auto px-4">
          {/* Back button */}
          <Link
            to={location.state?.fromManage ? '/admin/news' : '/news'}
            className="inline-flex items-center text-white hover:text-primary mb-8 transition-colors bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg shadow-card"
          >
            <ArrowLeft size={18} className="mr-2" />
            Back to {location.state?.fromManage ? 'Manage News' : 'News'}
          </Link>

          {/* Article header */}
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-headingEn font-bold text-white mb-6 leading-tight">
              {selectedNews.title}
            </h1>

            {/* Category badge */}
            <div className="mb-6">
              <span className="inline-block px-6 py-3 bg-primary text-white rounded-lg text-sm font-bold uppercase tracking-wide shadow-card hover:bg-primary-hover transition-colors">
                {selectedNews.category}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 -mt-8 relative z-10">
        {/* Article Card */}
        <div className="bg-card rounded-xl shadow-card-hover overflow-hidden mb-12">

          {/* Meta information */}
          <div className="p-8 border-b border-border-color">
            <div className="flex flex-wrap items-center justify-center gap-6 text-textprimary">
              <div className="flex items-center bg-muted px-4 py-3 rounded-lg shadow-sm hover:shadow-card transition-shadow">
                <Calendar size={18} className="mr-3 text-primary" />
                <span className="font-medium font-bodyEn">
                  {selectedNews.createdAt
                    ? new Date(selectedNews.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })
                    : 'No date'
                  }
                </span>
              </div>
              <div className="flex items-center bg-muted px-4 py-3 rounded-lg shadow-sm hover:shadow-card transition-shadow">
                <User size={18} className="mr-3 text-primary" />
                <span className="font-medium font-bodyEn">
                  {selectedNews.author || selectedNews.autherName || 'Unknown Author'}
                </span>
              </div>
              <div className="flex items-center bg-muted px-4 py-3 rounded-lg shadow-sm hover:shadow-card transition-shadow">
                <Eye size={18} className="mr-3 text-primary" />
                <span className="font-medium font-bodyEn">{selectedNews.views || 0} views</span>
              </div>
            </div>
          </div>

          {/* Featured image */}
          <div className="relative">
            <img
              src={ `http://localhost:5432/public/images/news/${selectedNews.image}` || '/placeholder.svg'}
              alt={selectedNews.title}
              className="w-full h-[400px] md:h-[500px] object-cover"
              onError={(e) => {
                e.target.src = '/placeholder.svg';
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>

          {/* Article content */}
          <div className="p-8 md:p-12">
            <div
              className="prose prose-lg max-w-none text-textprimary leading-relaxed font-bodyEn"
              style={{
                fontSize: '1.125rem',
                lineHeight: '1.8'
              }}
              dangerouslySetInnerHTML={{ __html: selectedNews.content }}
            />

            {/* Tags */}
            {selectedNews.tags && selectedNews.tags.length > 0 && (
              <div className="mt-12 pt-8 border-t border-border-color">
                <h4 className="text-lg font-headingEn font-semibold text-secondary mb-4">Tags</h4>
                <div className="flex flex-wrap gap-3">
                  {selectedNews.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-gradient-to-r from-muted to-white text-textprimary border border-border-color px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary hover:text-white hover:border-primary transition-all cursor-pointer shadow-sm"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Share Section */}
        <div className="bg-card rounded-xl shadow-card p-8 mb-8">
          <div className="text-center">
            <h3 className="text-xl font-headingEn font-bold text-secondary mb-6">Share this article</h3>
            <div className="flex justify-center space-x-4">
              <button className="group w-12 h-12 rounded-lg bg-[#1877F2] text-white flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-card hover:shadow-card-hover">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z"></path>
                </svg>
              </button>
              <button className="group w-12 h-12 rounded-lg bg-[#1DA1F2] text-white flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-card hover:shadow-card-hover">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"></path>
                </svg>
              </button>
              <button className="group w-12 h-12 rounded-lg bg-[#0A66C2] text-white flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-card hover:shadow-card-hover">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 8a6 6 0 016 6v7h-4v-7a2 2 0 00-2-2 2 2 0 00-2 2v7h-4v-7a6 6 0 016-6zM2 9h4v12H2z"></path>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </button>
              <button className="group w-12 h-12 rounded-lg bg-success text-white flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-card hover:shadow-card-hover">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"></path>
                </svg>
              </button>
              <button className="group w-12 h-12 rounded-lg bg-muted border border-border-color text-textprimary flex items-center justify-center hover:scale-110 hover:bg-primary hover:text-white hover:border-primary transition-all duration-200 shadow-card">
                <Share2 size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Current Category Info */}
        <div className="bg-gradient-to-r from-secondary to-[#34495e] rounded-xl p-6 mb-8 text-white shadow-card-hover">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <p className="text-white/80 text-sm font-bodyEn font-medium mb-2">You're exploring</p>
              <div className="flex items-center justify-center">
                <span className="px-6 py-3 bg-primary rounded-lg text-white text-lg font-headingEn font-bold tracking-wide shadow-card hover:bg-primary-hover transition-colors">
                  📰 {selectedNews.category}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Related articles */}
        <div className="bg-card rounded-xl shadow-card-hover p-8">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-headingEn font-bold text-secondary mb-2">
              More from "{selectedNews.category}"
            </h3>
            {relatedNews.length > 0 && (
              <p className="text-textprimary font-bodyEn">
                {relatedNews.filter(item => item.category === selectedNews.category).length > 0
                  ? `Discover ${relatedNews.filter(item => item.category === selectedNews.category).length} more articles in this category`
                  : "Explore articles from other categories"
                }
              </p>
            )}
          </div>

          {relatedNews.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedNews.map((item) => (
                <Link key={item.id} to={`/news/${item.id}`} className="group">
                  <div className="bg-gradient-to-br from-card to-muted rounded-xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 transform hover:-translate-y-2 border border-border-color">
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={
                          item.image
                            ? `http://localhost:5432/public/images/news/${item.image}`
                            : '/placeholder.svg'
                        }
                        alt={item.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        onError={(e) => {
                          e.target.src = '/placeholder.svg';
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute top-4 left-4">
                        <span className={`px-3 py-1 rounded-lg text-xs font-semibold shadow-sm ${
                          item.category === selectedNews.category
                            ? 'bg-primary text-white'
                            : 'bg-white/90 text-textprimary border border-border-color'
                        }`}>
                          {item.category}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <h4 className="font-headingEn font-bold text-lg text-textprimary mb-3 group-hover:text-primary transition-colors line-clamp-2">
                        {item.title}
                      </h4>
                      <div className="flex items-center text-sm text-textprimary/70 font-bodyEn">
                        <Calendar size={16} className="mr-2 text-primary" />
                        {item.createdAt
                          ? new Date(item.createdAt).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })
                          : 'No date'
                        }
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center border border-border-color">
                <svg className="w-12 h-12 text-textprimary/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
              </div>
              <h4 className="text-xl font-headingEn font-semibold text-secondary mb-2">No more articles available</h4>
              <p className="text-textprimary/70 font-bodyEn">Check back later for more content in this category.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewsDetail;
