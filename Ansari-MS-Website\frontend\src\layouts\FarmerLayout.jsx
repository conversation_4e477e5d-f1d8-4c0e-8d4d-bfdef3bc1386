/* eslint-disable react/prop-types */
'use client';

import { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Home, LogOut, User, Sun, Moon,Bell,Settings } from 'lucide-react';
import { FaSnapchat } from 'react-icons/fa';

import { useAuth } from '../contexts/AuthContext';
import {
  SidebarProvider,
  SidebarContent,
  SidebarFooter,
  SidebarMenu,
  SidebarGroup,
  SidebarGroupContent,
} from '../components/management-system/ui/sidebar';

const classNames = (...classes) => classes.filter(Boolean).join(' ');

const FarmerLayout = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('theme') === 'dark' ||
        window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });

  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  useEffect(() => {
    const root = document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      root.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  }, [isDarkMode]);

  const toggleDarkMode = () => setIsDarkMode(!isDarkMode);

  const handleLogout = () => {
    logout();
    navigate('/signin');
  };

  const navItems = [
    { name: 'Dashboard', icon: Home, path: '/farmer' },
    { name: 'Chat', icon: FaSnapchat, path: '/farmer/chat' },
  ];

  const isActive = (path) => location.pathname === path;

  
  const UserAvatar = ({ size = 8, className = '' }) => (
    <div
      className={classNames(
        `rounded-full bg-primary/10 flex items-center justify-center overflow-hidden`,
        size === 10 ? 'h-10 w-10' : `h-${size} w-${size}`,
        className
      )}
    >
      {user?.image ? (
        <img
          src={`http://localhost:5432/public/images/users/${user.image}`}
          alt={`${user.firstName || 'Farmer'} ${user.lastName || 'User'}`}
          className="h-full w-full object-cover"
          loading="lazy"
        />
      ) : (
        <User className={`text-primary ${size === 10 ? 'h-6 w-6' : 'h-5 w-5'}`} />
      )}
    </div>
  );

  return (
    <SidebarProvider defaultOpen>
      <div className="flex bg-muted/30 dark:bg-gray-900 text-textprimary dark:text-gray-100 min-h-screen transition-colors">
        {/* Desktop Sidebar */}
        <aside className="hidden md:flex flex-col w-64 bg-muted dark:bg-gray-800 border-r border-border dark:border-gray-700 p-4 gap-10">
          {/* User Info */}
          <div className="flex items-center gap-3 px-2">
            <UserAvatar />
            <span className="text-primary dark:text-white font-medium text-sm truncate">
              {user?.firstName || 'Farmer'} {user?.lastName || 'User'}
            </span>
          </div>

          {/* Title */}
          <div className="border-y border-gray-500 my-2 py-2 px-2">
            <h3 className="text-lg font-bold">Farmer Dashboard</h3>
          </div>

          {/* Navigation */}
          <SidebarContent className="flex-1 px-2 overflow-auto">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="flex flex-col gap-2">
                  {navItems.map(({ name, icon: Icon, path }) => (
                    <Link
                      key={name}
                      to={path}
                      className={classNames(
                        'flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors',
                        isActive(path)
                          ? 'bg-primary text-primary-foreground'
                          : 'text-textprimary dark:text-gray-300 hover:bg-secondary hover:text-secondary-foreground'
                      )}
                    >
                      <Icon className="h-5 w-5" aria-hidden="true" />
                      <span>{name}</span>
                    </Link>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          {/* Footer */}
          <SidebarFooter className="px-2 py-4 border-t border-border dark:border-gray-700">
            <div className="flex items-center gap-3">
              <UserAvatar size={10} />
              <div className="flex-1 min-w-0 overflow-hidden">
                <p className="text-sm font-medium dark:text-muted truncate">
                  {user?.firstName || 'Farmer'} {user?.lastName || 'User'}
                </p>
                <p className="text-xs text-muted-foreground dark:text-muted truncate">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
              <button
                onClick={toggleDarkMode}
                className="p-2 rounded-md hover:bg-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
                title="Toggle dark mode"
              >
                {isDarkMode ? <Sun className="h-5 w-5 text-yellow-400" /> : <Moon className="h-5 w-5" />}
              </button>
              <button
                onClick={handleLogout}
                title="Logout"
                aria-label="Logout"
                className="p-2 rounded-md hover:bg-muted dark:text-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </SidebarFooter>
        </aside>

        {/* Main content */}
        <div className="flex flex-col flex-1 w-full overflow-hidden">
          <header className="flex items-center justify-between px-4 md:px-6 h-16 bg-muted dark:bg-secondary border-b border-border z-30">
        <div className="md:hidden">
          <button
            onClick={() => setIsMobileMenuOpen((v) => !v)}
            aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
            className="p-2 rounded-md hover:bg-muted dark:bg-secondary focus:outline-none"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        <div className="flex-1" />
        <div className="flex items-center space-x-4 ml-4">
          <button
            onClick={toggleDarkMode}
            aria-label="Toggle Dark Mode"
            className="p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            {isDarkMode ? <Sun className="h-6 w-6 text-yellow-400" /> : <Moon className="h-6 w-6 text-gray-600" />}
          </button>
          <button
            onClick={logout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
          >
            Logout
          </button>
        </div>
      </header>
          {/* Mobile Sidebar */}
          <AnimatePresence>
            {isMobileMenuOpen && (
              <motion.nav
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <motion.div
                  className="fixed inset-y-0 left-0 w-3/4 max-w-xs bg-background dark:bg-gray-800 shadow-xl p-6 overflow-y-auto"
                  initial={{ x: '-100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '-100%' }}
                  transition={{ type: 'spring', damping: 20 }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex items-center mb-6 gap-4">
                    <UserAvatar size={10} />
                    <div>
                      <p className="text-sm font-medium truncate">
                        {user?.firstName || 'Farmer'} {user?.lastName || 'User'}
                      </p>
                      <p className="text-xs text-muted-foreground dark:text-muted truncate">
                        {user?.email || '<EMAIL>'}
                      </p>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="ml-auto p-2 rounded-md hover:bg-muted dark:text-muted dark:hover:bg-gray-700 transition-colors text-muted-foreground"
                    >
                      <LogOut className="h-5 w-5" />
                    </button>
                  </div>

                  <nav className="flex flex-col gap-2">
                    {navItems.map(({ name, icon: Icon, path }) => (
                      <Link
                        key={name}
                        to={path}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={classNames(
                          'flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors',
                          isActive(path)
                            ? 'bg-primary text-primary-foreground'
                            : 'text-textprimary dark:text-gray-300 hover:bg-secondary hover:text-secondary-foreground'
                        )}
                      >
                        <Icon className="h-5 w-5" aria-hidden="true" />
                        <span>{name}</span>
                      </Link>
                    ))}
                  </nav>
                </motion.div>
              </motion.nav>
            )}
          </AnimatePresence>

          {/* Main content */}
          <main className="flex-1 overflow-auto px-6 py-4">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default FarmerLayout;

