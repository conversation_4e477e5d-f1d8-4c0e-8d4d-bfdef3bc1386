import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Save, X } from 'lucide-react';
import { useShop } from '../../contexts/ShopContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import MapLocationPicker from '../../components/MapLocationPicker';
import LocationMapCell from '../../components/LocationMapCell';
import Button from '../../components/Button';
import axios from 'axios';

const EditShopPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { shops, updateShop } = useShop();
  const { language, translations } = useLanguage();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });
  const [shoppers, setShoppers] = useState([]);
  const [selectedShopper, setSelectedShopper] = useState(null);
  const [shoppersLoaded, setShoppersLoaded] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    owner: '',
    phone: '',
    location: '',
    userId: '',
  });

  // Fetch shoppers on component mount
  useEffect(() => {
    if (shoppersLoaded) return; // Prevent multiple calls

    const fetchShoppers = async () => {
      try {
        console.log('Fetching shoppers...');
        const token = localStorage.getItem('authToken');
        const response = await axios.get('http://localhost:5432/api/v1/users/shoppers', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.data.success) {
          console.log('Shoppers loaded:', response.data.data);
          setShoppers(response.data.data);
          setShoppersLoaded(true);
        }
      } catch (error) {
        console.error('Error fetching shoppers:', error);
        setShoppersLoaded(true); // Mark as loaded even on error to prevent retries
      }
    };

    fetchShoppers();
  }, [shoppersLoaded]);

  // Populate form when shop data is available (only once)
  useEffect(() => {
    if (shops.length === 0) return; // Wait for shops to load
    
    const shop = shops.find((s) => s.id === id || s.id.toString() === id);
    console.log('Looking for shop with ID:', id);
    console.log('Found shop:', shop);
    
    if (shop && formData.name === '') { // Only populate if form is empty
      console.log('Populating form with shop data:', shop);
      setFormData({
        name: shop.name || '',
        email: shop.email || '',
        owner: shop.owner || '',
        phone: shop.phone || '',
        location: shop.location || '',
        userId: shop.userId || '',
      });
      
      // Clear any previous error
      setFeedback({ type: '', message: '' });
    } else if (!shop && shops.length > 0) {
      setFeedback({
        type: 'error',
        message: t('shop_not_found') || 'Shop not found. Please check if the shop exists.',
      });
    }
  }, [id, shops, t, formData.name]); // Added formData.name to prevent re-population

  // Separate effect to handle shopper selection when shoppers are loaded (only once)
  useEffect(() => {
    if (shoppersLoaded && shoppers.length > 0 && formData.userId && !selectedShopper) {
      const shopper = shoppers.find(s => s.u_Id.toString() === formData.userId.toString());
      console.log('Setting shopper for userId:', formData.userId);
      console.log('Found shopper:', shopper);

      if (shopper) {
        setSelectedShopper(shopper);
        // Only update owner if it's empty or matches the shopper
        if (!formData.owner || formData.owner === shopper.name) {
          setFormData(prev => ({
            ...prev,
            owner: shopper.name
          }));
        }
      }
    }
  }, [shoppersLoaded, shoppers.length, formData.userId]); // Removed selectedShopper to prevent loops

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`Field changed: ${name} = ${value}`);
    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: value,
      };
      console.log('New form data:', newData);
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const handleShopperSelect = (e) => {
    const shopperId = e.target.value;
    const shopper = shoppers.find(s => s.u_Id.toString() === shopperId);

    console.log('Shopper selected:', shopper);

    if (shopper) {
      setSelectedShopper(shopper);
      setFormData(prev => ({
        ...prev,
        userId: shopper.u_Id.toString(),
        owner: shopper.name
      }));
    } else {
      setSelectedShopper(null);
      setFormData(prev => ({
        ...prev,
        userId: '',
        owner: ''
      }));
    }
    setFeedback({ type: '', message: '' });
  };

  const handleLocationSelect = (location) => {
    setFormData(prev => ({
      ...prev,
      location: location
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) throw new Error(t('name_required') || 'Shop name is required');
    if (!formData.email.trim()) throw new Error(t('email_required') || 'Email is required');
    if (!formData.phone.trim()) throw new Error(t('phone_required') || 'Phone number is required');
    if (!formData.location.trim()) throw new Error(t('location_required') || 'Location is required');
    if (!selectedShopper) throw new Error(t('shopper_required') || 'Please select a shopper');

    // Validate phone format (+93xxxxxxxxx)
    const phoneRegex = /^\+93[0-9]{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      throw new Error(t('invalid_phone_format') || 'Phone must be in format +93xxxxxxxxx');
    }

    // Validate location format (latitude longitude)
    const locationRegex = /^[-+]?[0-9]*\.?[0-9]+ [-+]?[0-9]*\.?[0-9]+$/;
    if (!locationRegex.test(formData.location)) {
      throw new Error(t('invalid_location_format') || 'Location must be in format "latitude longitude"');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      // Prepare shop data with owner ID
      const shopDataToSubmit = {
        SName: formData.name,
        SEmail: formData.email,
        SPhone: formData.phone,
        SLocation: `POINT(${formData.location.split(' ').reverse().join(' ')})`, // Convert to POINT(lng lat)
        SOwner: selectedShopper.name,
        userId: selectedShopper.u_Id,
      };

      console.log('Updating shop with data:', shopDataToSubmit);
      console.log('Selected shopper:', selectedShopper);

      await updateShop(id, shopDataToSubmit);
      setFeedback({
        type: 'success',
        message: t('shop_updated_successfully') || 'Shop updated successfully',
      });

      setTimeout(() => {
        navigate('/admin/shops');
      }, 1500);
    } catch (error) {
      console.error('Error updating shop:', error);
      
      // Extract error message from backend response
      let errorMessage = error.message || t('update_shop_error') || 'Failed to update shop';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center justify-between ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('edit_shop') || 'Edit Shop'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('update_shop_information') || 'Update shop information'}
          </p>
        </div>
        <div className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="outline"
            onClick={() => navigate('/admin/shops')}
            className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
          >
            <X className="h-4 w-4" />
            {t('cancel') || 'Cancel'}
          </Button>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('shop_information') || 'Shop Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Shop Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('shop_name') || 'Shop Name'}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_shop_name') || 'Enter shop name'}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('email') || 'Email'}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_email') || 'Enter email address'}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Phone */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('phone') || 'Phone'}
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="+93xxxxxxxxx"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
                <small className="text-gray-500">Format: +93xxxxxxxxx</small>
              </div>

              {/* Owner (Read-only) */}
              <div>
                <label htmlFor="owner" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('owner') || 'Owner (Selected Shopper)'}
                </label>
                <input
                  type="text"
                  id="owner"
                  name="owner"
                  value={formData.owner}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="Select a shopper to set as owner"
                  readOnly
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
                <small className="text-gray-500">This field is automatically set to the selected shopper</small>
              </div>

              {/* Shopper Selection */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('select_shopper') || 'Select Shopper'}
                </label>
                <select
                  value={selectedShopper?.u_Id || ''}
                  onChange={handleShopperSelect}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  required
                >
                  <option value="">{t('choose_shopper') || 'Choose a shopper...'}</option>
                  {shoppers.map((shopper) => (
                    <option key={shopper.u_Id} value={shopper.u_Id}>
                      {shopper.name} ({shopper.email})
                    </option>
                  ))}
                </select>
                {selectedShopper && (
                  <div className="mt-2 p-2 bg-blue-50 rounded-md">
                    <small className="text-blue-700">
                      Selected: {selectedShopper.name} - User ID: {selectedShopper.u_Id}
                    </small>
                  </div>
                )}
              </div>
            </div>

            {/* Current Location Display */}
            {formData.location && (
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('current_location') || 'Current Location'}
                </label>
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <LocationMapCell location={formData.location} />
                </div>
              </div>
            )}

            {/* Location Selection with Map */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('update_location') || 'Update Location'}
              </label>
              <MapLocationPicker
                onLocationSelect={handleLocationSelect}
                initialLocation={formData.location}
                className="w-full"
              />
            </div>

            {/* Submit Button */}
            <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/admin/shops')}
              >
                {t('cancel') || 'Cancel'}
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Save className="h-4 w-4" />
                {loading ? (t('updating_shop') || 'Updating...') : (t('update_shop') || 'Update Shop')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditShopPage;
