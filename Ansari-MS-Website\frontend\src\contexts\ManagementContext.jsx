import React, { createContext, useContext, useReducer } from 'react';
import { useAuth } from './AuthContext';

const ManagementContext = createContext();

const initialState = {
  farms: [],
  feeds: [],
  medicines: [],
  loading: false,
  error: null,
};

function managementReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_FARMS':
      return { ...state, farms: action.payload };
    case 'SET_FEEDS':
      return { ...state, feeds: action.payload };
    case 'SET_MEDICINES':
      return { ...state, medicines: action.payload };
    case 'ADD_FARM':
      return { ...state, farms: [...state.farms, action.payload] };
    case 'UPDATE_FARM':
      return {
        ...state,
        farms: state.farms.map((farm) => (farm.id === action.payload.id ? action.payload : farm)),
      };
    case 'DELETE_FARM':
      return {
        ...state,
        farms: state.farms.filter((farm) => farm.id !== action.payload),
      };
    case 'ADD_FEED':
      return { ...state, feeds: [...state.feeds, action.payload] };
    case 'UPDATE_FEED':
      return {
        ...state,
        feeds: state.feeds.map((feed) => (feed.id === action.payload.id ? action.payload : feed)),
      };
    case 'DELETE_FEED':
      return {
        ...state,
        feeds: state.feeds.filter((feed) => feed.id !== action.payload),
      };
    case 'ADD_MEDICINE':
      return { ...state, medicines: [...state.medicines, action.payload] };
    case 'UPDATE_MEDICINE':
      return {
        ...state,
        medicines: state.medicines.map((medicine) => (medicine.id === action.payload.id ? action.payload : medicine)),
      };
    case 'DELETE_MEDICINE':
      return {
        ...state,
        medicines: state.medicines.filter((medicine) => medicine.id !== action.payload),
      };
    default:
      return state;
  }
}

export const ManagementProvider = ({ children }) => {
  const [state, dispatch] = useReducer(managementReducer, initialState);
  const { user } = useAuth();

  return <ManagementContext.Provider value={{ state, dispatch }}>{children}</ManagementContext.Provider>;
};

export const useManagement = () => {
  const context = useContext(ManagementContext);
  if (!context) {
    throw new Error('useManagement must be used within a ManagementProvider');
  }
  return context;
};
