import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const LoadingSpinner = () => {
  const { language, translations } = useLanguage();

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  return (
    <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    // <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center">
    //   <div className="relative w-20 h-20">
    //     {/* Outer ring */}
    //     <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
    //     {/* Animated ring */}
    //     <div className="absolute inset-0 border-4 border-[#FF6B00] border-t-transparent animate-spin rounded-full"></div>
    //     {/* Center dot */}
    //     <div className="absolute inset-0 flex items-center justify-center">
    //       <div className="w-2 h-2 bg-[#FF6B00] rounded-full"></div>
    //     </div>
    //   </div>
    //   <div className="mt-6 text-center">
    //     <p className="text-lg font-semibold text-gray-800">{language === 'ps' ? 'بار کول...' : 'Loading...'}</p>
    //     <p className="mt-2 text-sm text-gray-500">
    //       {language === 'ps' ? 'مهرباني وکړئ لږ صبر وکړئ' : 'Please wait while we load your content'}
    //     </p>
    //   </div>
    // </div>
  );
};

export default LoadingSpinner;
