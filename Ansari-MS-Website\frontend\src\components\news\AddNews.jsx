import axios from 'axios';
import { useState } from 'react';
import Button from 'components/shared/Button';

const AddNews = () => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('http://localhost:5000/api/news', {
        title,
        content,
      });
      console.log('News added successfully:', response.data);
      // Optionally clear form fields after successful submission
      setTitle('');
      setContent('');
    } catch (error) {
      console.error('Error adding news:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white shadow-md rounded-lg mt-10">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Add News</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title Input */}
        <div>
          <label htmlFor="title" className="block text-lg font-medium text-gray-700 mb-2">
            News Title
          </label>
          <input
            type="text"
            id="title"
            placeholder="Enter the title of the news"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 transition duration-300"
          />
        </div>

        {/* Content Textarea */}
        <div>
          <label htmlFor="content" className="block text-lg font-medium text-gray-700 mb-2">
            News Content
          </label>
          <textarea
            id="content"
            placeholder="Enter the content of the news"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 transition duration-300 h-40"
          />
        </div>

        {/* Submit Button */}
        <div className="text-right">
          <Button
            variant="primary"
            type="submit"
            // className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300 focus:outline-none"
          >
            Add News
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AddNews;
