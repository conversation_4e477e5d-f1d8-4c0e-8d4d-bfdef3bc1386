import { useState } from 'react';

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    address: '',
    message: '',
  });

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted', formData);
    setFormData({
      name: '',
      email: '',
      address: '',
      message: '',
    });
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-6 bg-custom-gray-1 p-8 sm:p-10 rounded-3xl border w-full max-w-lg mx-auto"
    >
      <h2 className="text-2xl sm:text-3xl font-semibold text-center text-text-third">Get In Touch</h2>

      {/* Name Input */}
      <div className="space-y-1">
        <label htmlFor="name" className="block text-text-third text-sm font-medium">
          Full Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          className="w-full p-4 border-2 border-border-color rounded-lg focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
          placeholder="Enter your full name"
          required
        />
      </div>

      {/* Email Input */}
      <div className="space-y-1">
        <label htmlFor="email" className="block text-text-third text-sm font-medium">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className="w-full p-4 border-2 border-border-color rounded-lg focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
          placeholder="Enter your email"
          required
        />
      </div>

      {/* Address Input */}
      <div className="space-y-1">
        <label htmlFor="address" className="block text-text-third text-sm font-medium">
          Your Address
        </label>
        <input
          type="text"
          id="address"
          name="address"
          value={formData.address}
          onChange={handleChange}
          className="w-full p-4 border-2 border-border-color rounded-lg focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
          placeholder="Enter your address"
          required
        />
      </div>

      {/* Message Input */}
      <div className="space-y-1">
        <label htmlFor="message" className="block text-text-third text-sm font-medium">
          Your Message
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          rows="5"
          className="w-full p-4 border-2 border-border-color rounded-lg focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
          placeholder="Write your message here"
          required
        ></textarea>
      </div>

      {/* Submit Button */}
      <div>
        <button
          type="submit"
          className="w-full py-3 bg-button-bg text-textprimary font-semibold rounded-lg hover:bg-button-bg-hover transition-all duration-300 transform "
        >
          Send Message
        </button>
      </div>
    </form>
  );
};

export default ContactForm;
