# Chat System Update: Message Editing and Deletion

This update adds message editing and deletion capabilities to your chat system, along with real-time Socket.IO integration.

## New Features

### 1. Message Editing
- Users can edit their own messages by clicking the edit button (✏️) that appears on hover
- Edited messages show an "(edited)" indicator
- Original message content is preserved in the database
- Real-time updates for all users in the chat room

### 2. Message Deletion
- Users can delete their own messages by clicking the delete button (🗑️) that appears on hover
- Confirmation modal prevents accidental deletions
- Soft delete - messages are marked as deleted but preserved in database
- Real-time updates for all users in the chat room

### 3. Real-time Updates with Socket.IO
- Instant message delivery without page refresh
- Real-time editing and deletion updates
- Automatic room joining/leaving
- Reduced polling frequency (improved performance)

## Installation & Setup

### 1. Database Migration
Run the database migration to add the required columns:

```bash
cd backend
node scripts/run-migration.js
```

This will add the following columns to your `chat_messages` table:
- `is_deleted` (BO<PERSON>EAN, default FALSE)
- `is_edited` (BO<PERSON>EAN, default FALSE)
- `edited_at` (TIMESTAMP, nullable)
- `original_content` (TEXT, nullable)

### 2. Dependencies
All required dependencies are already installed:
- Backend: `socket.io` (already in package.json)
- Frontend: `socket.io-client` (already in package.json)

### 3. Server Configuration
The server now runs on port 5432 (updated from 3000) to avoid conflicts.

## API Endpoints

### New Endpoints Added:

#### Delete Message
```
DELETE /api/v1/chats/messages/:messageId
```
- Requires authentication
- Only message sender can delete their own messages
- Returns success confirmation with room ID

#### Edit Message
```
PUT /api/v1/chats/messages/:messageId
```
- Requires authentication
- Only message sender can edit their own messages
- Body: `{ "message_content": "new content" }`
- Returns updated message data

## Socket.IO Events

### Client Events (Frontend → Backend):
- `join_room` - Join a chat room
- `leave_room` - Leave a chat room

### Server Events (Backend → Frontend):
- `new_message` - New message received
- `message_deleted` - Message was deleted
- `message_edited` - Message was edited

## Usage

### For Users:
1. **Editing Messages:**
   - Hover over your own message
   - Click the edit button (✏️)
   - Modify the text in the textarea
   - Click "Save" or "Cancel"

2. **Deleting Messages:**
   - Hover over your own message
   - Click the delete button (🗑️)
   - Confirm deletion in the modal

### For Developers:
The system maintains backward compatibility. All existing functionality continues to work as before.

## Database Schema Changes

```sql
ALTER TABLE chat_messages 
ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN is_edited BOOLEAN DEFAULT FALSE,
ADD COLUMN edited_at TIMESTAMP NULL,
ADD COLUMN original_content TEXT NULL;
```

## Security Features

- **Ownership Validation:** Users can only edit/delete their own messages
- **Authentication Required:** All operations require valid authentication
- **Soft Delete:** Messages are never permanently removed from database
- **Original Content Preservation:** Original message content is stored before editing

## Performance Improvements

- **Reduced Polling:** HTTP polling reduced from 5s to 10s intervals
- **Real-time Updates:** Socket.IO provides instant updates
- **Efficient Queries:** Database queries optimized with proper indexing

## Troubleshooting

### Common Issues:

1. **Migration Fails:**
   - Check database connection settings in `.env`
   - Ensure database user has ALTER privileges
   - Run migration manually if needed

2. **Socket.IO Connection Issues:**
   - Verify server is running on port 5432
   - Check CORS settings in server.js
   - Ensure frontend connects to correct port

3. **Edit/Delete Buttons Not Showing:**
   - Verify user authentication
   - Check if user owns the message
   - Ensure message is not already deleted

## Testing

To test the new features:

1. Start the backend server: `npm run dev`
2. Start the frontend: `npm run dev`
3. Open multiple browser tabs/windows
4. Send messages and test editing/deletion
5. Verify real-time updates across all tabs

## Future Enhancements

Potential future improvements:
- Message reactions/emojis
- Message threading/replies
- File attachment editing
- Message search functionality
- Message history/audit trail
