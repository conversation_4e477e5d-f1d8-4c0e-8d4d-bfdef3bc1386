import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, MoreVertical, Trash, Eye, Calendar, DollarSign } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import Link from '../../components/feed-components/Link';

const PurchasesPage = () => {
  const navigate = useNavigate();
  const { purchases, fetchPurchases, deletePurchase, loading } = useChicken();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    fetchPurchases();
  }, []);

  const filteredPurchases = purchases.filter((purchase) =>
    purchase.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    purchase.notes?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id) => {
    if (window.confirm(t('confirm_delete_purchase') || 'Are you sure you want to delete this purchase?')) {
      try {
        await deletePurchase(id);
      } catch (error) {
        console.error('Error deleting purchase:', error);
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    const numericAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_purchases') || 'Loading purchases...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('chicken_purchases') || 'Chicken Purchases'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_chicken_purchases_description') || 'Manage all chicken purchases from suppliers'}
          </p>
        </div>
        <div className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Link 
            to="/admin/chickens" 
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('back_to_dashboard') || 'Back to Dashboard'}
          </Link>
          <Link 
            to="/admin/chickens/purchases/add" 
            className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
            <span>{t('add_new_purchase') || 'Add New Purchase'}</span>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('search_purchases') || 'Search purchases by supplier name or notes...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_purchases') || 'Total Purchases'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredPurchases.length}
                </h3>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Calendar className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_chickens') || 'Total Chickens'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredPurchases.reduce((sum, purchase) => sum + purchase.quantity, 0)}
                </h3>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Eye className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_amount') || 'Total Amount'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(filteredPurchases.reduce((sum, purchase) => {
                    const totalPrice = parseFloat(purchase.totalPrice) || 0;
                    return sum + totalPrice;
                  }, 0))}
                </h3>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Purchases Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('purchases_list') || 'Purchases List'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('date') || 'Date'}</TableHead>
                  <TableHead>{t('supplier') || 'Supplier'}</TableHead>
                  <TableHead>{t('quantity') || 'Quantity'}</TableHead>
                  <TableHead>{t('price_per_chicken') || 'Price/Chicken'}</TableHead>
                  <TableHead>{t('total_price') || 'Total Price'}</TableHead>
                  <TableHead>{t('status') || 'Status'}</TableHead>
                  <TableHead>{t('allocated') || 'Allocated'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPurchases.length > 0 ? (
                  filteredPurchases.map((purchase) => (
                    <TableRow key={purchase.id}>
                      <TableCell>{formatDate(purchase.purchaseDate)}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{purchase.supplierName}</div>
                          {purchase.supplierContact && (
                            <div className="text-sm text-gray-500">{purchase.supplierContact}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{purchase.quantity}</TableCell>
                      <TableCell>{formatCurrency(purchase.pricePerChicken)}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(purchase.totalPrice || 0)}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          purchase.status === 'purchased' 
                            ? 'bg-blue-100 text-blue-800'
                            : purchase.status === 'partially_allocated'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {purchase.status === 'purchased' ? 'Available' : 
                           purchase.status === 'partially_allocated' ? 'Partial' : 'Allocated'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{purchase.allocatedQuantity || 0} / {purchase.quantity}</div>
                          <div className="text-gray-500">
                            {purchase.remainingQuantity || purchase.quantity} remaining
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem 
                              onClick={() => navigate(`/admin/chickens/allocations/add?purchaseId=${purchase.id}`)}
                              disabled={purchase.remainingQuantity <= 0}
                            >
                              <Eye className={`h-4 w-4 text-green-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('allocate_to_farm') || 'Allocate to Farm'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(purchase.id)}>
                              <Trash className={`h-4 w-4 text-red-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      {t('no_purchases_found') || 'No purchases found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PurchasesPage;
