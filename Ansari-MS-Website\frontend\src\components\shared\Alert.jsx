import React from 'react';
import { X, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';

const Alert = React.forwardRef(({ children, variant = 'info', title, onClose, className = '', ...props }, ref) => {
  const variants = {
    info: {
      icon: Info,
      styles: 'bg-blue-50 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800',
    },
    success: {
      icon: CheckCircle,
      styles:
        'bg-green-50 dark:bg-green-900/50 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800',
    },
    warning: {
      icon: AlertTriangle,
      styles:
        'bg-yellow-50 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
    },
    error: {
      icon: AlertCircle,
      styles: 'bg-red-50 dark:bg-red-900/50 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800',
    },
  };

  const Icon = variants[variant].icon;

  return (
    <div ref={ref} className={`rounded-lg border p-4 ${variants[variant].styles} ${className}`} role="alert" {...props}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Icon className="h-5 w-5" />
        </div>
        <div className="ml-3 flex-1">
          {title && <h3 className="text-sm font-medium">{title}</h3>}
          <div className="text-sm">{children}</div>
        </div>
        {onClose && (
          <div className="ml-4 flex-shrink-0">
            <button
              type="button"
              className="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              onClick={onClose}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
});

Alert.displayName = 'Alert';

export default Alert;
