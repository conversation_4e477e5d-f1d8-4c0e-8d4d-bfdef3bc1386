import { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: 'http://localhost:5432/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Create the context
const ShopContext = createContext();

// Custom hook to use the context
export const useShop = () => {
  const context = useContext(ShopContext);
  if (!context) {
    throw new Error('useShop must be used within a ShopProvider');
  }
  return context;
};

// Create a provider component
export const ShopProvider = ({ children }) => {
  const [shops, setShops] = useState([]);
  const [loading, setLoading] = useState(false);
  const [shoppers, setShoppers] = useState([]);

  // Fetch shoppers from API
  const fetchShoppers = async () => {
    try {
      const response = await api.get('/users/shoppers');
      if (response.data.success) {
        setShoppers(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching shoppers:', error);
    }
  };

  // Fetch shops from API
  const fetchShops = async () => {
    try {
      setLoading(true);
      
      // Fetch both shops and shoppers data
      const [shopsResponse, shoppersResponse] = await Promise.all([
        api.get('/shops'),
        api.get('/users/shoppers')
      ]);

      if (shopsResponse.data.success) {
        // Store shoppers data
        const shoppersData = shoppersResponse.data.success ? shoppersResponse.data.data : [];
        setShoppers(shoppersData);
        
        // Transform backend data to frontend format
        const transformedShops = shopsResponse.data.data.map(shop => {
          // Parse location from MySQL POINT format to "latitude longitude"
          let location = shop.SLocation;
          if (location && location.includes('POINT(')) {
            // Extract coordinates from POINT(longitude latitude) format
            const coords = location.match(/POINT\(([^)]+)\)/);
            if (coords && coords[1]) {
              const [longitude, latitude] = coords[1].split(' ');
              location = `${latitude} ${longitude}`; // Convert to "latitude longitude" format
            }
          }

          // Find shopper name by userId
          const shopper = shoppersData.find(s => s.u_Id === shop.userId);
          const ownerName = shopper ? shopper.name : `User ${shop.userId}`;

          return {
            id: shop.SId,
            name: shop.SName,
            email: shop.SEmail,
            owner: ownerName, // Use farmer name instead of SOwner
            phone: shop.SPhone,
            location: location,
            userId: shop.userId,
            // Add default values for frontend compatibility
            type: 'retail',
            status: 'active',
            lastInspection: '',
            nextInspection: '',
            notes: '',
          };
        });
        setShops(transformedShops);
      }
    } catch (error) {
      console.error('Error fetching shops:', error);
      toast.error('Failed to fetch shops');
    } finally {
      setLoading(false);
    }
  };

  // Load shops on component mount
  useEffect(() => {
    fetchShops();
  }, []);

  // Add new shop
  const addShop = async (shopData) => {
    try {
      const response = await api.post('/shops', shopData);
      if (response.data.success) {
        // Refresh shops list
        await fetchShops();
      }
    } catch (error) {
      console.error('Error adding shop:', error);
      throw error;
    }
  };

  // Update shop
  const updateShop = async (id, shopData) => {
    try {
      const response = await api.put(`/shops/${id}`, shopData);
      if (response.data.success) {
        // Refresh shops list
        await fetchShops();
      }
    } catch (error) {
      console.error('Error updating shop:', error);
      throw error;
    }
  };

  // Delete shop
  const deleteShop = async (id) => {
    try {
      const response = await api.delete(`/shops/${id}`);
      if (response.data.success) {
        // Refresh shops list
        await fetchShops();
        toast.success('Shop deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting shop:', error);
      toast.error('Failed to delete shop');
      throw error;
    }
  };

  const value = {
    shops,
    loading,
    shoppers,
    fetchShops,
    addShop,
    updateShop,
    deleteShop,
  };

  return <ShopContext.Provider value={value}>{children}</ShopContext.Provider>;
};
