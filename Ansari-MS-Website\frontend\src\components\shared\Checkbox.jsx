import React from 'react';
import { Check } from 'lucide-react';

const Checkbox = React.forwardRef(
  (
    { label, checked, onChange, error, helperText, className = '', disabled = false, required = false, ...props },
    ref
  ) => {
    const baseStyles =
      'h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-[#FF6B00] focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed';
    const borderStyles = error ? 'border-red-500 dark:border-red-500' : 'border-gray-300 dark:border-gray-600';

    return (
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            type="checkbox"
            ref={ref}
            checked={checked}
            onChange={onChange}
            disabled={disabled}
            required={required}
            className={`${baseStyles} ${borderStyles} ${className}`}
            {...props}
          />
        </div>
        <div className="ml-3">
          {label && (
            <label
              className={`text-sm font-medium ${disabled ? 'text-gray-500 dark:text-gray-400' : 'text-gray-700 dark:text-gray-300'}`}
            >
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {(error || helperText) && (
            <p className={`mt-1 text-sm ${error ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
              {error || helperText}
            </p>
          )}
        </div>
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export default Checkbox;
