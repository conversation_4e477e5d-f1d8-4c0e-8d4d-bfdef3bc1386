import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';

const AddPurchasePage = () => {
  const navigate = useNavigate();
  const { createPurchase } = useChicken();
  const { language, translations } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    purchaseDate: new Date().toISOString().split('T')[0],
    quantity: '',
    pricePerChicken: '',
    totalPrice: '',
    supplierName: '',
    supplierContact: '',
    notes: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newData = { ...prev, [name]: value };
      
      // Auto-calculate total price when quantity or price per chicken changes
      if (name === 'quantity' || name === 'pricePerChicken') {
        const quantity = parseFloat(name === 'quantity' ? value : newData.quantity) || 0;
        const pricePerChicken = parseFloat(name === 'pricePerChicken' ? value : newData.pricePerChicken) || 0;
        newData.totalPrice = (quantity * pricePerChicken).toFixed(2);
      }
      
      return newData;
    });
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.purchaseDate) throw new Error(t('purchase_date_required') || 'Purchase date is required');
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('quantity_required') || 'Valid quantity is required');
    if (!formData.pricePerChicken || formData.pricePerChicken <= 0) throw new Error(t('price_required') || 'Valid price per chicken is required');
    if (!formData.supplierName.trim()) throw new Error(t('supplier_name_required') || 'Supplier name is required');
    
    // Validate phone format if provided
    if (formData.supplierContact && !/^\+93[0-9]{9}$/.test(formData.supplierContact)) {
      throw new Error(t('invalid_phone_format') || 'Phone must be in format +93xxxxxxxxx');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      const purchaseData = {
        purchaseDate: formData.purchaseDate,
        quantity: parseInt(formData.quantity),
        pricePerChicken: parseFloat(formData.pricePerChicken),
        totalPrice: parseFloat(formData.totalPrice),
        supplierName: formData.supplierName.trim(),
        supplierContact: formData.supplierContact.trim() || null,
        notes: formData.notes.trim() || null,
      };

      await createPurchase(purchaseData);
      
      setFeedback({
        type: 'success',
        message: t('purchase_created_successfully') || 'Purchase created successfully',
      });

      setTimeout(() => {
        navigate('/admin/chickens/purchases');
      }, 1500);
    } catch (error) {
      console.error('Error creating purchase:', error);
      
      let errorMessage = error.message || t('create_purchase_error') || 'Failed to create purchase';
      
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      setFeedback({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <Button
          variant="secondary"
          onClick={() => navigate('/admin/chickens/purchases')}
          className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back_to_purchases') || 'Back to Purchases'}
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('add_new_purchase') || 'Add New Purchase'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('create_new_chicken_purchase') || 'Create a new chicken purchase from supplier'}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('purchase_information') || 'Purchase Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          {feedback.message && (
            <div
              className={`mb-6 p-4 rounded-lg ${
                feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}
            >
              {feedback.message}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Purchase Date */}
              <div>
                <label htmlFor="purchaseDate" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('purchase_date') || 'Purchase Date'}
                </label>
                <input
                  type="date"
                  id="purchaseDate"
                  name="purchaseDate"
                  value={formData.purchaseDate}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                />
              </div>

              {/* Quantity */}
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('quantity') || 'Quantity (Number of Chickens)'}
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleChange}
                  required
                  min="1"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_quantity') || 'Enter number of chickens'}
                />
              </div>

              {/* Price Per Chicken */}
              <div>
                <label htmlFor="pricePerChicken" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('price_per_chicken') || 'Price Per Chicken (AFN)'}
                </label>
                <input
                  type="number"
                  id="pricePerChicken"
                  name="pricePerChicken"
                  value={formData.pricePerChicken}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_price_per_chicken') || 'Enter price per chicken'}
                />
              </div>

              {/* Total Price (Auto-calculated) */}
              <div>
                <label htmlFor="totalPrice" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('total_price') || 'Total Price (AFN)'}
                </label>
                <input
                  type="number"
                  id="totalPrice"
                  name="totalPrice"
                  value={formData.totalPrice}
                  readOnly
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100 focus:outline-none dark:bg-gray-600 dark:text-white dark:border-gray-600"
                  placeholder={t('auto_calculated') || 'Auto-calculated'}
                />
                <small className="text-gray-500">
                  {t('total_price_auto_calculated') || 'This field is automatically calculated'}
                </small>
              </div>

              {/* Supplier Name */}
              <div>
                <label htmlFor="supplierName" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('supplier_name') || 'Supplier Name'}
                </label>
                <input
                  type="text"
                  id="supplierName"
                  name="supplierName"
                  value={formData.supplierName}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('enter_supplier_name') || 'Enter supplier name'}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Supplier Contact */}
              <div>
                <label htmlFor="supplierContact" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('supplier_contact') || 'Supplier Contact (Optional)'}
                </label>
                <input
                  type="tel"
                  id="supplierContact"
                  name="supplierContact"
                  value={formData.supplierContact}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="+93xxxxxxxxx"
                />
                <small className="text-gray-500">Format: +93xxxxxxxxx</small>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                {t('notes') || 'Notes (Optional)'}
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                placeholder={t('enter_notes') || 'Enter any additional notes about this purchase'}
                dir={language === 'ps' ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Submit Button */}
            <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate('/admin/chickens/purchases')}
              >
                {t('cancel') || 'Cancel'}
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <Save className="h-4 w-4" />
                {loading ? (t('creating_purchase') || 'Creating...') : (t('create_purchase') || 'Create Purchase')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddPurchasePage;
