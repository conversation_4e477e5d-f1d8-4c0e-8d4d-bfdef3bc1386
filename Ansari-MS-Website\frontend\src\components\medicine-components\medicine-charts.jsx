import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from './chart';

export function MedicineChart({ data }) {
  // Prepare data for the chart
  const chartData = data.slice(0, 6).map((medicine) => ({
    name: medicine.name.length > 10 ? `${medicine.name.substring(0, 10)}...` : medicine.name,
    quantity: medicine.quantity,
    value: medicine.price * medicine.quantity,
  }));

  return (
    <ChartContainer>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#eee" />
          <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} tick={{ fontSize: 12 }} />
          <YAxis yAxisId="left" orientation="left" stroke="#FF6B00" />
          <YAxis yAxisId="right" orientation="right" stroke="#2C3E50" />
          <Tooltip
            content={({ active, payload }) => {
              if (active && payload && payload.length) {
                return (
                  <ChartTooltip>
                    <ChartTooltipContent>
                      <div className="font-medium">{payload[0].payload.name}</div>
                      <div className="flex flex-col gap-1 mt-2">
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-[#FF6B00]" />
                          <div>Quantity: {payload[0].value}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-2 w-2 rounded-full bg-[#2C3E50]" />
                          <div>Value: ${payload[1].value}</div>
                        </div>
                      </div>
                    </ChartTooltipContent>
                  </ChartTooltip>
                );
              }
              return null;
            }}
          />
          <Legend />
          <Bar yAxisId="left" dataKey="quantity" fill="#FF6B00" name="Quantity" radius={[4, 4, 0, 0]} />
          <Bar yAxisId="right" dataKey="value" fill="#2C3E50" name="Value ($)" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}
