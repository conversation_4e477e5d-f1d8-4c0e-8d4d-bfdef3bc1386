'use client';
/* eslint-disable react/prop-types */
import { createContext, useState, useContext, useEffect } from 'react';

// Create the auth context
const AuthContext = createContext(null);

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  // Check for authentication token on mount
  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('authToken');
      if (token) {
        // You could also verify the token with your backend here
        setIsAuthenticated(true);
        // Try to get user data from localStorage
        try {
          const userData = JSON.parse(localStorage.getItem('user'));
          if (userData) {
            setUser(userData);
          } else {
            // Fallback to basic user info if no stored user data
            setUser({
              name: 'Admin User',
              role: 'admin',
            });
          }
        } catch (error) {
          console.error('Error setting user data:', error);
        }
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  // Login function
  const login = (token, userData) => {
    localStorage.setItem('authToken', token);
    setIsAuthenticated(true);
    setUser(userData || { name: 'Admin User', role: 'admin' });

    // Store user data in localStorage for persistence
    if (userData) {
      localStorage.setItem('user', JSON.stringify(userData));
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);
  };

  // Update user data function
  const updateUserData = (userData) => {
    setUser(userData);
    if (userData) {
      localStorage.setItem('user', JSON.stringify(userData));
    }
  };

  // Auth context value
  const value = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    updateUserData,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

///////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////
// /* eslint-disable react-refresh/only-export-components */
// /* eslint-disable react/prop-types */
// "use client"

// import { createContext, useState, useContext, useEffect } from "react"

// // Create the auth context
// const AuthContext = createContext(null)

// // Auth provider component
// export const AuthProvider = ({ children }) => {
//   const [isAuthenticated, setIsAuthenticated] = useState(false)
//   const [isLoading, setIsLoading] = useState(true)
//   const [user, setUser] = useState(null)

//   // Check for authentication token on mount
//   useEffect(() => {
//     const checkAuth = () => {
//       const token = localStorage.getItem("authToken")
//       if (token) {
//         // You could also verify the token with your backend here
//         setIsAuthenticated(true)
//         // Set basic user info (you could decode JWT or fetch from API)
//         try {
//           // For now, just set a basic user object
//           // In a real app, you might decode the JWT or fetch user data
//           setUser({
//             name: "Admin User",
//             role: "admin",
//           })
//         } catch (error) {
//           console.error("Error setting user data:", error)
//         }
//       } else {
//         setIsAuthenticated(false)
//         setUser(null)
//       }
//       setIsLoading(false)
//     }

//     checkAuth()
//   }, [])

//   // Login function
//   const login = (token, userData) => {
//     localStorage.setItem("authToken", token)
//     setIsAuthenticated(true)
//     setUser(userData || { name: "Admin User", role: "admin" })
//   }

//   // Logout function
//   const logout = () => {
//     localStorage.removeItem("authToken")
//     setIsAuthenticated(false)
//     setUser(null)
//   }

//   // Auth context value
//   const value = {
//     isAuthenticated,
//     isLoading,
//     user,
//     login,
//     logout,
//   }

//   return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
// }

// // Custom hook to use the auth context
// export const useAuth = () => {
//   const context = useContext(AuthContext)
//   if (!context) {
//     throw new Error("useAuth must be used within an AuthProvider")
//   }
//   return context
// }
