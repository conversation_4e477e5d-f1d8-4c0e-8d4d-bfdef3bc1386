import Jo<PERSON> from "joi";
import asyncHand<PERSON> from "../middlewares/asyncHandler.js";
import DrugsModel from "../models/drugsModel.js";

const drugSchema = Joi.object({
  D_Name: Joi.string().min(3).required(),
  D_Price: Joi.number().min(1).required(),
  D_SalesPrice: Joi.number().min(Joi.ref("D_Price")).required(),
  D_StartDate: Joi.date().optional(),
  D_ExpireDate: Joi.date().min(Joi.ref("D_StartDate")).required(),
  Lid: Joi.number().required(),
});

const DrugsController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = drugSchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    const drug = await DrugsModel.create(value);
    res
      .status(201)
      .json({ success: true, message: "Drug added successfully", drug });
  }),

  getAll: asyncHandler(async (req, res) => {
    const drugs = await DrugsModel.getAll();
    res.json({ success: true, total: drugs.length, data: drugs });
  }),

  getById: asyncHandler(async (req, res) => {
    const drug = await DrugsModel.getById(req.params.id);
    if (!drug)
      return res.status(404).json({ success: false, error: "Drug not found" });
    res.json({ success: true, data: drug });
  }),

  update: asyncHandler(async (req, res) => {
    const existing = await DrugsModel.getById(req.params.id);
    if (!existing)
      return res.status(404).json({ success: false, error: "Drug not found" });

    const fullData = {
      D_Name: req.body.D_Name ?? existing.D_Name,
      D_Price: req.body.D_Price ?? existing.D_Price,
      D_SalesPrice: req.body.D_SalesPrice ?? existing.D_SalesPrice,
      D_StartDate: req.body.D_StartDate ?? existing.D_StartDate,
      D_ExpireDate: req.body.D_ExpireDate ?? existing.D_ExpireDate,
      Lid: req.body.Lid ?? existing.Lid,
    };

    const { error } = drugSchema.validate(fullData);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const updated = await DrugsModel.update(req.params.id, fullData);
    res.json({
      success: true,
      message: "Drug updated successfully",
      drug: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const result = await DrugsModel.delete(req.params.id);
    if (result.affectedRows === 0)
      return res.status(404).json({ success: false, error: "Drug not found" });

    res.json({ success: true, message: "Drug deleted successfully" });
  }),
};

export default DrugsController;
