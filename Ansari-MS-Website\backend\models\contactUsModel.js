import db from "../config/db.js";

const ContactUsModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO ContactUs (C_Title, C_Body, user_name, user_email, user_phone) VALUES (?, ?, ?, ?, ?)`,
      [
        data.C_Title,
        data.C_Body,
        data.user_name,
        data.user_email,
        data.user_phone,
      ],
    );

    const [newContact] = await db.query(
      `SELECT * FROM ContactUs WHERE C_Id = ?`,
      [result.insertId],
    );

    return newContact[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM ContactUs");
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM ContactUs WHERE C_Id = ?", [
      id,
    ]);
    return rows[0];
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE ContactUs SET C_Title = ?, C_Body = ?, user_name = ?, user_email = ?, user_phone = ? WHERE C_Id = ?`,
      [
        data.C_Title,
        data.C_Body,
        data.user_name,
        data.user_email,
        data.user_phone,
        id,
      ],
    );

    if (result.affectedRows > 0) {
      const [updatedContact] = await db.query(
        `SELECT * FROM ContactUs WHERE C_Id = ?`,
        [id],
      );
      return updatedContact[0];
    }
    return null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM ContactUs WHERE C_Id = ?", [
      id,
    ]);
    return result;
  },
};

export default ContactUsModel;
