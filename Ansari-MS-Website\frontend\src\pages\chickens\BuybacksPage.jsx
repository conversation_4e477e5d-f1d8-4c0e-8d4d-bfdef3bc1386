import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, MoreVertical, Trash, Store, Calendar, DollarSign, Package } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import Link from '../../components/feed-components/Link';

const BuybacksPage = () => {
  const navigate = useNavigate();
  const { buybacks, fetchBuybacks, deleteBuyback, loading } = useChicken();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    fetchBuybacks();
  }, []);

  const filteredBuybacks = buybacks.filter((buyback) =>
    buyback.farmName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    buyback.farmOwner.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id) => {
    if (window.confirm(t('confirm_delete_buyback') || 'Are you sure you want to delete this buyback?')) {
      try {
        await deleteBuyback(id);
      } catch (error) {
        console.error('Error deleting buyback:', error);
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    const numericAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  const getStatusBadge = (buyback) => {
    if (buyback.status === 'fully_distributed') {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Fully Distributed</span>;
    } else if (buyback.status === 'partially_distributed') {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Partially Distributed</span>;
    } else {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Available</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_buybacks') || 'Loading buybacks...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('chicken_buybacks') || 'Chicken Buybacks'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_chicken_buybacks_description') || 'Manage chickens bought back from farms after 45 days'}
          </p>
        </div>
        <div className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Link 
            to="/admin/chickens" 
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('back_to_dashboard') || 'Back to Dashboard'}
          </Link>
          <Link 
            to="/admin/chickens/buybacks/add" 
            className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
            <span>{t('buyback_from_farm') || 'Buyback from Farm'}</span>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('search_buybacks') || 'Search buybacks by farm name or owner...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_buybacks') || 'Total Buybacks'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredBuybacks.length}
                </h3>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Calendar className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_chickens') || 'Total Chickens'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {filteredBuybacks.reduce((sum, buyback) => sum + buyback.quantity, 0)}
                </h3>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <Package className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('total_amount') || 'Total Amount'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(filteredBuybacks.reduce((sum, buyback) => {
                    const totalPrice = parseFloat(buyback.totalPrice) || 0;
                    return sum + totalPrice;
                  }, 0))}
                </h3>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Buybacks Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('buybacks_list') || 'Buybacks List'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('buyback_date') || 'Buyback Date'}</TableHead>
                  <TableHead>{t('farm') || 'Farm'}</TableHead>
                  <TableHead>{t('quantity') || 'Quantity'}</TableHead>
                  <TableHead>{t('price_per_chicken') || 'Price/Chicken'}</TableHead>
                  <TableHead>{t('total_price') || 'Total Price'}</TableHead>
                  <TableHead>{t('days_completed') || 'Days Completed'}</TableHead>
                  <TableHead>{t('status') || 'Status'}</TableHead>
                  <TableHead>{t('distributed') || 'Distributed'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBuybacks.length > 0 ? (
                  filteredBuybacks.map((buyback) => (
                    <TableRow key={buyback.id}>
                      <TableCell>{formatDate(buyback.buybackDate)}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{buyback.farmName}</div>
                          <div className="text-sm text-gray-500">{buyback.farmOwner}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{buyback.quantity}</TableCell>
                      <TableCell>{formatCurrency(buyback.pricePerChicken)}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(buyback.totalPrice)}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          buyback.daysCompleted >= 45 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {buyback.daysCompleted} days
                        </span>
                      </TableCell>
                      <TableCell>{getStatusBadge(buyback)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{buyback.distributedQuantity || 0} / {buyback.quantity}</div>
                          <div className="text-gray-500">
                            {buyback.remainingQuantity || buyback.quantity} remaining
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem 
                              onClick={() => navigate(`/admin/chickens/distributions/add?buybackId=${buyback.id}`)}
                              disabled={buyback.remainingQuantity <= 0}
                            >
                              <Store className={`h-4 w-4 text-purple-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('distribute_to_shop') || 'Distribute to Shop'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(buyback.id)}>
                              <Trash className={`h-4 w-4 text-red-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      {t('no_buybacks_found') || 'No buybacks found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BuybacksPage;
