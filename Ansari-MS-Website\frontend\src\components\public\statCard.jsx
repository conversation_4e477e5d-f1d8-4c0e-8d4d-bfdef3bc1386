import { useEffect, useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';

// 🔢 Convert 0-9 to Pashto digits
const convertToPashtoDigits = (input) => {
  const pashtoDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  return input.toString().replace(/\d/g, d => pashtoDigits[parseInt(d)]);
};

const StatCard = ({ targetValue, label, lang = 'en' }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });
  const [displayValue, setDisplayValue] = useState('');

  useEffect(() => {
    if (!isInView) return;

    const duration = 8000;
    const stepTime = 30;

    const animateValue = (from, to, suffix = '', isRange = false, rangeMax = '') => {
      let current = from;
      const steps = Math.ceil(duration / stepTime);
      const increment = (to - from) / steps;

      const timer = setInterval(() => {
        current += increment;

        if (current >= to) {
          const result = isRange ? `${to}-${rangeMax}` : `${to}${suffix}`;
          setDisplayValue(lang === 'ps' ? convertToPashtoDigits(result) : result);
          clearInterval(timer);
        } else {
          const result = isRange
            ? `${Math.floor(current)}-${rangeMax}`
            : `${Math.floor(current)}${suffix}`;
          setDisplayValue(lang === 'ps' ? convertToPashtoDigits(result) : result);
        }
      }, stepTime);

      return () => clearInterval(timer);
    };

    // === Handle "5-7" format ===
    if (/^\d+-\d+$/.test(targetValue)) {
      const [min, max] = targetValue.split('-').map(Number);
      return animateValue(0, min, '', true, max);
    }

    // === Handle "24h" format ===
    if (/^\d+h$/.test(targetValue)) {
      const num = parseInt(targetValue);
      return animateValue(0, num, 'h');
    }

    // === Handle numeric + suffix like "250+" ===
    const numeric = parseInt(targetValue.replace(/\D/g, ''));
    const suffix = targetValue.replace(/\d+/g, '');
    if (!isNaN(numeric)) {
      return animateValue(0, numeric, suffix);
    }

    // === Non-numeric fallback ===
    setDisplayValue(lang === 'ps' ? convertToPashtoDigits(targetValue) : targetValue);
  }, [isInView, targetValue, lang]);

  return (
    <motion.div
      ref={ref}
      lang={lang}
      className="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, delay: 0.2 }}
    >
      <div className="text-3xl font-bold text-primary">{displayValue}</div>
      <div className="text-sm text-text-primary">{label}</div>
    </motion.div>
  );
};

export default StatCard;


/////////////////////////////////
// import { useEffect, useRef, useState } from 'react';
// import { motion, useInView } from 'framer-motion';

// const StatCard = ({ targetValue, label, lang = 'en' }) => {
//   const ref = useRef(null);
//   const isInView = useInView(ref, { once: true });
//   const [value, setValue] = useState(0);

//   useEffect(() => {
//     if (!isInView) return;

//     const duration = 8000; // Slower animation duration in ms
//     const stepTime = 30;   // Smoother stepping

//     // === Handle "5-7" format ===
//     if (/^\d+-\d+$/.test(targetValue)) {
//       const [min, max] = targetValue.split('-').map(Number);
//       let current = 1;
//       const steps = Math.ceil(duration / stepTime);
//       const increment = min / steps;

//       const timer = setInterval(() => {
//         current += increment;
//         if (current >= min) {
//           setValue(`${min}-${max}`);
//           clearInterval(timer);
//         } else {
//           setValue(`${Math.floor(current)}-${max}`);
//         }
//       }, stepTime);

//       return () => clearInterval(timer);
//     }

//     // === Handle "24h" format ===
//     if (/^\d+h$/.test(targetValue)) {
//       const num = parseInt(targetValue);
//       let current = 1;
//       const steps = Math.ceil(duration / stepTime);
//       const increment = num / steps;

//       const timer = setInterval(() => {
//         current += increment;
//         if (current >= num) {
//           setValue(`${num}h`);
//           clearInterval(timer);
//         } else {
//           setValue(`${Math.floor(current)}h`);
//         }
//       }, stepTime);

//       return () => clearInterval(timer);
//     }

//     // === Handle numeric + suffix (like "250+" or "1000k") ===
//     const numeric = parseInt(targetValue.replace(/\D/g, ''));
//     const suffix = targetValue.replace(/\d+/g, '');
//     if (isNaN(numeric)) {
//       setValue(targetValue);
//       return;
//     }

//     let current = 0;
//     const steps = Math.ceil(duration / stepTime);
//     const increment = numeric / steps;

//     const timer = setInterval(() => {
//       current += increment;
//       if (current >= numeric) {
//         setValue(`${numeric}${suffix}`);
//         clearInterval(timer);
//       } else {
//         setValue(`${Math.floor(current)}${suffix}`);
//       }
//     }, stepTime);

//     return () => clearInterval(timer);
//   }, [isInView, targetValue]);

//   return (
//     <motion.div
//       ref={ref}
//       lang={lang} // This applies RTL and font if managed in global styles
//       className="bg-white p-6 rounded-xl shadow-md flex flex-col items-center text-center"
//       initial={{ opacity: 0, y: 20 }}
//       whileInView={{ opacity: 1, y: 0 }}
//       viewport={{ once: true }}
//       transition={{ duration: 0.8, delay: 0.2 }}
//     >
//       <div className="text-3xl font-bold text-primary">{value}</div>
//       <div className="text-sm text-text-primary">{label}</div>
//     </motion.div>
//   );
// };

// export default StatCard;

