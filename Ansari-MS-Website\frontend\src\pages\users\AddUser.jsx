"use client";

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Save, X } from "lucide-react";
import FileUploadComponent from "../../components/file-upload";
import { useUsers } from "../../contexts/UsersContext";

const AddUser = () => {
  const navigate = useNavigate();
  const { addUser, loading: apiLoading } = useUsers();

  const [formData, setFormData] = useState({
    U_FirstName: "",
    U_LastName: "",
    U_Email: "",
    password: "",
    confirmPassword: "",
    address: "",
    Role: "user",
    status: "active",
  });

  const [avatar, setAvatar] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: "", message: "" });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: "", message: "" });
  };

  const handleFileChange = (file) => {
    setAvatar(file);
    // Don't add to formData - handle separately in submit
  };

  const validateForm = () => {
    // First name: required, min 3, max 25
    if (!formData.U_FirstName.trim()) throw new Error("First name is required");
    if (formData.U_FirstName.length < 3 || formData.U_FirstName.length > 25)
      throw new Error("First name must be between 3 and 25 characters");

    // Last name: optional, but if provided, must be min 3, max 25
    if (
      formData.U_LastName &&
      (formData.U_LastName.length < 3 || formData.U_LastName.length > 25)
    )
      throw new Error("Last name must be between 3 and 25 characters");

    // Email: required, must be valid email format
    if (!formData.U_Email.trim()) throw new Error("Email is required");
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.U_Email))
      throw new Error("Invalid email format");

    // Password: required, min 6
    if (!formData.password) throw new Error("Password is required");
    if (formData.password.length < 6)
      throw new Error("Password must be at least 6 characters");

    // Confirm password: must match
    if (formData.password !== formData.confirmPassword)
      throw new Error("Passwords do not match");

    // Address: optional but max 255 characters
    if (formData.address && formData.address.length > 90)
      throw new Error("Address must be 80 characters or less");

    // Role: must be one of allowed values
    const validRoles = ["user", "admin", "farmer", "shopper"];
    if (!formData.Role || !validRoles.includes(formData.Role))
      throw new Error("Invalid role selected");

    // Status: must be present (frontend required field)
    if (!formData.status) throw new Error("Status is required");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: "", message: "" });

    try {
      validateForm();

      const userData = {
        U_FirstName: formData.U_FirstName,
        U_LastName: formData.U_LastName || "",
        U_Email: formData.U_Email,
        password: formData.password,
        address: formData.address || "kandahar",
        Role: formData.Role,
        status: formData.status,
      };

      // Only add image if a file was uploaded, otherwise let backend use default
      if (avatar && avatar instanceof File) {
        userData.image = avatar;
        console.log("Adding user with image:", avatar.name, avatar.size, "bytes");
      } else {
        console.log("Adding user without image - will use default");
      }
      // If no image is uploaded, don't include the image field - backend will use default

      console.log("User data being sent:", { ...userData, image: userData.image ? "File object" : "No image" });
      const response = await addUser(userData);

      if (response.success) {
        setFeedback({
          type: "success",
          message: response.message || "User added successfully!",
        });

        // Delay navigation to show success message
        setTimeout(() => {
          navigate("/admin/users");
        }, 1500);
      } else {
        setFeedback({
          type: "error",
          message: response.error || "Failed to add user. Please try again.",
        });
      }
    } catch (error) {
      console.error("Error adding user:", error);
      setFeedback({
        type: "error",
        message: error.message || "Failed to add user. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-[#2C3E50]">Add User</h1>
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={() => navigate("/admin/users")}
            className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center"
          >
            <X size={18} className="mr-2" />
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading || apiLoading}
            className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg hover:bg-[#D32F2F] transition-colors flex items-center"
          >
            {loading || apiLoading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <Save size={18} className="mr-2" />
                Save User
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        {feedback.message && (
          <div
            className={`mb-6 p-4 rounded-lg ${
              feedback.type === "success"
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"
            }`}
          >
            {feedback.message}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2 space-y-6">
              <div>
                <label
                  htmlFor="U_FirstName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  First Name
                </label>
                <input
                  type="text"
                  id="U_FirstName"
                  name="U_FirstName"
                  value={formData.U_FirstName}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 border ${
                    feedback.type === "error"
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                  placeholder="Enter first name"
                />
              </div>

              <div>
                <label
                  htmlFor="U_LastName"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Last Name
                </label>
                <input
                  type="text"
                  id="U_LastName"
                  name="U_LastName"
                  value={formData.U_LastName}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 border ${
                    feedback.type === "error"
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                  placeholder="Enter last name"
                />
              </div>

              <div>
                <label
                  htmlFor="address"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  placeholder="Enter user address"
                />
              </div>

              <div>
                <label
                  htmlFor="U_Email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Email Address
                </label>
                <input
                  type="email"
                  id="U_Email"
                  name="U_Email"
                  value={formData.U_Email}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 border ${
                    feedback.type === "error"
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 border ${
                    feedback.type === "error"
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                  placeholder="••••••••"
                />
              </div>

              <div>
                <label
                  htmlFor="confirmPassword"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Confirm Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className={`w-full px-4 py-2 border ${
                    feedback.type === "error"
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
                  placeholder="••••••••"
                />
              </div>

              <div>
                <label
                  htmlFor="Role"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Role
                </label>
                <select
                  id="Role"
                  name="Role"
                  value={formData.Role}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                >
                  <option value="admin">Admin</option>
                  <option value="user">User</option>
                  <option value="farmer">Farmer</option>
                  <option value="shopper">Shopper</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center">
                    <input
                      id="status-active"
                      name="status"
                      type="radio"
                      value="active"
                      checked={formData.status === "active"}
                      onChange={handleChange}
                      className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                    />
                    <label
                      htmlFor="status-active"
                      className="ml-3 block text-sm font-medium text-gray-700"
                    >
                      Active
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="status-inactive"
                      name="status"
                      type="radio"
                      value="inactive"
                      checked={formData.status === "inactive"}
                      onChange={handleChange}
                      className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                    />
                    <label
                      htmlFor="status-inactive"
                      className="ml-3 block text-sm font-medium text-gray-700"
                    >
                      Inactive
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium text-[#2C3E50] mb-4">
                  Profile Picture
                </h2>
                <FileUploadComponent
                  onFileChange={handleFileChange}
                  previewImage={previewImage}
                  setPreviewImage={setPreviewImage}
                />
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddUser;


///////////// / ///////
// "use client"

// import { useState } from "react"
// import { useNavigate } from "react-router-dom"
// import { Save, X } from "lucide-react"
// import FileUploadComponent from "../../components/file-upload"

// const AddUser = () => {
//   const navigate = useNavigate()
//   const [formData, setFormData] = useState({
//     firstName: "",
//     lastName: "",
//     email: "",
//     password: "",
//     confirmPassword: "",
//     role: "staff",
//     status: "active",
//     address: "",
//   })
//   const [avatar, setAvatar] = useState(null)
//   const [previewImage, setPreviewImage] = useState(null)
//   const [loading, setLoading] = useState(false)
//   const [feedback, setFeedback] = useState({ type: "", message: "" })

//   const handleChange = (e) => {
//     const { name, value } = e.target
//     setFormData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))
//     setFeedback({ type: "", message: "" })
//   }

//   const handleFileChange = (file) => {
//     setAvatar(file)
//   }

//   // const validateForm = () => {
//   //   if (!formData.firstName.trim()) throw new Error("First name is required")
//   //   if (!formData.lastName.trim()) throw new Error("Last name is required")
//   //   if (!formData.email.trim()) throw new Error("Email is required")
//   //   if (!formData.password) throw new Error("Password is required")
//   //     if (!formData.address.trim()) throw new Error("Address is required");
//   //   if (formData.password !== formData.confirmPassword) {
//   //     throw new Error("Passwords do not match")
//   //   }
//   //   if (!formData.role) throw new Error("Role is required")
//   //   if (!formData.status) throw new Error("Status is required")
//   // }
//   const validateForm = () => {
//   // First name: required, min 3, max 25
//   if (!formData.firstName.trim()) throw new Error("First name is required");
//   if (formData.firstName.length < 3 || formData.firstName.length > 25)
//     throw new Error("First name must be between 3 and 25 characters");

//   // Last name: optional, but if provided, must be min 3, max 25
//   if (formData.lastName && (formData.lastName.length < 3 || formData.lastName.length > 25))
//     throw new Error("Last name must be between 3 and 25 characters");

//   // Email: required, must be valid email format
//   if (!formData.email.trim()) throw new Error("Email is required");
//   const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//   if (!emailRegex.test(formData.email)) throw new Error("Invalid email format");

//   // Password: required, min 6
//   if (!formData.password) throw new Error("Password is required");
//   if (formData.password.length < 6)
//     throw new Error("Password must be at least 6 characters");

//   // Confirm password: must match
//   if (formData.password !== formData.confirmPassword)
//     throw new Error("Passwords do not match");

//   // Address: optional but max 255 characters
//   if (formData.address && formData.address.length > 90)
//     throw new Error("Address must be 80 characters or less");

//   // Role: must be one of allowed values
//   const validRoles = ["user", "admin", "farmer", "shopper"];
//   if (!formData.role || !validRoles.includes(formData.role))
//     throw new Error("Invalid role selected");

//   // Status: must be present (frontend required field)
//   if (!formData.status) throw new Error("Status is required");
// };


//   const handleSubmit = async (e) => {
//     e.preventDefault()
//     setLoading(true)
//     setFeedback({ type: "", message: "" })

//     try {
//       validateForm()

//       const userData = {
//         ...formData,
//         avatar,
//         createdAt: new Date().toISOString(),
//       }

//       // Log the data being submitted
//       console.log("Adding new user with data:", {
//         firstName: userData.firstName,
//         lastName: userData.lastName,
//         email: userData.email,
//         role: userData.role,
//         status: userData.status,
//         avatarSize: userData.avatar ? `${Math.round(userData.avatar.size / 1024)} KB` : "No avatar",
//       })

//       // In a real app, you would call your API to add the user
//       // Simulating API call with a timeout
//       setTimeout(() => {
//         console.log("User added successfully:", `${userData.firstName} ${userData.lastName}`)

//         setFeedback({
//           type: "success",
//           message: "User added successfully!",
//         })

//         // Delay navigation to show success message
//         setTimeout(() => {
//           navigate("/admin/users")
//         }, 1500)
//       }, 1000)
//     } catch (error) {
//       console.error("Error adding user:", error)
//       setFeedback({
//         type: "error",
//         message: error.message || "Failed to add user. Please try again.",
//       })
//     } finally {
//       setLoading(false)
//     }
//   }

//   return (
//     <div>
//       <div className="flex justify-between items-center mb-6">
//         <h1 className="text-2xl font-bold text-[#2C3E50]">Add User</h1>
//         <div className="flex space-x-3">
//           <button
//             type="button"
//             onClick={() => navigate("/admin/users")}
//             className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center"
//           >
//             <X size={18} className="mr-2" />
//             Cancel
//           </button>
//           <button
//             onClick={handleSubmit}
//             disabled={loading}
//             className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg hover:bg-[#D32F2F] transition-colors flex items-center"
//           >
//             {loading ? (
//               <>
//                 <svg
//                   className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
//                   xmlns="http://www.w3.org/2000/svg"
//                   fill="none"
//                   viewBox="0 0 24 24"
//                 >
//                   <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                   <path
//                     className="opacity-75"
//                     fill="currentColor"
//                     d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
//                   ></path>
//                 </svg>
//                 Saving...
//               </>
//             ) : (
//               <>
//                 <Save size={18} className="mr-2" />
//                 Save User
//               </>
//             )}
//           </button>
//         </div>
//       </div>

//       <div className="bg-white rounded-lg shadow-md p-6">
//         {feedback.message && (
//           <div
//             className={`mb-6 p-4 rounded-lg ${
//               feedback.type === "success" ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
//             }`}
//           >
//             {feedback.message}
//           </div>
//         )}

//         <form onSubmit={handleSubmit}>
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//             <div className="md:col-span-2 space-y-6">
//               <div>
//                 <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
//                   First Name
//                 </label>
//                 <input
//                   type="text"
//                   id="firstName"
//                   name="firstName"
//                   value={formData.firstName}
//                   onChange={handleChange}
//                   className={`w-full px-4 py-2 border ${
//                     feedback.type === "error" ? "border-red-500" : "border-gray-300"
//                   } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                   placeholder="Enter first name"
//                 />
//               </div>

//               <div>
//                 <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
//                   Last Name
//                 </label>
//                 <input
//                   type="text"
//                   id="lastName"
//                   name="lastName"
//                   value={formData.lastName}
//                   onChange={handleChange}
//                   className={`w-full px-4 py-2 border ${
//                     feedback.type === "error" ? "border-red-500" : "border-gray-300"
//                   } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                   placeholder="Enter last name"
//                 />
//               </div>

//                <div>
//               <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
//                     Address
//                   </label>
//                   <input
//                     type="text"
//                     id="address"
//                     name="address"
//                     value={formData.address}
//                     onChange={handleChange}
//                     className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                     placeholder="Enter user address"
//                   />
//                 </div>


//               <div>
//                 <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
//                   Email Address
//                 </label>
//                 <input
//                   type="email"
//                   id="email"
//                   name="email"
//                   value={formData.email}
//                   onChange={handleChange}
//                   className={`w-full px-4 py-2 border ${
//                     feedback.type === "error" ? "border-red-500" : "border-gray-300"
//                   } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                   placeholder="<EMAIL>"
//                 />
//               </div>

//               <div>
//                 <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
//                   Password
//                 </label>
//                 <input
//                   type="password"
//                   id="password"
//                   name="password"
//                   value={formData.password}
//                   onChange={handleChange}
//                   className={`w-full px-4 py-2 border ${
//                     feedback.type === "error" ? "border-red-500" : "border-gray-300"
//                   } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                   placeholder="••••••••"
//                 />
//               </div>

//               <div>
//                 <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
//                   Confirm Password
//                 </label>
//                 <input
//                   type="password"
//                   id="confirmPassword"
//                   name="confirmPassword"
//                   value={formData.confirmPassword}
//                   onChange={handleChange}
//                   className={`w-full px-4 py-2 border ${
//                     feedback.type === "error" ? "border-red-500" : "border-gray-300"
//                   } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]`}
//                   placeholder="••••••••"
//                 />
//               </div>

//               <div>
//                 <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
//                   Role
//                 </label>
//                 <select
//                   id="role"
//                   name="role"
//                   value={formData.role}
//                   onChange={handleChange}
//                   className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                 >
//                   <option value="admin">Admin</option>
//                   <option value="user">User</option>
//                   <option value="farmer">Farmer</option>
//                   <option value="shopper">Shopper</option>
//                 </select>
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
//                 <div className="mt-2 space-y-2">
//                   <div className="flex items-center">
//                     <input
//                       id="status-active"
//                       name="status"
//                       type="radio"
//                       value="active"
//                       checked={formData.status === "active"}
//                       onChange={handleChange}
//                       className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                     />
//                     <label htmlFor="status-active" className="ml-3 block text-sm font-medium text-gray-700">
//                       Active
//                     </label>
//                   </div>
//                   <div className="flex items-center">
//                     <input
//                       id="status-inactive"
//                       name="status"
//                       type="radio"
//                       value="inactive"
//                       checked={formData.status === "inactive"}
//                       onChange={handleChange}
//                       className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                     />
//                     <label htmlFor="status-inactive" className="ml-3 block text-sm font-medium text-gray-700">
//                       Inactive
//                     </label>
//                   </div>
//                 </div>
//               </div>
//             </div>

//             <div className="space-y-6">
//               <div>
//                 <h2 className="text-lg font-medium text-[#2C3E50] mb-4">Profile Picture</h2>
//                 <FileUploadComponent
//                   onFileChange={handleFileChange}
//                   previewImage={previewImage}
//                   setPreviewImage={setPreviewImage}
//                 />
//               </div>
//             </div>
//           </div>
//         </form>
//       </div>
//     </div>
//   )
// }

// export default AddUser
