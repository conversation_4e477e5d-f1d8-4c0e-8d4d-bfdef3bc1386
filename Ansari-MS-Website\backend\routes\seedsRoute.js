import express from "express";
import seedsController from "../controllers/seedsController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/", authenticate, seedsController.create);
router.get("/", seedsController.getAll);
router.get("/:id", seedsController.getById);
router.put("/:id", authenticate, seedsController.update);
router.delete("/:id", authenticate, authorizeAdmin, seedsController.delete);

export default router;
