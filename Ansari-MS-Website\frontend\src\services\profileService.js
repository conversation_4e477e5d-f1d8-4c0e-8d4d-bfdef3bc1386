// Profile API service
const API_BASE_URL = 'http://localhost:5432/api/v1';

class ProfileService {
  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('authToken');
  }

  // Get current user's profile
  async getCurrentUserProfile() {
    try {
      const token = this.getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE_URL}/users/profile/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch profile');
      }

      return data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  // Update current user's profile
  async updateCurrentUserProfile(profileData, imageFile = null) {
    try {
      const token = this.getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Create FormData for file upload support
      const formData = new FormData();
      
      // Add profile data to FormData
      Object.keys(profileData).forEach(key => {
        if (profileData[key] !== null && profileData[key] !== undefined) {
          // Convert frontend field names to backend field names
          if (key === 'firstName') {
            formData.append('U_FirstName', profileData[key]);
          } else if (key === 'lastName') {
            formData.append('U_LastName', profileData[key]);
          } else if (key === 'email') {
            formData.append('U_Email', profileData[key]);
          } else if (key !== 'joinDate') { // Exclude joinDate from updates
            formData.append(key, profileData[key]);
          }
        }
      });

      // Add image file if provided
      if (imageFile) {
        formData.append('image', imageFile);
      }

      const response = await fetch(`${API_BASE_URL}/users/profile/me`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          // Don't set Content-Type header for FormData, let browser set it
        },
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile');
      }

      return data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Validate required fields
  validateProfileData(profileData) {
    const errors = {};

    // Check required fields
    if (!profileData.firstName || !profileData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!profileData.lastName || !profileData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!profileData.email || !profileData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(profileData.email)) {
      errors.email = 'Email format is invalid';
    }

    // Check for empty fields and show clear messages
    const fieldMessages = {
      phone: 'Phone number is not provided',
      address: 'Address is not provided',
      bio: 'Bio is not provided',
      birthDate: 'Birth date is not provided'
    };

    Object.keys(fieldMessages).forEach(field => {
      if (!profileData[field] || !profileData[field].trim()) {
        errors[field] = fieldMessages[field];
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}

// Export singleton instance
const profileService = new ProfileService();
export default profileService;
