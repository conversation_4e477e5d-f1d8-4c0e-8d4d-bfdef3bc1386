/* eslint-disable react/prop-types */
import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import clsx from 'clsx';

const LinkButton = React.forwardRef(
  (
    {
      to,
      children,
      variant = 'primary', // "primary" | "secondary" | "outline"
      disabled = false,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseStyles =
      'inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';

    const variants = {
      primary: 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500',
      secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-300',
      outline: 'border border-gray-300 text-gray-700 hover:bg-gray-100 focus:ring-gray-300',
    };

    return (
      <RouterLink
        to={disabled ? '#' : to}
        ref={ref}
        className={clsx(baseStyles, variants[variant], disabled && 'opacity-50 pointer-events-none', className)}
        {...props}
      >
        {children}
      </RouterLink>
    );
  }
);

LinkButton.displayName = 'LinkButton';

export default LinkButton;

////////////////
// import React from "react";
// import { Link as RouterLink } from "react-router-dom";
// import clsx from "clsx";

// const Link = React.forwardRef(({ to, className = "", children, ...props }, ref) => {
//   return (
//     <RouterLink
//       to={to}
//       ref={ref}
//       className={clsx(
//         "text-orange-600 hover:underline hover:text-orange-700 transition font-medium",
//         className
//       )}
//       {...props}
//     >
//       {children}
//     </RouterLink>
//   );
// });

// Link.displayName = "Link";

// export default Link;
