import React from 'react';
import { ChevronDown } from 'lucide-react';

const Accordion = React.forwardRef(({ children, className = '', ...props }, ref) => {
  return (
    <div ref={ref} className={`divide-y divide-gray-200 dark:divide-gray-700 ${className}`} {...props}>
      {children}
    </div>
  );
});

const AccordionItem = React.forwardRef(
  ({ title, children, isOpen = false, onToggle, className = '', ...props }, ref) => {
    return (
      <div ref={ref} className={`py-4 ${className}`} {...props}>
        <button className="flex w-full items-center justify-between text-left" onClick={onToggle}>
          <span className="text-sm font-medium text-gray-900 dark:text-white">{title}</span>
          <ChevronDown
            className={`h-5 w-5 text-gray-500 dark:text-gray-400 transform transition-transform ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </button>
        <div className={`mt-2 overflow-hidden transition-all duration-200 ${isOpen ? 'max-h-96' : 'max-h-0'}`}>
          <div className="text-sm text-gray-500 dark:text-gray-400">{children}</div>
        </div>
      </div>
    );
  }
);

Accordion.Item = AccordionItem;

Accordion.displayName = 'Accordion';
AccordionItem.displayName = 'Accordion.Item';

export default Accordion;
