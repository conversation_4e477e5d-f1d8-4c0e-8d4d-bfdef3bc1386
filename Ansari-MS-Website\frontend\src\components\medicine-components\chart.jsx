export const ChartContainer = ({ children }) => {
  return <div className="chart-container w-full h-full">{children}</div>;
};

export const ChartTooltip = ({ children }) => {
  return <div className="bg-white shadow-lg rounded-md border border-gray-200 p-2 text-sm">{children}</div>;
};

export const ChartTooltipContent = ({ children }) => {
  return <div className="chart-tooltip-content">{children}</div>;
};

export const ChartLegend = ({ children }) => {
  return <div className="chart-legend flex justify-center mt-4">{children}</div>;
};

export const ChartLegendContent = ({ children }) => {
  return <div className="chart-legend-content flex gap-4">{children}</div>;
};
