"use client"

import React, { useState, useEffect, useRef, useMemo, useCallback } from "react"
import { useNavigate, Link } from "react-router-dom"
import { useNews } from "../../contexts/NewsContext"
import { Edit, Trash2, Search, Plus, Eye, MoreVertical } from "lucide-react"

const ManageNews = () => {
  const navigate = useNavigate()
  const { news, deleteNews, fetchNews, loading: newsLoading } = useNews()
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [newsToDelete, setNewsToDelete] = useState(null)
  const [loading, setLoading] = useState(false)
  const [activeMenu, setActiveMenu] = useState(null)
  const menuRef = useRef(null)
  const [feedback, setFeedback] = useState({ type: "", message: "" })

  // Fetch news on component mount
  useEffect(() => {
    const loadNews = async () => {
      try {
        setLoading(true)
        await fetchNews()
      } catch (error) {
        console.error("Error loading news:", error)
        setFeedback({
          type: "error",
          message: "Error loading news. Please try again.",
        })
      } finally {
        setLoading(false)
      }
    }

    loadNews()
  }, [])

  // Handle search
  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1)
  }, [])

  // Handle view
  const handleView = useCallback(
    (newsId) => {
      try {
        navigate(`/news/${newsId}`)
        setActiveMenu(null)
      } catch (error) {
        console.error("Error navigating to view:", error)
        setFeedback({
          type: "error",
          message: "Error viewing news item. Please try again.",
        })
      }
    },
    [navigate],
  )

  // Handle edit
  const handleEdit = useCallback(
    (newsId) => {
      try {
        navigate(`/admin/news/edit/${newsId}`)
        setActiveMenu(null)
      } catch (error) {
        console.error("Error navigating to edit:", error)
        setFeedback({
          type: "error",
          message: "Error editing news item. Please try again.",
        })
      }
    },
    [navigate],
  )

  // Handle delete
  const handleDelete = useCallback(
    async (id) => {
      try {
        setLoading(true)
        await deleteNews(id)
        setFeedback({ type: "success", message: "News deleted successfully" })
        setIsDeleteModalOpen(false)
        setNewsToDelete(null)
        setTimeout(() => setFeedback({ type: "", message: "" }), 3000)
      } catch (error) {
        console.error("Error deleting news:", error)
        setFeedback({
          type: "error",
          message: error.response?.data?.error || error.message || "Failed to delete news",
        })
        setTimeout(() => setFeedback({ type: "", message: "" }), 3000)
      } finally {
        setLoading(false)
      }
    },
    [deleteNews],
  )

  // Toggle menu
  const toggleMenu = useCallback(
    (newsId) => {
      setActiveMenu(activeMenu === newsId ? null : newsId)
    },
    [activeMenu],
  )

  // Add click outside handler
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setActiveMenu(null)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Memoize filtered news
  const filteredNews = useMemo(() => {
    return news.filter(
      (item) =>
        item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.author && item.author.toString().toLowerCase().includes(searchTerm.toLowerCase())),
    )
  }, [news, searchTerm])

  // Memoize paginated news
  const paginatedNews = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredNews.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredNews, currentPage, itemsPerPage])
  
  // Memoize total pages
  const totalPages = useMemo(() => Math.ceil(filteredNews.length / itemsPerPage), [filteredNews.length, itemsPerPage])
  
  // Memoize handlePageChange
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page)
  }, [])
  
  if (loading || newsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }
  console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
  console.log(paginatedNews);
  console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage News</h1>
          <Link
            to="/admin/news/add"
            className="w-full sm:w-auto bg-[#FF6B00] text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
          >
            <Plus size={18} className="mr-2" />
            Add News
          </Link>
        </div>

        {/* Feedback Message */}
        {feedback.message && (
          <div
            className={`mb-4 p-4 rounded-lg ${
              feedback.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
            }`}
          >
            {feedback.message}
          </div>
        )}

        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search news..."
              className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
              value={searchTerm}
              onChange={handleSearch}
              />
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* News Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="w-full">
            <table className="w-full table-fixed">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="w-1/3 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Article
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Category
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
                    Status
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
                    Author Name
                  </th>
                  <th className="w-1/6 px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedNews.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-4 sm:px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No news items found
                    </td>
                  </tr>
                ) : (
                  paginatedNews.map((item) => (
                    
                    <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-4 sm:px-6 py-4">
                        <div className="flex items-center min-w-0">
                          <div className="h-10 w-10 flex-shrink-0">
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={
                                item.image && typeof item.image === "string" && !item.image.startsWith("data:")
                                  ? `http://localhost:5432/public/images/news/${item.image}`
                                  : "/placeholder.svg"
                              }
                              alt={item.title}
                              onError={(e) => {
                                e.target.onerror = null
                                e.target.src = "/placeholder.svg"
                              }}
                            />
                          </div>
                          <div className="ml-4 min-w-0">
                            <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {item.title}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400 sm:hidden truncate">
                              {item.category}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 sm:hidden">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  item.status === "Published"
                                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                    : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                                }`}
                              >
                                {item.status || "Draft"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{item.category}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.status === "Published"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                          }`}
                        >
                          {item.status || "Draft"}
                        </span>
                      </td>
                      <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
                        <div className="truncate">{item.autherName}</div>
                      </td>
                      <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
                        <div className="relative">
                          <button
                            onClick={() => toggleMenu(item.id)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            <MoreVertical size={18} />
                          </button>
                          {activeMenu === item.id && (
                            <div
                              className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10"
                              ref={menuRef}
                            >
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleView(item.id)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Eye size={16} className="mr-2" />
                                View
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleEdit(item.id)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Edit size={16} className="mr-2" />
                                Edit
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setNewsToDelete(item)
                                  setIsDeleteModalOpen(true)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
                              >
                                <Trash2 size={16} className="mr-2" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
              <div className="text-sm text-gray-700 dark:text-gray-300">
                <p>
                  Showing <span className="font-medium">{currentPage * itemsPerPage - itemsPerPage + 1}</span> to{" "}
                  <span className="font-medium">
                    {currentPage * itemsPerPage > filteredNews.length
                      ? filteredNews.length
                      : currentPage * itemsPerPage}
                  </span>{" "}
                  of <span className="font-medium">{filteredNews.length}</span> results
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 rounded text-sm ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
                      : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 rounded text-sm ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed"
                      : "bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90"
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {isDeleteModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Are you sure you want to delete "{newsToDelete?.title}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setIsDeleteModalOpen(false)
                    setNewsToDelete(null)
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(newsToDelete.id)}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default React.memo(ManageNews)


///////////////////////
// 'use client';

// import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useNews } from '../../contexts/NewsContext';
// import { Edit, Trash2, Search, Plus, Eye, MoreVertical } from 'lucide-react';
// import { format } from 'date-fns';

// const ManageNews = () => {
//   const navigate = useNavigate();
//   const { news, deleteNews, fetchNews } = useNews();
//   const [searchTerm, setSearchTerm] = useState('');
//   const [currentPage, setCurrentPage] = useState(1);
//   const [itemsPerPage] = useState(10);
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
//   const [newsToDelete, setNewsToDelete] = useState(null);
//   const [loading, setLoading] = useState(false);
//   const [activeMenu, setActiveMenu] = useState(null);
//   const menuRef = useRef(null);
//   const [feedback, setFeedback] = useState({ type: '', message: '' });

//   // Fetch news on component mount
//   useEffect(() => {
//     const loadNews = async () => {
//       try {
//         setLoading(true);
//         await fetchNews();
//       } catch (error) {
//         console.error('Error loading news:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error loading news. Please try again.',
//         });
//       } finally {
//         setLoading(false);
//       }
//     };

//     loadNews();
//   }, []); // Remove fetchNews from dependency array

//   // Handle search
//   const handleSearch = useCallback((e) => {
//     setSearchTerm(e.target.value);
//     setCurrentPage(1);
//   }, []);

//   // Handle view
//   const handleView = useCallback(
//     (newsId) => {
//       try {
//         const selectedNews = news.find((item) => item.id === newsId);
//         if (selectedNews) {
//           navigate(`/news/${newsId}`, {
//             state: {
//               selectedNews,
//               fromManage: true,
//             },
//           });
//           setActiveMenu(null);
//         } else {
//           setFeedback({
//             type: 'error',
//             message: 'News item not found',
//           });
//         }
//       } catch (error) {
//         console.error('Error navigating to view:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error viewing news item. Please try again.',
//         });
//       }
//     },
//     [navigate, news]
//   );

//   // Handle edit
//   const handleEdit = useCallback(
//     (newsId) => {
//       try {
//         navigate(`/admin/news/edit/${newsId}`);
//         setActiveMenu(null);
//       } catch (error) {
//         console.error('Error navigating to edit:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error editing news item. Please try again.',
//         });
//       }
//     },
//     [navigate]
//   );

//   // Handle delete
//   const handleDelete = useCallback(
//     async (id) => {
//       try {
//         setLoading(true);
//         await deleteNews(id);
//         setFeedback({ type: 'success', message: 'News deleted successfully' });
//         setIsDeleteModalOpen(false);
//         setNewsToDelete(null);
//         setTimeout(() => setFeedback({ type: '', message: '' }), 3000);
//       } catch (error) {
//         console.error('Error deleting news:', error);
//         setFeedback({ type: 'error', message: 'Failed to delete news' });
//         setTimeout(() => setFeedback({ type: '', message: '' }), 3000);
//       } finally {
//         setLoading(false);
//       }
//     },
//     [deleteNews]
//   );

//   // Toggle menu
//   const toggleMenu = useCallback(
//     (newsId) => {
//       setActiveMenu(activeMenu === newsId ? null : newsId);
//     },
//     [activeMenu]
//   );

//   // Add click outside handler
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (menuRef.current && !menuRef.current.contains(event.target)) {
//         setActiveMenu(null);
//       }
//     };

//     document.addEventListener('mousedown', handleClickOutside);
//     return () => {
//       document.removeEventListener('mousedown', handleClickOutside);
//     };
//   }, []);

//   // Memoize filtered news
//   const filteredNews = useMemo(() => {
//     return news.filter(
//       (item) =>
//         item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         item.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         item.author?.toLowerCase().includes(searchTerm.toLowerCase())
//     );
//   }, [news, searchTerm]);

//   // Memoize paginated news
//   const paginatedNews = useMemo(() => {
//     const startIndex = (currentPage - 1) * itemsPerPage;
//     return filteredNews.slice(startIndex, startIndex + itemsPerPage);
//   }, [filteredNews, currentPage, itemsPerPage]);

//   // Memoize total pages
//   const totalPages = useMemo(() => Math.ceil(filteredNews.length / itemsPerPage), [filteredNews.length, itemsPerPage]);

//   // Memoize handlePageChange
//   const handlePageChange = useCallback((page) => {
//     setCurrentPage(page);
//   }, []);

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
//           <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage News</h1>
//           <Link
//             to="/admin/news/add"
//             className="w-full sm:w-auto bg-[#FF6B00] text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
//           >
//             <Plus size={18} className="mr-2" />
//             Add News
//           </Link>
//         </div>

//         {/* Feedback Message */}
//         {feedback.message && (
//           <div
//             className={`mb-4 p-4 rounded-lg ${
//               feedback.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
//             }`}
//           >
//             {feedback.message}
//           </div>
//         )}

//         {/* Search and Filter */}
//         <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
//           <div className="relative">
//             <input
//               type="text"
//               placeholder="Search news..."
//               className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               value={searchTerm}
//               onChange={handleSearch}
//             />
//             <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
//           </div>
//         </div>

//         {/* News Table */}
//         <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
//           <div className="w-full">
//             <table className="w-full table-fixed">
//               <thead className="bg-gray-50 dark:bg-gray-700">
//                 <tr>
//                   <th className="w-1/3 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     Article
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Category
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden sm:table-cell">
//                     Status
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
//                     Author
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider hidden md:table-cell">
//                     Date
//                   </th>
//                   <th className="w-1/6 px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
//                 {paginatedNews.length === 0 ? (
//                   <tr>
//                     <td colSpan={6} className="px-4 sm:px-6 py-4 text-center text-gray-500 dark:text-gray-400">
//                       No news items found
//                     </td>
//                   </tr>
//                 ) : (
//                   paginatedNews.map((item) => (
//                     <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
//                       <td className="px-4 sm:px-6 py-4">
//                         <div className="flex items-center min-w-0">
//                           <div className="h-10 w-10 flex-shrink-0">
//                             <img
//                               className="h-10 w-10 rounded-full object-cover"
//                               src={item.image || '/placeholder.svg'}
//                               alt={item.title}
//                               onError={(e) => {
//                                 e.target.onerror = null;
//                                 e.target.src = '/placeholder.svg';
//                               }}
//                             />
//                           </div>
//                           <div className="ml-4 min-w-0">
//                             <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
//                               {item.title}
//                             </div>
//                             <div className="text-sm text-gray-500 dark:text-gray-400 sm:hidden truncate">
//                               {item.category}
//                             </div>
//                             <div className="text-xs text-gray-500 dark:text-gray-400 sm:hidden">
//                               <span
//                                 className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                                   item.status === 'Published'
//                                     ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
//                                     : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
//                                 }`}
//                               >
//                                 {item.status || 'Published'}
//                               </span>
//                             </div>
//                           </div>
//                         </div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                         <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{item.category}</div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
//                         <span
//                           className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                             item.status === 'Published'
//                               ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
//                               : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
//                           }`}
//                         >
//                           {item.status || 'Published'}
//                         </span>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
//                         <div className="truncate">{item.author}</div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
//                         <div className="truncate">
//                           {item.date ? format(new Date(item.date), 'MMM dd, yyyy') : 'N/A'}
//                         </div>
//                       </td>
//                       <td className="px-4 sm:px-6 py-4 text-right text-sm font-medium">
//                         <div className="relative">
//                           <button
//                             onClick={() => toggleMenu(item.id)}
//                             className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
//                           >
//                             <MoreVertical size={18} />
//                           </button>
//                           {activeMenu === item.id && (
//                             <div
//                               className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10"
//                               ref={menuRef}
//                             >
//                               <button
//                                 onClick={(e) => {
//                                   e.stopPropagation();
//                                   handleView(item.id);
//                                 }}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Eye size={16} className="mr-2" />
//                                 View
//                               </button>
//                               <button
//                                 onClick={(e) => {
//                                   e.stopPropagation();
//                                   handleEdit(item?.id);
//                                 }}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Edit size={16} className="mr-2" />
//                                 Edit
//                               </button>
//                               <button
//                                 onClick={(e) => {
//                                   e.stopPropagation();
//                                   setNewsToDelete(item);
//                                   setIsDeleteModalOpen(true);
//                                 }}
//                                 className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
//                               >
//                                 <Trash2 size={16} className="mr-2" />
//                                 Delete
//                               </button>
//                             </div>
//                           )}
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 )}
//               </tbody>
//             </table>
//           </div>

//           {/* Pagination */}
//           {totalPages > 1 && (
//             <div className="px-4 sm:px-6 py-3 flex flex-col sm:flex-row items-center justify-between border-t border-gray-200 dark:border-gray-700 gap-4">
//               <div className="text-sm text-gray-700 dark:text-gray-300">
//                 <p>
//                   Showing <span className="font-medium">{currentPage * itemsPerPage - itemsPerPage + 1}</span> to{' '}
//                   <span className="font-medium">
//                     {currentPage * itemsPerPage > filteredNews.length
//                       ? filteredNews.length
//                       : currentPage * itemsPerPage}
//                   </span>{' '}
//                   of <span className="font-medium">{filteredNews.length}</span> results
//                 </p>
//               </div>
//               <div className="flex space-x-2">
//                 <button
//                   onClick={() => handlePageChange(currentPage - 1)}
//                   disabled={currentPage === 1}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === 1
//                       ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
//                       : 'bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90'
//                   }`}
//                 >
//                   Previous
//                 </button>
//                 <button
//                   onClick={() => handlePageChange(currentPage + 1)}
//                   disabled={currentPage === totalPages}
//                   className={`px-3 py-1 rounded text-sm ${
//                     currentPage === totalPages
//                       ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
//                       : 'bg-[#FF6B00] text-white hover:bg-[#FF6B00]/90 dark:bg-[#FF6B00] dark:hover:bg-[#FF6B00]/90'
//                   }`}
//                 >
//                   Next
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>

//         {/* Delete Confirmation Modal */}
//         {isDeleteModalOpen && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg max-w-md w-full">
//               <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Delete</h3>
//               <p className="mb-6 text-gray-600 dark:text-gray-300">
//                 Are you sure you want to delete "{newsToDelete?.title}"? This action cannot be undone.
//               </p>
//               <div className="flex justify-end space-x-3">
//                 <button
//                   onClick={() => {
//                     setIsDeleteModalOpen(false);
//                     setNewsToDelete(null);
//                   }}
//                   className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={() => handleDelete(newsToDelete.id)}
//                   className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
//                 >
//                   Delete
//                 </button>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default React.memo(ManageNews);
