import express from "express";
import UserController, {
  uploadUserPhoto,
  resizeUserPhoto,
} from "../controllers/userController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

// Register with Image Upload
router.post(
  "/register",
  uploadUserPhoto,
  resizeUserPhoto,
  UserController.register
);
router.post("/login", UserController.login);
router.post("/logout", authenticate, UserController.logout);
// Admin and Authentication Routes
router.get("/", authenticate, authorizeAdmin, UserController.getAllUsers);
router.get("/admins", authenticate, UserController.getAllAdmins);
router.get("/farmers", authenticate, authorizeAdmin, UserController.getAllFarmers);
router.get("/shoppers", authenticate, authorizeAdmin, UserController.getAllShoppers);

// Profile routes for current user
router.get("/profile/me", authenticate, UserController.getCurrentUserProfile);
router.put(
  "/profile/me",
  authenticate,
  uploadUserPhoto,
  resizeUserPhoto,
  UserController.updateCurrentUserProfile
);

router.get("/:id", authenticate, UserController.getUser);
router.put(
  "/:id",
  authenticate,
  uploadUserPhoto,
  resizeUserPhoto,
  UserController.updateUser
);
router.delete("/:id", authenticate, UserController.deleteUser);

// for  user
router.post("/forgotPassword", UserController.forgotPassword);
router.post("/resetPassword/:token", UserController.resetPassword);
router.post("/changePassword", authenticate, UserController.changePassword);

// for otp code
// Email Verification Routes
router.post("/verify-email/send", UserController.verifyEmailSendOTP);
router.post("/verify-email/confirm", UserController.verifyEmailConfirmOTP);

export default router;
