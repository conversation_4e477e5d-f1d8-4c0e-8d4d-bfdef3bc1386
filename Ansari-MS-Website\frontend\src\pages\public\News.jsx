/* eslint-disable no-unused-vars */
import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Calendar, User, Search, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNews } from '../../contexts/NewsContext';
import Button from '../../components/Button';

const News = () => {
  const location = useLocation();
  const { news, fetchNews } = useNews();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  const [categories, setCategories] = useState(['All']);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredNews, setFilteredNews] = useState([]);

  // Fetch news data
  useEffect(() => {
    const loadNews = async () => {
      try {
        setLoading(true);
        await fetchNews();
      } catch (error) {
        console.error('Error loading news:', error);
      } finally {
        setLoading(false);
      }
    };

    loadNews();
  }, []); // Remove fetchNews from dependency array

  // Update categories when news changes
  useEffect(() => {
    if (news && news.length > 0) {
      const uniqueCategories = ['All', ...new Set(news.map((item) => item.category).filter(Boolean))];
      setCategories(uniqueCategories);

      // Initialize filtered news when news data changes
      let filtered = [...news];

      // Apply existing filters
      if (searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase().trim();
        filtered = filtered.filter(
          (item) =>
            (item.title && item.title.toLowerCase().includes(searchLower)) ||
            (item.excerpt && item.excerpt.toLowerCase().includes(searchLower)) ||
            (item.author && item.author.toLowerCase().includes(searchLower))
        );
      }

      if (selectedCategory !== 'All') {
        filtered = filtered.filter((item) => item.category === selectedCategory);
      }
      setFilteredNews(filtered);
    } else {
      setFilteredNews([]);
    }
  }, [news]); // Only depend on news changes

  // Handle filters
  useEffect(() => {
    if (!news) return;

    let filtered = [...news];

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (item) =>
          (item.title && item.title.toLowerCase().includes(searchLower)) ||
          (item.excerpt && item.excerpt.toLowerCase().includes(searchLower)) ||
          (item.author && item.author.toLowerCase().includes(searchLower))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'All') {
      filtered = filtered.filter((item) => item.category === selectedCategory);
    }

    setFilteredNews(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchTerm, selectedCategory, news]); // Only depend on filter changes and news

  // Handle search input
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle category selection
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('All');
    setCurrentPage(1);
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredNews.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredNews.length / itemsPerPage);

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  if (loading) {
    return (
      <div className="min-h-screen pt-20 pb-10 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold font-heading text-[#2C3E50]">News & Updates</h1>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <div key={item} className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );


  }

  return (
    <div className="min-h-screen pt-15 pb-10">
      {/* Hero Section */}
      <section className="relative h-[90vh] top-16 flex items-center justify-center overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0">
          <img src="/imgs/news.jpg" alt="News background" className="w-full h-full" />
          <div className="absolute inset-0 bg-gradient-to-r from-[#2C3E50]/90 to-[#333333]/80 backdrop-blur-0"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h1 className="text-4xl md:text-6xl font-extrabold font-heading text-white mb-6 leading-tight">
              News & <span className="text-[#FF6B00]">Updates</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-3xl font-sans mx-auto mb-4 leading-relaxed">
              Stay informed with the latest happenings, tips, and insights from our poultry farm.
            </p>
          </motion.div>
        </div>
      </section>

      {/* News Content Section */}
      <section className="relative top-16 py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Main Content */}
            <div className="w-full md:w-3/4">
              {/* Search and Filter */}
              <div className="bg-white p-6 rounded-xl shadow-md mb-8">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-grow">
                    <input
                      type="text"
                      placeholder="Search news..."
                      className="w-full pl-10 pr-4 py-2 border font-sans rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                      value={searchTerm}
                      onChange={handleSearch}
                    />
                    <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  <select
                    className="px-4 py-2 border font-sans rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                  >
                    {categories.map((category) => (
                      <option key={category} value={category} className="font-sans">
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* News Grid */}
              {filteredNews.length === 0 ? (
                <div className="bg-white p-8 rounded-xl shadow-md text-center">
                  <h3 className="text-xl font-semibold font-heading text-[#2C3E50] mb-2">No Results Found</h3>
                  <p className="text-[#333333]/80 font-sans mb-4">
                    We couldn&apos;t find any news articles matching your search criteria.
                  </p>
                  <Button
                    variant="primary"
                    onClick={clearFilters}
                    // className="bg-[#FF6B00] text-white px-4 py-2 rounded-lg hover:bg-[#D32F2F] transition-colors"
                  >
                    Clear Filters
                  </Button>
                </div>
              ) : (
                <motion.div
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                  variants={staggerContainer}
                  initial="hidden"
                  animate="visible"
                >
                  {currentItems.map((item) => (
                    <motion.div key={item.id} variants={fadeIn}>
                      <Link to={`/news/${item.id}`} className="block group">
                        <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                          <div className="h-48 overflow-hidden">
                            <img
                              src={`http://localhost:5432/public/images/news/${item.image}`|| '/placeholder.svg'}
                              alt={item.title}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                              onError={(e) => {
                                e.target.src = '/placeholder.svg';
                              }}
                            />
                          </div>
                          <div className="p-6">
                            <div className="flex items-center mb-2">
                              <span className="text-xs font-medium bg-[#FF6B00]/10 text-[#FF6B00] px-2 font-sans py-1 rounded-full">
                                {item.category}
                              </span>
                            </div>
                            <h3 className="text-xl font-heading font-bold text-[#2C3E50] mb-2 group-hover:text-[#FF6B00] transition-colors">
                              {item.title}
                            </h3>
                            {/* <p className="text-[#333333]/80 font-sans mb-4 line-clamp-3">{item.excerpt}</p> */}
                            <div className="flex justify-between items-center text-sm text-gray-500">
                              <div className="flex items-center">
                                <Calendar size={14} className="mr-1" />
                                {item.createdAt
                                  ? new Date(item.createdAt).toLocaleDateString()
                                  : 'No date'
                                }
                              </div>
                              <div className="flex items-center">
                                <User size={14} className="mr-1" />
                                {item.author || item.autherName || 'Unknown'}
                              </div>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </motion.div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-12">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`px-4 py-2 rounded-lg flex items-center ${
                        currentPage === 1
                          ? 'bg-gray-100 text-gray-400 font-sans cursor-not-allowed'
                          : 'bg-[#2C3E50] text-white hover:bg-[#1a2530]'
                      }`}
                    >
                      <ChevronLeft size={18} className="mr-1" />
                      Previous
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`w-10 h-10 rounded-lg ${
                          currentPage === page
                            ? 'bg-[#FF6B00] text-white'
                            : 'bg-gray-100 text-[#333333] hover:bg-gray-200'
                        }`}
                      >
                        {page}
                      </button>
                    ))}
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`px-4 py-2 rounded-lg flex items-center ${
                        currentPage === totalPages
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-[#2C3E50] text-white hover:bg-[#1a2530]'
                      }`}
                    >
                      Next
                      <ChevronRight size={18} className="ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="w-full md:w-1/4">
              {/* Categories */}
              <div className="bg-white p-6 rounded-xl shadow-md mb-8">
                <h3 className="text-xl font-heading font-bold text-[#2C3E50] mb-4">Categories</h3>
                <ul className="space-y-2">
                  {categories.map((category) => (
                    <li key={category}>
                      <button
                        onClick={() => handleCategoryChange(category)}
                        className={`w-full font-sans text-left px-3 py-2 rounded-lg transition-colors ${
                          selectedCategory === category ? 'bg-[#FF6B00] text-white' : 'hover:bg-gray-100 text-[#333333]'
                        }`}
                      >
                        {category}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>



              {/* Recent Posts */}
              <div className="bg-white p-6 rounded-xl shadow-md mb-8">
                <h3 className="text-xl font-heading font-bold text-[#2C3E50] mb-4">Recent Posts</h3>
                <div className="space-y-4">

                  {[...news]
                    .sort((a, b) => b.id - a.id)  // له نوې نه زړې خوا ته ترتیب
                    .slice(0, 4)                  // لومړي ۴، یعنې وروستي/نوې
                    .map((item) => (
                      <Link key={item.id} to={`/news/${item.id}`} className="flex group">
                        <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={`http://localhost:5432/public/images/news/${item.image}` || '/placeholder.svg'}
                            alt={item.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              e.target.src = '/placeholder.svg';
                            }}
                          />
      </div>
      <div className="ml-3">
        <h4 className="font-medium text-[#2C3E50] group-hover:text-primary transition-colors font-heading line-clamp-2">
          {item.title}
        </h4>
        <div className="text-xs font-sans text-gray-500 mt-1">
          <Calendar size={12} className="inline mr-1" />
          {item.createdAt
            ? new Date(item.createdAt).toLocaleDateString()
            : 'No date'
          }
        </div>
      </div>
    </Link>
))}

                </div>
              </div>

              {/* Newsletter */}
              {/* <div className="bg-[#2C3E50] p-6 rounded-xl shadow-md text-white">
                <h3 className="text-xl font-heading font-bold mb-4">Subscribe to Newsletter</h3>
                <p className="text-white/80 font-sans mb-4">Stay updated with our latest news and special offers.</p>
                <form className="space-y-4">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-4 py-2 rounded-lg text-[#333333] focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  />
                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full flex items-center justify-center"
                    // className="w-full bg-[#FF6B00] text-white font-bold py-2 px-4 rounded-lg hover:bg-[#D32F2F] transition-colors flex items-center justify-center"
                  >
                    Subscribe <ArrowRight size={18} className="ml-2" />
                  </Button>
                </form>
              </div> */}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default News;
