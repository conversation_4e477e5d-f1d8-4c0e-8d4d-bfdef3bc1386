'use client';

import { useState, use<PERSON>emo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreVertical,
  Activity,
  AlertTriangle,
  XCircle,
  Trash,
  Pencil,
} from 'lucide-react';
import { useFeed } from '../../contexts/FeedContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>G<PERSON>, <PERSON> } from 'recharts';
import { format } from 'date-fns';

const SeedsPage = () => {
  const navigate = useNavigate();
  const { seeds, deleteSeed } = useFeed();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredSeeds = useMemo(() => {
    return seeds.filter((seed) => {
      const matchesSearch = seed.S_Name.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [seeds, searchTerm]);

  const chartData = useMemo(() => {
    return filteredSeeds.map((seed) => ({
      name: seed.S_Name,
      value: seed.S_Price,
    }));
  }, [filteredSeeds]);

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this seed?')) {
      deleteSeed(id);
    }
  };

  const handleEdit = (id) => {
    navigate(`/admin/seeds/edit/${id}`);
  };

  const handleAdd = () => {
    navigate('/admin/seeds/add');
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Seeds Management</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage your seeds inventory and track seed information</p>
          </div>
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button variant="secondary" size="sm">
              <Download className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('export')}
            </Button>
            <Button variant="secondary" size="sm">
              <Upload className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              {t('import')}
            </Button>
            <Button onClick={handleAdd} variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              Add New Seed
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className={`flex flex-col sm:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder="Search seeds..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Total Seeds</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{seeds.length}</div>
              <p className="text-xs text-muted-foreground">Available varieties</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Active Seeds</CardTitle>
              <AlertTriangle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {seeds.filter((s) => new Date(s.S_ExpireDate) > new Date()).length}
              </div>
              <p className="text-xs text-muted-foreground">Not expired</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Expired Seeds</CardTitle>
              <XCircle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {seeds.filter((s) => new Date(s.S_ExpireDate) <= new Date()).length}
              </div>
              <p className="text-xs text-muted-foreground">Need replacement</p>
            </CardContent>
          </Card>
        </div>

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Seeds Price Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#FF6B00" name="Price ($)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Seeds Table */}
        <Card>
          <CardHeader>
            <CardTitle>Seeds List</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>Expiry Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSeeds.length > 0 ? (
                  filteredSeeds.map((seed) => (
                    <TableRow key={seed.S_Id}>
                      <TableCell className="font-medium">{seed.S_Name}</TableCell>
                      <TableCell>${seed.S_Price}</TableCell>
                      <TableCell>{seed.S_StartDate ? format(new Date(seed.S_StartDate), 'yyyy-MM-dd') : 'N/A'}</TableCell>
                      <TableCell>{format(new Date(seed.S_ExpireDate), 'yyyy-MM-dd')}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          new Date(seed.S_ExpireDate) > new Date() 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {new Date(seed.S_ExpireDate) > new Date() ? 'Active' : 'Expired'}
                        </span>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(seed.S_Id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(seed.S_Id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No seeds found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SeedsPage;
