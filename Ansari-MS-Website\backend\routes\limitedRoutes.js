import express from "express";
import LimitedController, {
  uploadUserPhoto,
  resizeUserPhoto,
} from "../controllers/limitedController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.use(authenticate, authorizeAdmin);
router.post(
  "/",
  uploadUserPhoto,
  resizeUserPhoto,
  LimitedController.createLimited,
);
router.get("/", LimitedController.getAllLimited);
router.get("/:id", LimitedController.getLimitedById);
router.put(
  "/:id",
  uploadUserPhoto,
  resizeUserPhoto,
  LimitedController.updateLimited,
);
router.delete("/:id", LimitedController.deleteLimited);

export default router;
