import React, { createContext, useContext, useState, useEffect } from 'react';
import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';

export const MedicineContext = createContext();

export const useMedicine = () => {
  const context = useContext(MedicineContext);
  if (!context) {
    throw new Error('useMedicine must be used within a MedicineProvider');
  }
  return context;
};

export const MedicineProvider = ({ children }) => {
  const [medicines, setMedicines] = useState(() => loadFromLocalStorage('medicines', []));

  useEffect(() => {
    saveToLocalStorage('medicines', medicines);
  }, [medicines]);

  const clearMedicines = () => {
    setMedicines([]);
    removeFromLocalStorage('medicines');
  };

  const addMedicine = (medicine) => {
    setMedicines((prev) => [...prev, { ...medicine, id: Date.now() }]);
  };

  const updateMedicine = (id, updatedMedicine) => {
    setMedicines((prev) =>
      prev.map((medicine) => (medicine.id === id ? { ...medicine, ...updatedMedicine } : medicine))
    );
  };

  const deleteMedicine = (id) => {
    setMedicines((prev) => prev.filter((medicine) => medicine.id !== id));
  };

  return (
    <MedicineContext.Provider
      value={{
        medicines,
        setMedicines,
        addMedicine,
        updateMedicine,
        deleteMedicine,
        clearMedicines,
      }}
    >
      {children}
    </MedicineContext.Provider>
  );
};
