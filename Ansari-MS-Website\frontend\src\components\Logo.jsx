'use client';

import { motion } from 'framer-motion';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// import { Tractor } from "lucide-react"

const Logo = () => {
  const { t, i18n } = useTranslation();

  useEffect(() => {
    const dir = i18n.language === 'ps' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', dir);
    document.documentElement.setAttribute('lang', i18n.language);
  }, [i18n.language]);
  return (
    <motion.div
      className="flex items-center gap-2"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="w-10 h-10 flex items-center  justify-center">
        <img src="./imgs/logo.png" alt="Logo" className="w-full h-full  rounded-full" />
      </div>
      <div className="flex ">
        <h1 className="text-primary text-2xl  font-bold ">
          {t('company.name')} <span className="text-white md:hidden">{t('company.ltd')}</span>
          <span className="text-white hidden md:inline">{t('company.ltd1')}</span>
        </h1>
      </div>
    </motion.div>
  );
};

export default Logo;

///////////////////
// "use client"

// import { motion } from "framer-motion"
// import { Tractor } from "lucide-react"

// const Logo = () => {
//   return (
//     <motion.div
//       className="flex items-center gap-2"
//       initial={{ opacity: 0, x: -20 }}
//       animate={{ opacity: 1, x: 0 }}
//       transition={{ duration: 0.5 }}
//     >
//       <div className="w-10 h-10  flex items-center justify-center">
//       <Tractor className="text-primary " size={40} />
//       </div>
//       <h1 className="text-white text-xl font-bold font-heading"><span className="text-primary">Ansari</span> Company</h1>
//     </motion.div>
//   )
// }

// export default Logo
