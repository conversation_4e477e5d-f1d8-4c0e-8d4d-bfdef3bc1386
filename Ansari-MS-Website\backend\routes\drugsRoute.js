import express from "express";
import DrugsController from "../controllers/drugsController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post("/", authenticate, DrugsController.create);
router.get("/", DrugsController.getAll);
router.get("/:id", DrugsController.getById);
router.put("/:id", authenticate, DrugsController.update);
router.delete("/:id", authenticate, authorizeAdmin, DrugsController.delete);

export default router;
