import db from "../config/db.js";

const ChatModel = {
  createRoom: async (user1_id, user2_id) => {
    const [result] = await db.execute(
      `INSERT INTO chat_rooms (user1_id, user2_id) VALUES (?, ?)`,
      [user1_id, user2_id],
    );
    return result.insertId;
  },

  getRoom: async (user1_id, user2_id) => {
    const [rows] = await db.execute(
      `SELECT * FROM chat_rooms WHERE (user1_id = ? AND user2_id = ?) OR (user1_id = ? AND user2_id = ?)`,
      [user1_id, user2_id, user2_id, user1_id],
    );
    return rows[0]; // returns the room if it exists
  },
  getUserById: async (user_id) => {
    const [rows] = await db.execute(
      `SELECT u_Id, Role FROM users WHERE u_Id = ?`,
      [user_id],
    );
    return rows[0];
  },

  createMessage: async (
    room_id,
    sender_id,
    message_type,
    message_content,
    image_url = null,
  ) => {
    const [result] = await db.execute(
      `INSERT INTO chat_messages (room_id, sender_id, message_type, message_content, image_url) VALUES (?, ?, ?, ?, ?)`,
      [room_id, sender_id, message_type, message_content, image_url],
    );
    return result.insertId;
  },

  getMessagesByRoomId: async (room_id) => {
    const [messages] = await db.execute(
      `SELECT cm.*, u.U_FirstName, u.U_LastName, u.image FROM chat_messages cm
       JOIN users u ON cm.sender_id = u.u_Id WHERE cm.room_id = ? ORDER BY cm.sent_at`,
      [room_id],
    );
    return messages;
  },

  deleteMessage: async (messageId, userId) => {
    // First check if the message exists and belongs to the user
    const [message] = await db.execute(
      `SELECT * FROM chat_messages WHERE message_id = ? AND sender_id = ?`,
      [messageId, userId]
    );

    if (message.length === 0) {
      return null; // Message not found or user doesn't own it
    }

    // Soft delete the message
    const [result] = await db.execute(
      `UPDATE chat_messages SET is_deleted = TRUE WHERE message_id = ? AND sender_id = ?`,
      [messageId, userId]
    );

    return result.affectedRows > 0 ? message[0] : null;
  },

  editMessage: async (messageId, userId, newContent) => {
    // First check if the message exists and belongs to the user
    const [message] = await db.execute(
      `SELECT * FROM chat_messages WHERE message_id = ? AND sender_id = ? AND is_deleted = FALSE`,
      [messageId, userId]
    );

    if (message.length === 0) {
      return null; // Message not found, user doesn't own it, or it's deleted
    }

    const originalMessage = message[0];

    // Update the message with new content
    const [result] = await db.execute(
      `UPDATE chat_messages
       SET message_content = ?,
           is_edited = TRUE,
           edited_at = CURRENT_TIMESTAMP,
           original_content = CASE
             WHEN original_content IS NULL THEN ?
             ELSE original_content
           END
       WHERE message_id = ? AND sender_id = ?`,
      [newContent, originalMessage.message_content, messageId, userId]
    );

    if (result.affectedRows > 0) {
      // Return the updated message
      const [updatedMessage] = await db.execute(
        `SELECT cm.*, u.U_FirstName, u.U_LastName, u.image FROM chat_messages cm
         JOIN users u ON cm.sender_id = u.u_Id WHERE cm.message_id = ?`,
        [messageId]
      );
      return updatedMessage[0];
    }

    return null;
  },

  getMessageById: async (messageId) => {
    const [message] = await db.execute(
      `SELECT cm.*, u.U_FirstName, u.U_LastName, u.image FROM chat_messages cm
       JOIN users u ON cm.sender_id = u.u_Id WHERE cm.message_id = ?`,
      [messageId]
    );
    return message[0];
  },
  // In ChatModel.js
  getAdminUser: async () => {
    const [adminUser] = await db.execute(
      `SELECT u_Id, U_FirstName, U_LastName, image FROM users WHERE u_Id = ?`,
      [1], // Assuming admin has user ID 1
    );
    return adminUser.length ? adminUser[0] : null; // Returns admin user info if exists
  },

  getAdminChatRoomsByRole: async (adminId, role) => {
    const [rows] = await db.execute(
      `SELECT
        cr.*,
        uOther.u_Id AS userId,
        uOther.U_FirstName AS name,
        uOther.image,
        uOther.U_Email AS email
     FROM chat_rooms cr
     JOIN users u1 ON cr.user1_id = u1.u_Id
     JOIN users u2 ON cr.user2_id = u2.u_Id
     JOIN users uOther
       ON (uOther.u_Id = IF(cr.user1_id = ?, cr.user2_id, cr.user1_id))
     WHERE (
        (cr.user1_id = ? AND u2.Role = ?) OR
        (cr.user2_id = ? AND u1.Role = ?)
     )`,
      [adminId, adminId, role, adminId, role],
    );
    return rows;
  },

  getUsersByRole: async (role) => {
    const [rows] = await db.execute(
      `SELECT u_Id, U_FirstName AS U_FirstName, U_Email AS U_Email FROM users WHERE Role = ?`,
      [role],
    );
    return rows;
  },
};

export default ChatModel;
