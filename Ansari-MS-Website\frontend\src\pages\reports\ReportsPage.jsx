import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useReports } from '../../contexts/ReportContext';
import { Card, Row, Col, Button, DatePicker, Table, Space, Select } from 'antd';
import { DownloadOutlined, Bar<PERSON><PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined, Pie<PERSON>hartOutlined } from '@ant-design/icons';
import { Line, Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend);

const ReportsPage = () => {
  const { language, translations } = useLanguage();
  const { getWeeklyReport, getMonthlyReport, getAnnualReport } = useReports();
  const [reportType, setReportType] = useState('weekly');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [reportData, setReportData] = useState(null);

  // Get translation function
  const t = (key) => translations[language][key];

  const handleGenerateReport = () => {
    if (!startDate || !endDate) return;

    let report;
    switch (reportType) {
      case 'weekly':
        report = getWeeklyReport(startDate, endDate);
        break;
      case 'monthly':
        report = getMonthlyReport(startDate, endDate);
        break;
      case 'annual':
        report = getAnnualReport(startDate.getFullYear());
        break;
      default:
        return;
    }
    setReportData(report);
  };

  const handleExport = (format) => {
    // Implement export functionality
    console.log(`Exporting to ${format} format`);
  };

  const chartData = {
    labels: reportData?.data?.labels || [],
    datasets: [
      {
        label: t('revenue'),
        data: reportData?.data?.revenue || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
      },
      {
        label: t('expenses'),
        data: reportData?.data?.expenses || [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
      },
    ],
  };

  return (
    <div className="reports-page">
      <Card title={t('reports')}>
        <Space style={{ marginBottom: 16 }}>
          <Select
            value={reportType}
            onChange={setReportType}
            options={[
              { value: 'weekly', label: t('weekly_report') },
              { value: 'monthly', label: t('monthly_report') },
              { value: 'annual', label: t('annual_report') },
            ]}
          />
          <DatePicker.RangePicker
            onChange={(dates) => {
              setStartDate(dates[0]);
              setEndDate(dates[1]);
            }}
          />
          <Button type="primary" onClick={handleGenerateReport}>
            {t('generate_report')}
          </Button>
          <Button icon={<DownloadOutlined />} onClick={() => handleExport('pdf')}>
            {t('export_pdf')}
          </Button>
          <Button icon={<DownloadOutlined />} onClick={() => handleExport('excel')}>
            {t('export_excel')}
          </Button>
        </Space>

        {reportData && (
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title={t('revenue_vs_expenses')}>
                <Line data={chartData} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title={t('sales_by_category')}>
                <Pie data={chartData} />
              </Card>
            </Col>
            <Col span={12}>
              <Card title={t('monthly_comparison')}>
                <Bar data={chartData} />
              </Card>
            </Col>
          </Row>
        )}
      </Card>
    </div>
  );
};

export default ReportsPage;
