'use client';

/* eslint-disable react/prop-types */
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Save, X, Upload } from 'lucide-react';
import { useFeed } from '../../contexts/FeedContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';

const AddFeed = () => {
  const navigate = useNavigate();
  const { addFeed } = useFeed();
  const { language, translations } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const [formData, setFormData] = useState({
    name: '',
    type: 'starter',
    quantity: '',
    price: '',
    supplier: '',
    status: 'in-stock',
    batchNumber: '',
    expiryDate: '',
    notes: '',
    unit: 'kg',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setFeedback({ type: '', message: '' });
  };

  const validateForm = () => {
    if (!formData.name.trim()) throw new Error(t('name_required'));
    if (!formData.type) throw new Error(t('type_required'));
    if (!formData.quantity || formData.quantity <= 0) throw new Error(t('valid_quantity_required'));
    if (!formData.price || formData.price <= 0) throw new Error(t('valid_price_required'));
    if (!formData.supplier.trim()) throw new Error(t('supplier_required'));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setFeedback({ type: '', message: '' });

    try {
      validateForm();

      const feedData = {
        ...formData,
        createdAt: new Date().toISOString(),
      };

      await addFeed(feedData);
      setFeedback({
        type: 'success',
        message: t('feed_added_successfully'),
      });

      setTimeout(() => {
        navigate('/admin/feed');
      }, 1500);
    } catch (error) {
      setFeedback({
        type: 'error',
        message: error.message || t('add_feed_error'),
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        <Card>
          <CardHeader>
            <div className={`flex justify-between items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              <div>
                <CardTitle>{t('add_new_feed')}</CardTitle>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('feed_information')}</p>
              </div>
              <Button
                variant="secondary"
                onClick={() => navigate('/admin/feed')}
                className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
              >
                <X className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                {t('cancel')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-6 p-4 rounded-lg ${
                  feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`}
              >
                {feedback.message}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2 space-y-6">
                  {/* Feed Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('name')}
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      placeholder={t('enter_feed_name')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  {/* Feed Type */}
                  <div>
                    <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_type')}
                    </label>
                    <select
                      id="type"
                      name="type"
                      value={formData.type}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    >
                      <option value="starter">{t('starter')}</option>
                      <option value="grower">{t('grower')}</option>
                      <option value="finisher">{t('finisher')}</option>
                      <option value="layer">{t('layer')}</option>
                      <option value="broiler">{t('broiler')}</option>
                      <option value="other">{t('other')}</option>
                    </select>
                  </div>

                  {/* Supplier */}
                  <div>
                    <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_supplier')}
                    </label>
                    <input
                      type="text"
                      id="supplier"
                      name="supplier"
                      value={formData.supplier}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      placeholder={t('enter_supplier_name')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  {/* Notes */}
                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('notes')}
                    </label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleChange}
                      rows="4"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      placeholder={t('additional_notes')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    ></textarea>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Quantity */}
                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_quantity')}
                    </label>
                    <div className={`flex ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                      <input
                        type="number"
                        id="quantity"
                        name="quantity"
                        value={formData.quantity}
                        onChange={handleChange}
                        required
                        className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600 ${
                          language === 'ps' ? 'rounded-r-lg' : 'rounded-l-lg'
                        }`}
                        placeholder={t('enter_quantity')}
                        dir={language === 'ps' ? 'rtl' : 'ltr'}
                      />
                      <select
                        id="unit"
                        name="unit"
                        value={formData.unit}
                        onChange={handleChange}
                        className={`px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] bg-gray-50 dark:bg-gray-700 dark:text-white dark:border-gray-600 ${
                          language === 'ps' ? 'rounded-l-lg border-r-0' : 'rounded-r-lg border-l-0'
                        }`}
                        dir={language === 'ps' ? 'rtl' : 'ltr'}
                      >
                        <option value="kg">kg</option>
                        <option value="g">g</option>
                        <option value="ton">ton</option>
                        <option value="bag">bag</option>
                      </select>
                    </div>
                  </div>

                  {/* Price */}
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_price')} (AFN)
                    </label>
                    <input
                      type="number"
                      id="price"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      placeholder={t('enter_price')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  {/* Batch Number */}
                  <div>
                    <label htmlFor="batchNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_batch_number')}
                    </label>
                    <input
                      type="text"
                      id="batchNumber"
                      name="batchNumber"
                      value={formData.batchNumber}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      placeholder={t('enter_batch_number')}
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  {/* Expiry Date */}
                  <div>
                    <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('feed_expiry_date')}
                    </label>
                    <input
                      type="date"
                      id="expiryDate"
                      name="expiryDate"
                      value={formData.expiryDate}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    />
                  </div>

                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      {t('status')}
                    </label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                      dir={language === 'ps' ? 'rtl' : 'ltr'}
                    >
                      <option value="in-stock">{t('in_stock')}</option>
                      <option value="low-stock">{t('low_stock')}</option>
                      <option value="out-of-stock">{t('out_of_stock')}</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="mt-6 flex justify-end">
                <Button
                  type="submit"
                  disabled={loading}
                  className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                >
                  {loading ? (
                    <>
                      <svg
                        className={`animate-spin h-5 w-5 text-white ${language === 'ps' ? 'ml-2' : 'mr-2'}`}
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      {t('saving')}
                    </>
                  ) : (
                    <>
                      <Save className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                      {t('save_feed')}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddFeed;

// // src/pages/admin/feed/add.jsx
// import { useNavigate } from "react-router-dom"
// import  Button  from "../../components/management-system/ui/Button"
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../components/management-system/ui/Card"
// import { Input } from "../../components/management-system/ui/Input"
// import { Label } from "../../components/management-system/ui/Label"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/management-system/ui/Select"

// export default function AddFeedPage() {
//   const navigate = useNavigate()

//   const handleSubmit = (e) => {
//     e.preventDefault()
//     // Handle form submission
//     navigate("/admin/feed")
//   }

//   return (
//     <div className="max-w-2xl mx-auto">
//       <Card>
//         <CardHeader>
//           <CardTitle>Add Feed Stock</CardTitle>
//           <CardDescription>Record new feed inventory</CardDescription>
//         </CardHeader>
//         <form onSubmit={handleSubmit}>
//           <CardContent className="space-y-4">
//             <div className="space-y-2">
//               <Label htmlFor="supplier">Supplier</Label>
//               <Select>
//                 <SelectTrigger id="supplier">
//                   <SelectValue placeholder="Select supplier" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="shorandam">Shorandam Mill</SelectItem>
//                   <SelectItem value="kandahar">Kandahar Feed</SelectItem>
//                   <SelectItem value="helmand">Helmand Supplies</SelectItem>
//                   <SelectItem value="new">Add New Supplier</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="feedType">Feed Type</Label>
//               <Select>
//                 <SelectTrigger id="feedType">
//                   <SelectValue placeholder="Select feed type" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="starter">Starter Feed</SelectItem>
//                   <SelectItem value="grower">Grower Feed</SelectItem>
//                   <SelectItem value="finisher">Finisher Feed</SelectItem>
//                   <SelectItem value="layer">Layer Feed</SelectItem>
//                 </SelectContent>
//               </Select>
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="quantity">Quantity (Bags)</Label>
//               <Input id="quantity" type="number" placeholder="Enter number of bags" required />
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="pricePerBag">Price per Bag ($)</Label>
//               <Input id="pricePerBag" type="number" step="0.01" placeholder="Enter price per bag" required />
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="deliveryDate">Delivery Date</Label>
//               <Input id="deliveryDate" type="date" required />
//             </div>
//             <div className="space-y-2">
//               <Label htmlFor="notes">Notes</Label>
//               <Input id="notes" placeholder="Additional notes" />
//             </div>
//           </CardContent>
//           <CardFooter className="flex justify-between">
//             <Button type="button" variant="outline" onClick={() => navigate("/admin/feed")}>
//               Cancel
//             </Button>
//             <Button type="submit">Add Feed Stock</Button>
//           </CardFooter>
//         </form>
//       </Card>
//     </div>
//   )
// }
