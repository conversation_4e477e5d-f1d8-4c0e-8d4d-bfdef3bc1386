'use client';

import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  MoreVertical,
  Activity,
  AlertTriangle,
  XCircle,
  Trash,
  Pencil,
} from 'lucide-react';
import { useFarm } from '../../contexts/FarmContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import LocationMapCell from '../../components/LocationMapCell';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { BarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import Link from '../../components/feed-components/Link';

const FarmsPage = () => {
  const navigate = useNavigate();
  const { farms, deleteFarm } = useFarm();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredFarms = useMemo(() => {
    let filtered = farms.filter((farm) => {
      const matchesSearch = farm.name.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'owner':
          return a.owner.localeCompare(b.owner);
        case 'email':
          return a.email.localeCompare(b.email);
        case 'phone':
          return a.phone.localeCompare(b.phone);
        default:
          return 0;
      }
    });

    return filtered;
  }, [farms, searchTerm, sortBy]);

  const chartData = useMemo(() => {
    // Create meaningful chart data based on actual farm data
    // Group farms by owner and count them
    const ownerCounts = {};
    filteredFarms.forEach((farm) => {
      const owner = farm.owner || 'Unknown';
      ownerCounts[owner] = (ownerCounts[owner] || 0) + 1;
    });

    // Convert to chart format and limit to top 10 owners
    return Object.entries(ownerCounts)
      .map(([owner, count]) => ({
        name: owner.length > 15 ? owner.substring(0, 15) + '...' : owner,
        fullName: owner,
        farms: count,
      }))
      .sort((a, b) => b.farms - a.farms)
      .slice(0, 10); // Show top 10 owners
  }, [filteredFarms]);

  const handleDelete = (id) => {
    if (window.confirm(t('confirm_delete_farm'))) {
      deleteFarm(id);
    }
  };

  const handleEdit = (id) => {
    navigate(`/admin/farms/edit/${id}`);
  };



  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('farm_management')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('farm_management_description')}</p>
          </div>
          <div className={`flex items-center ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Link
              to="/admin/farms/add"
              className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
              <span className="text-lg">{t('add_new_farm') || 'Add New Farm'}</span>
            </Link>
          </div>
        </div>



        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('total_farms') || 'Total Farms'}</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredFarms.length}</div>
              <p className="text-xs text-muted-foreground">
                {searchTerm ? `${t('filtered_results') || 'Filtered results'}` : `${t('registered_farms') || 'Registered farms'}`}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('unique_owners') || 'Unique Owners'}</CardTitle>
              <AlertTriangle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(filteredFarms.map(farm => farm.owner)).size}
              </div>
              <p className="text-xs text-muted-foreground">{t('different_farm_owners') || 'Different farm owners'}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">{t('avg_farms_per_owner') || 'Avg Farms/Owner'}</CardTitle>
              <XCircle className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {filteredFarms.length > 0
                  ? (filteredFarms.length / new Set(filteredFarms.map(farm => farm.owner)).size).toFixed(1)
                  : '0'
                }
              </div>
              <p className="text-xs text-muted-foreground">{t('farms_per_owner') || 'Farms per owner'}</p>
            </CardContent>
          </Card>
        </div>

        {/* Farm Distribution by Owner Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{t('farm_distribution_by_owner') || 'Farm Distribution by Owner'}</CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('top_farm_owners') || 'Top 10 farm owners by number of farms'}
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis
                    dataKey="name"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    fontSize={12}
                  />
                  <YAxis
                    stroke="#666"
                    fontSize={12}
                    label={{ value: 'Number of Farms', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip
                    formatter={(value) => [value, 'Number of Farms']}
                    labelFormatter={(label) => {
                      const item = chartData.find(d => d.name === label);
                      return `Owner: ${item?.fullName || label}`;
                    }}
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: '1px solid #ccc',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Bar
                    dataKey="farms"
                    fill="#FF6B00"
                    name="Farms"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            {chartData.length === 0 && (
              <div className="flex items-center justify-center h-[200px] text-gray-500">
                <div className="text-center">
                  <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>{t('no_farm_data') || 'No farm data available'}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Search and Sort Controls */}
        <Card>
          <CardHeader>
            <CardTitle>{t('search_and_filter_farms') || 'Search & Filter Farms'}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`flex flex-col lg:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
              {/* Search Input */}
              <div className="relative flex-1">
                <Search
                  className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
                  size={20}
                />
                <input
                  type="text"
                  placeholder={t('search_farms_by_name') || 'Search farms by name...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Sort Controls */}
              <div className={`flex gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                {/* Sort By */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div
                      className={`flex items-center justify-center px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600 cursor-pointer transition-all duration-200 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                    >
                      <Activity className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                      <span>{t('sort_by') || 'Sort'}: {t(sortBy) || sortBy}</span>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                    <DropdownMenuItem
                      onClick={() => setSortBy('name')}
                      className={sortBy === 'name' ? 'bg-[#FF6B00] text-white' : ''}
                    >
                      {t('name') || 'Name'}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setSortBy('owner')}
                      className={sortBy === 'owner' ? 'bg-[#FF6B00] text-white' : ''}
                    >
                      {t('owner') || 'Owner'}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setSortBy('email')}
                      className={sortBy === 'email' ? 'bg-[#FF6B00] text-white' : ''}
                    >
                      {t('email') || 'Email'}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setSortBy('phone')}
                      className={sortBy === 'phone' ? 'bg-[#FF6B00] text-white' : ''}
                    >
                      {t('phone') || 'Phone'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Clear Filters Button */}
                {(searchTerm || sortBy !== 'name') && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setSortBy('name');
                    }}
                    className="px-4 py-3 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 transition-all duration-200"
                  >
                    <XCircle className="h-4 w-4 inline mr-1" />
                    {t('clear_all') || 'Clear All'}
                  </button>
                )}
              </div>
            </div>

            {/* Search Results Info */}
            <div className="mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div>
                {searchTerm && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-blue-700 text-sm">
                      {filteredFarms.length > 0
                        ? `Found ${filteredFarms.length} farm${filteredFarms.length === 1 ? '' : 's'} with name containing "${searchTerm}"`
                        : `No farms found with name containing "${searchTerm}"`
                      }
                      <button
                        onClick={() => setSearchTerm('')}
                        className="ml-2 text-blue-600 hover:text-blue-800 underline"
                      >
                        Clear search
                      </button>
                    </p>
                  </div>
                )}
              </div>

              {/* Results Count */}
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Showing {filteredFarms.length} of {farms.length} farms
                {sortBy !== 'name' && (
                  <span className="ml-1 text-[#FF6B00]">
                    (sorted by {sortBy})
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Farms Table */}
        <Card>
          <CardHeader>
            <CardTitle>{t('farm_list')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('name') || 'Name'}</TableHead>
                  <TableHead>{t('email') || 'Email'}</TableHead>
                  <TableHead>{t('owner') || 'Owner'}</TableHead>
                  <TableHead>{t('phone') || 'Phone'}</TableHead>
                  <TableHead>{t('location') || 'Location'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredFarms.length > 0 ? (
                  filteredFarms.map((farm) => (
                    <TableRow key={farm.id}>
                      <TableCell className="font-medium">{farm.name}</TableCell>
                      <TableCell>{farm.email}</TableCell>
                      <TableCell>{farm.owner}</TableCell>
                      <TableCell>{farm.phone}</TableCell>
                      <TableCell>
                        <LocationMapCell location={farm.location} />
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(farm.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('edit') || 'Edit'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(farm.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      {t('no_farms_found') || 'No farms found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FarmsPage;

//////////////////////
// /* eslint-disable no-undef */
// /* eslint-disable no-unused-vars */
// "use client"

// import { useState, useEffect } from "react"
// import { Link } from "react-router-dom"
// import { Plus, Edit, Trash2, Search, Eye, XCircle, MoreVertical, Pencil, Trash2Icon } from "lucide-react"

// import Button from "../../components/Button"
// import { Input } from "../../components/management-system/ui/Input"
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogFooter,
//   DialogHeader,
//   DialogTitle,
// } from "../../components/management-system/ui/Dialog"
// import { Skeleton } from "../../components/management-system/ui/Skeleton"
// import {
//   Pagination,
//   PaginationContent,
//   PaginationEllipsis,
//   PaginationItem,
//   PaginationLink,
//   PaginationNext,
//   PaginationPrevious,
// } from "../../components/management-system/ui/Pagination"

// const FarmsPage = () => {
//   const [farms, setFarms] = useState([])
//   const [searchTerm, setSearchTerm] = useState("")
//   const [currentPage, setCurrentPage] = useState(1)
//   const [itemsPerPage] = useState(6)
//   const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
//   const [farmToDelete, setFarmToDelete] = useState(null)
//   const [loading, setLoading] = useState(true)
//   const [activeMenu, setActiveMenu] = useState(null) // Track active menu by farm id

//   // Simulating API data fetching
//   useEffect(() => {
//     const fetchFarms = async () => {
//       try {
//         await new Promise((resolve) => setTimeout(resolve, 800))

//         const mockFarms = [
//           {
//             id: 1,
//             name: "Sunny Acres",
//             location: "Texas, USA",
//             chickens: 1200,
//             revenue: "$50,000",
//             availability: "In Stock",
//           },
//           {
//             id: 2,
//             name: "Green Valley",
//             location: "California, USA",
//             chickens: 800,
//             revenue: "$30,000",
//             availability: "Out of Stock",
//           },
//           {
//             id: 3,
//             name: "Blue Ridge Farm",
//             location: "Virginia, USA",
//             chickens: 1500,
//             revenue: "$70,000",
//             availability: "In Stock",
//           },
//           {
//             id: 4,
//             name: "Meadow Farms",
//             location: "Florida, USA",
//             chickens: 900,
//             revenue: "$40,000",
//             availability: "In Stock",
//           },
//           {
//             id: 5,
//             name: "Golden Fields",
//             location: "Iowa, USA",
//             chickens: 1100,
//             revenue: "$45,000",
//             availability: "Out of Stock",
//           },
//           {
//             id: 6,
//             name: "Sunrise Poultry",
//             location: "Georgia, USA",
//             chickens: 1300,
//             revenue: "$55,000",
//             availability: "In Stock",
//           },
//           {
//             id: 7,
//             name: "Hilltop Farm",
//             location: "Oregon, USA",
//             chickens: 950,
//             revenue: "$42,000",
//             availability: "In Stock",
//           },
//         ]

//         setFarms(mockFarms)
//         setLoading(false)
//       } catch (error) {
//         console.error("Error fetching farms:", error)
//         setLoading(false)
//       }
//     }

//     fetchFarms()
//   }, [])

//   // Filter farms based on search term
//   const filteredFarms = farms.filter(
//     (farm) =>
//       farm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       farm.location.toLowerCase().includes(searchTerm.toLowerCase())
//   )

//   // Pagination
//   const indexOfLastItem = currentPage * itemsPerPage
//   const indexOfFirstItem = indexOfLastItem - itemsPerPage
//   const currentItems = filteredFarms.slice(indexOfFirstItem, indexOfLastItem)
//   const totalPages = Math.ceil(filteredFarms.length / itemsPerPage)

//   // Delete functions
//   const openDeleteModal = (farm) => {
//     setFarmToDelete(farm)
//     setIsDeleteModalOpen(true)
//   }

//   const confirmDelete = () => {
//     setFarms(farms.filter((item) => item.id !== farmToDelete.id))
//     setIsDeleteModalOpen(false)
//     setFarmToDelete(null)
//   }

//   // Handle page change
//   const handlePageChange = (page) => {
//     setCurrentPage(page)
//   }

//   // Generate pagination items
//   const renderPaginationItems = () => {
//     const items = []

//     // Always show first page
//     items.push(
//       <PaginationItem key="first">
//         <PaginationLink onClick={() => handlePageChange(1)} isActive={currentPage === 1}>
//           1
//         </PaginationLink>
//       </PaginationItem>
//     )

//     // Show ellipsis if needed
//     if (currentPage > 3) {
//       items.push(
//         <PaginationItem key="ellipsis-1">
//           <PaginationEllipsis />
//         </PaginationItem>
//       )
//     }

//     // Show current page and surrounding pages
//     for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
//       if (i === 1 || i === totalPages) continue // Skip first and last page as they're always shown
//       items.push(
//         <PaginationItem key={i}>
//           <PaginationLink onClick={() => handlePageChange(i)} isActive={currentPage === i}>
//             {i}
//           </PaginationLink>
//         </PaginationItem>
//       )
//     }

//     // Show ellipsis if needed
//     if (currentPage < totalPages - 2) {
//       items.push(
//         <PaginationItem key="ellipsis-2">
//           <PaginationEllipsis />
//         </PaginationItem>
//       )
//     }

//     // Always show last page if there's more than one page
//     if (totalPages > 1) {
//       items.push(
//         <PaginationItem key="last">
//           <PaginationLink onClick={() => handlePageChange(totalPages)} isActive={currentPage === totalPages}>
//             {totalPages}
//           </PaginationLink>
//         </PaginationItem>
//       )
//     }

//     return items
//   }

//   // Handle menu actions (Edit, Delete, Disable)
//   const handleDelete = (farm) => {
//     openDeleteModal(farm)
//     setActiveMenu(null) // Close the menu when an action is taken
//   }

//   const handleDisable = (farm) => {
//     alert(`${farm.name} status changed to disabled!`) // Placeholder for disabling logic
//     setActiveMenu(null)
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 sm:mb-6">
//           <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Manage Farms</h1>
//           <Link
//             to="/admin/farms/add"
//             className="w-full sm:w-auto bg-[#FF6B00] text-white px-4 py-2 rounded-lg flex items-center justify-center hover:bg-[#FF6B00]/90 transition-colors"
//           >
//             <Plus size={18} className="mr-2" />
//             Add Farm
//           </Link>
//         </div>

//         {/* Search and Filter */}
//         <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm mb-4 sm:mb-6">
//           <div className="relative">
//             <input
//               type="text"
//               placeholder="Search farms by name or location..."
//               className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
//               value={searchTerm}
//               onChange={(e) => setSearchTerm(e.target.value)}
//             />
//             <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
//           </div>
//         </div>

//         {/* Farms Grid */}
//         <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
//           {currentItems.map((farm) => (
//             <div
//               key={farm.id}
//               className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4"
//             >
//               <div className="flex justify-between items-start mb-4">
//                 <div>
//                   <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{farm.name}</h3>
//                   <p className="text-sm text-gray-500 dark:text-gray-400">{farm.location}</p>
//                 </div>
//                 <div className="relative">
//                   <button
//                     onClick={() => setActiveMenu(activeMenu === farm.id ? null : farm.id)}
//                     className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
//                   >
//                     <MoreVertical size={18} className="text-gray-500 dark:text-gray-400" />
//                   </button>
//                   {activeMenu === farm.id && (
//                     <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-10">
//                       <Link
//                         to={`/admin/farms/${farm.id}`}
//                         className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                       >
//                         <Eye size={16} className="mr-2" />
//                         View Details
//                       </Link>
//                       <Link
//                         to={`/admin/farms/edit/${farm.id}`}
//                         className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//                       >
//                         <Pencil size={16} className="mr-2" />
//                         Edit Farm
//                       </Link>
//                       <button
//                         onClick={() => openDeleteModal(farm)}
//                         className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700"
//                       >
//                         <Trash2 size={16} className="mr-2" />
//                         Delete Farm
//                       </button>
//                     </div>
//                   )}
//                 </div>
//               </div>
//               <div className="space-y-2">
//                 <div className="flex justify-between text-sm">
//                   <span className="text-gray-500 dark:text-gray-400">Chickens</span>
//                   <span className="font-medium text-gray-900 dark:text-white">{farm.chickens}</span>
//                 </div>
//                 <div className="flex justify-between text-sm">
//                   <span className="text-gray-500 dark:text-gray-400">Revenue</span>
//                   <span className="font-medium text-gray-900 dark:text-white">{farm.revenue}</span>
//                 </div>
//                 <div className="flex justify-between text-sm">
//                   <span className="text-gray-500 dark:text-gray-400">Status</span>
//                   <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                     farm.availability === 'In Stock'
//                       ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
//                       : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
//                   }`}>
//                     {farm.availability}
//                   </span>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>

//         {/* Pagination */}
//         {totalPages > 1 && (
//           <div className="mt-6">
//             <Pagination>
//               <PaginationContent>
//                 <PaginationItem>
//                   <PaginationPrevious
//                     onClick={() => handlePageChange(currentPage - 1)}
//                     className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
//                   />
//                 </PaginationItem>
//                 {renderPaginationItems()}
//                 <PaginationItem>
//                   <PaginationNext
//                     onClick={() => handlePageChange(currentPage + 1)}
//                     className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
//                   />
//                 </PaginationItem>
//               </PaginationContent>
//             </Pagination>
//           </div>
//         )}

//         {/* Delete Confirmation Modal */}
//         <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
//           <DialogContent className="sm:max-w-md">
//             <DialogHeader>
//               <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-white">Delete Farm</DialogTitle>
//               <DialogDescription className="text-gray-500 dark:text-gray-400">
//                 Are you sure you want to delete "{farmToDelete?.name}"? This action cannot be undone.
//               </DialogDescription>
//             </DialogHeader>
//             <DialogFooter className="flex justify-end space-x-3">
//               <Button
//                 variant="secondary"
//                 onClick={() => setIsDeleteModalOpen(false)}
//                 className="text-gray-700 dark:text-gray-300"
//               >
//                 Cancel
//               </Button>
//               <Button
//                 variant="destructive"
//                 onClick={confirmDelete}
//                 className="bg-red-600 text-white hover:bg-red-700"
//               >
//                 Delete
//               </Button>
//               {/* this is delete button */}
//             </DialogFooter>
//           </DialogContent>
//         </Dialog>
//       </div>
//     </div>
//   )
// }

// export default FarmsPage
