import React from 'react';
import { ChevronDown } from 'lucide-react';

const Dropdown = React.forwardRef(
  ({ children, label, variant = 'default', size = 'md', className = '', disabled = false, ...props }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const dropdownRef = React.useRef(null);

    React.useEffect(() => {
      const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const baseStyles = 'relative inline-block text-left';

    const variants = {
      default: 'bg-white dark:bg-gray-800',
      primary: 'bg-[#FF6B00] text-white',
      secondary: 'bg-gray-100 dark:bg-gray-700',
      outline: 'border border-gray-300 dark:border-gray-600',
    };

    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
    };

    const buttonStyles = `inline-flex items-center justify-center rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] ${variants[variant]} ${sizes[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`;

    return (
      <div ref={dropdownRef} className={`${baseStyles} ${className}`}>
        <button
          ref={ref}
          type="button"
          className={buttonStyles}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          {...props}
        >
          {label}
          <ChevronDown className={`ml-2 h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
            <div className="py-1" role="menu" aria-orientation="vertical">
              {children}
            </div>
          </div>
        )}
      </div>
    );
  }
);

Dropdown.Item = ({ children, onClick, className = '', ...props }) => {
  return (
    <button
      className={`block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

Dropdown.displayName = 'Dropdown';
Dropdown.Item.displayName = 'Dropdown.Item';

export default Dropdown;
