import db from "../config/db.js";

const DrugsModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Drugs (D_Name, D_Price, D_SalesPrice, D_StartDate, D_ExpireDate, Lid)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [
        data.D_Name,
        data.D_Price,
        data.D_SalesPrice,
        data.D_StartDate,
        data.D_ExpireDate,
        data.Lid,
      ],
    );

    const [newDrug] = await db.query("SELECT * FROM Drugs WHERE D_Id = ?", [
      result.insertId,
    ]);
    return newDrug[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM Drugs");
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM Drugs WHERE D_Id = ?", [id]);
    return rows[0];
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Drugs SET D_Name = ?, D_Price = ?, D_SalesPrice = ?, D_StartDate = ?, D_ExpireDate = ?, Lid = ? WHERE D_Id = ?`,
      [
        data.D_Name,
        data.D_Price,
        data.D_SalesPrice,
        data.D_StartDate,
        data.D_ExpireDate,
        data.Lid,
        id,
      ],
    );
    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Drugs WHERE D_Id = ?", [id]);
    return result;
  },
};

export default DrugsModel;
