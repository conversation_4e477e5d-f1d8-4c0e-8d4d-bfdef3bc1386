/* eslint-disable react/prop-types */
// src/components/management-system/ui/Calendar.jsx
import { useState } from 'react';
import { Calendar as CalendarIcon } from 'lucide-react';

const Calendar = ({ selectedDate, onDateChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleDateChange = (date) => {
    onDateChange(date);
    setIsOpen(false); // Close the calendar when a date is selected
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
      >
        <CalendarIcon className="mr-2" />
        {selectedDate ? selectedDate.toLocaleDateString() : 'Select Date'}
      </button>
      {isOpen && (
        <div className="absolute mt-2 rounded-md shadow-lg bg-white z-10">
          <input
            type="date"
            onChange={(e) => handleDateChange(new Date(e.target.value))}
            className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
        </div>
      )}
    </div>
  );
};

export { Calendar };
