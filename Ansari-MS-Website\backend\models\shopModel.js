import pool from "../config/db.js";

const shopModel = {
  create: async (data) => {
    const [rows] = await pool.query(
      `INSERT INTO Shop (SName, SOwner, SLocation, SEmail, SPhone, userId)
       VALUES (?, ?, ST_GeomFromText(?), ?, ?, ?)`,
      [
        data.SName,
        data.SOwner,
        data.SLocation,
        data.SEmail,
        data.SPhone,
        data.userId,
      ],
    );
    return { id: rows.insertId, ...data };
  },

  getAll: async () => {
    const [rows] = await pool.query(
      "SELECT *, ST_AsText(SLocation) AS SLocation FROM Shop",
    );
    return rows;
  },

  getById: async (id) => {
    const [rows] = await pool.query(
      "SELECT *, ST_AsText(SLocation) AS SLocation FROM Shop WHERE SId = ?",
      [id],
    );
    return rows[0];
  },

  update: async (id, data) => {
    await pool.query(
      `UPDATE Shop SET SName=?, SOwner=?, SLocation=ST_GeomFromText(?), SEmail=?, SPhone=?, userId=? WHERE SId=?`,
      [
        data.SName,
        data.SOwner,
        data.SLocation,
        data.SEmail,
        data.SPhone,
        data.userId,
        id,
      ],
    );
    return { id, ...data };
  },

  delete: async (id) => {
    const [result] = await pool.query("DELETE FROM Shop WHERE SId = ?", [id]);
    return result;
  },
  findByEmail: async (email) => {
    const [result] = await pool.execute(
      "SELECT * FROM Shop WHERE SEmail = ? LIMIT 1",
      [email],
    );
    return result[0];
  },

  findByPhone: async (phone) => {
    const [result] = await pool.execute(
      "SELECT * FROM Shop WHERE SPhone = ? LIMIT 1",
      [phone],
    );
    return result[0];
  },
};

export default shopModel;
0;
