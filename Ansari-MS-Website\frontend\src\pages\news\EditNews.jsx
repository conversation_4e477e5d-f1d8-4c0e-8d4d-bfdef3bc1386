"use client"

import React, { useState, useEffect, useCallback } from "react"
import { useParams, useNavigate } from "react-router-dom"
import { useNews } from "../../contexts/NewsContext"
import { Upload, ArrowLeft } from "lucide-react"
import axios from "axios"

const EditNews = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { getNewsById, updateNews } = useNews()
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    category: "",
    status: "draft",
    authorId: "",
    excerpt: "",
    featured: false,
    image: null,
  })
  const [previewImage, setPreviewImage] = useState(null)
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [feedback, setFeedback] = useState({ type: "", message: "" })
  const [users, setUsers] = useState([])

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const token = localStorage.getItem("authToken")
        const response = await axios.get("http://localhost:5432/api/v1/users/", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        if (response.data.success) {
          setUsers(response.data.users)
        } else {
          console.error("Failed to fetch users:", response.data.message)
        }
      } catch (err) {
        console.error("Error fetching users:", err)
      }
    }

    fetchUsers()
  }, [])

  useEffect(() => {
    const loadNews = async () => {
      try {
        setLoading(true)
        const newsItem = await getNewsById(id)

        if (newsItem) {
          setFormData({
            title: newsItem.A_Title,
            content: newsItem.A_Body,
            category: newsItem.Category,
            status: newsItem.Status === "Published" ? "published" : "draft",
            authorId: newsItem.u_Id.toString(),
            excerpt: newsItem.Excerpt,
            featured: newsItem.FeaturedNews === 1 || newsItem.FeaturedNews === true,
            image: newsItem.A_Image,
          })

          // Set preview image if available

            // Check if it's a full URL or just a path
            const imageUrl =newsItem.A_Image
            setPreviewImage(imageUrl)

        } else {
          setFeedback({
            type: "error",
            message: "News item not found",
          })
        }
      } catch (error) {
        console.error("Error loading news:", error)
        setFeedback({
          type: "error",
          message: "Error loading news item. Please try again.",
        })
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      loadNews()
    }
  }, [ ])

  const handleChange = useCallback(
    (e) => {
      const { name, value, type, checked } = e.target
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }))
      if (errors[name]) {
        setErrors((prev) => ({
          ...prev,
          [name]: "",
        }))
      }
    },
    [errors],
  )

  const handleImageChange = useCallback((e) => {
    const file = e.target.files[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          image: "Image size should be less than 10MB",
        }))
        return
      }

      if (!file.type.startsWith("image/")) {
        setErrors((prev) => ({
          ...prev,
          image: "Please upload an image file",
        }))
        return
      }

      // Set the actual file for form submission
      setFormData((prev) => ({
        ...prev,
        image: file,
      }))

      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setPreviewImage(reader.result)
      }
      reader.readAsDataURL(file)
    }
  }, [])

  const validateForm = useCallback(() => {
    const errors = {}
    if (!formData.title || formData.title.length < 5) errors.title = "Title must be at least 5 characters"
    if (!formData.content || formData.content.length < 8) errors.content = "Content must be at least 8 characters"
    if (!formData.excerpt) errors.excerpt = "Excerpt is required"
    if (!formData.category) errors.category = "Category is required"
    if (!formData.authorId) errors.authorId = "Author is required"
    return errors
  }, [formData])

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault()
      const validationErrors = validateForm()

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors)
        return
      }

      setIsSubmitting(true)
      try {
        await updateNews(id, formData)

        setFeedback({
          type: "success",
          message: "News updated successfully!",
        })

        setTimeout(() => {
          navigate("/admin/news")
        }, 1500)
      } catch (error) {
        console.error("Error updating news:", error)
        setFeedback({
          type: "error",
          message: error.response?.data?.error || error.message || "Failed to update news. Please try again.",
        })
      } finally {
        setIsSubmitting(false)
      }
    },
    [formData, id, updateNews, navigate, validateForm],
  )

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate("/admin/news")}
          className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back
        </button>
        <h1 className="text-2xl font-bold text-gray-900">Edit News</h1>
      </div>

      {feedback.message && (
        <div
          className={`mb-6 p-4 rounded-lg ${
            feedback.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}
        >
          {feedback.message}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  News Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                    errors.title ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter news title"
                />
                {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
              </div>

              {/* Content */}
              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                  Content
                </label>
                <textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleChange}
                  required
                  rows="6"
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                    errors.content ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Write your news content..."
                ></textarea>
                {errors.content && <p className="mt-1 text-sm text-red-600">{errors.content}</p>}
              </div>

              {/* Excerpt */}
              <div>
                <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-1">
                  Excerpt
                </label>
                <textarea
                  id="excerpt"
                  name="excerpt"
                  value={formData.excerpt}
                  onChange={handleChange}
                  required
                  rows="3"
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                    errors.excerpt ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Write a short excerpt..."
                ></textarea>
                {errors.excerpt && <p className="mt-1 text-sm text-red-600">{errors.excerpt}</p>}
              </div>
            </div>

            <div className="space-y-6">
              {/* News Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">News Image</label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg">
                  <div className="space-y-1 text-center">
                    {previewImage ? (
                      <div>
                        <img
                          src={
                            previewImage.startsWith('data:')
                              ? previewImage
                              : `http://localhost:5432/public/images/news/${previewImage}`
                          }
                          alt="Preview"
                          className="mx-auto h-32 w-auto object-cover rounded-lg"
                          onError={(e) => {
                            e.target.src = '/placeholder.svg';
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setPreviewImage(null)
                            setFormData((prev) => ({ ...prev, image: null }))
                          }}
                          className="mt-2 text-sm text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      </div>
                    ) : (
                      <div>
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                          <label
                            htmlFor="file-upload"
                            className="relative cursor-pointer bg-white rounded-md font-medium text-[#FF6B00] hover:text-[#D32F2F] focus-within:outline-none"
                          >
                            <span>Upload a file</span>
                            <input
                              id="file-upload"
                              name="file-upload"
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={handleImageChange}
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                      </div>
                    )}
                  </div>
                </div>
                {errors.image && <p className="mt-1 text-sm text-red-600">{errors.image}</p>}
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  required
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                    errors.category ? "border-red-500" : "border-gray-300"
                  }`}
                >
                  <option value="">Select a category</option>
                  <option value="Farm Updates">Farm Updates</option>
                  <option value="Poultry Care">Poultry Care</option>
                  <option value="Events">Events</option>
                  <option value="Nutrition">Nutrition</option>
                  <option value="Health">Health</option>
                  <option value="Sustainability">Sustainability</option>
                </select>
                {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
              </div>

              {/* Author Dropdown */}
              <div>
                <label htmlFor="authorId" className="block text-sm font-medium text-gray-700 mb-1">
                  Author
                </label>
                <select
                  id="authorId"
                  name="authorId"
                  value={formData.authorId}
                  onChange={handleChange}
                  required
                  disabled={users.length === 0}
                  className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                    errors.authorId ? "border-red-500" : "border-gray-300"
                  } ${users.length === 0 ? "bg-gray-100 cursor-not-allowed" : ""}`}
                >
                  <option value="">{users.length === 0 ? "No authors available" : "Select an author"}</option>
                  {users.map((user) => (
                    <option key={user.u_Id} value={user.u_Id}>
                      {user.U_FirstName} {user.U_LastName} ({user.Role})
                    </option>
                  ))}
                </select>
                {errors.authorId && <p className="mt-1 text-sm text-red-600">{errors.authorId}</p>}
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center">
                    <input
                      id="status-published"
                      name="status"
                      type="radio"
                      value="published"
                      checked={formData.status === "published"}
                      onChange={handleChange}
                      className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                    />
                    <label htmlFor="status-published" className="ml-3 block text-sm font-medium text-gray-700">
                      Published
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="status-draft"
                      name="status"
                      type="radio"
                      value="draft"
                      checked={formData.status === "draft"}
                      onChange={handleChange}
                      className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
                    />
                    <label htmlFor="status-draft" className="ml-3 block text-sm font-medium text-gray-700">
                      Draft
                    </label>
                  </div>
                </div>
              </div>

              {/* Featured */}
              <div className="flex items-center">
                <input
                  id="featured"
                  name="featured"
                  type="checkbox"
                  checked={formData.featured}
                  onChange={handleChange}
                  className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-3 block text-sm font-medium text-gray-700">
                  Featured News
                </label>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-[#FF6B00] text-white px-6 py-2 rounded-lg hover:bg-[#FF8533] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Updating..." : "Update News"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default React.memo(EditNews)


/////////////////////
// 'use client';

// import React, { useState, useEffect, useCallback } from 'react';
// import { useParams, useNavigate } from 'react-router-dom';
// import { useNews } from '../../contexts/NewsContext';
// import { Save, X, Upload, ArrowLeft } from 'lucide-react';

// const EditNews = () => {
//   const { id } = useParams();
//   const navigate = useNavigate();
//   const { news, updateNews } = useNews();
//   const [loading, setLoading] = useState(true);
//   const [formData, setFormData] = useState({
//     title: '',
//     content: '',
//     category: '',
//     status: 'draft',
//     author: '',
//     excerpt: '',
//     featured: false,
//     image: null,
//     date: new Date().toISOString().split('T')[0],
//   });
//   const [previewImage, setPreviewImage] = useState(null);
//   const [errors, setErrors] = useState({});
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [feedback, setFeedback] = useState({ type: '', message: '' });

//   useEffect(() => {
//     const loadNews = async () => {
//       try {
//         setLoading(true);
//         const newsItem = news.find((item) => item.id === parseInt(id));
//         if (newsItem) {
//           setFormData({
//             ...newsItem,
//             status: newsItem.status.toLowerCase(),
//             date: newsItem.date
//               ? new Date(newsItem.date).toISOString().split('T')[0]
//               : new Date().toISOString().split('T')[0],
//           });
//           if (newsItem.image) {
//             setPreviewImage(newsItem.image);
//           }
//         } else {
//           setFeedback({
//             type: 'error',
//             message: 'News item not found',
//           });
//         }
//       } catch (error) {
//         console.error('Error loading news:', error);
//         setFeedback({
//           type: 'error',
//           message: 'Error loading news item. Please try again.',
//         });
//       } finally {
//         setLoading(false);
//       }
//     };

//     loadNews();
//   }, [id, news]);

//   const handleChange = useCallback(
//     (e) => {
//       const { name, value, type, checked } = e.target;
//       setFormData((prev) => ({
//         ...prev,
//         [name]: type === 'checkbox' ? checked : value,
//       }));
//       if (errors[name]) {
//         setErrors((prev) => ({
//           ...prev,
//           [name]: '',
//         }));
//       }
//     },
//     [errors]
//   );

//   const handleImageChange = useCallback((e) => {
//     const file = e.target.files[0];
//     if (file) {
//       if (file.size > 10 * 1024 * 1024) {
//         setErrors((prev) => ({
//           ...prev,
//           image: 'Image size should be less than 10MB',
//         }));
//         return;
//       }

//       if (!file.type.startsWith('image/')) {
//         setErrors((prev) => ({
//           ...prev,
//           image: 'Please upload an image file',
//         }));
//         return;
//       }

//       const reader = new FileReader();
//       reader.onloadend = () => {
//         setPreviewImage(reader.result);
//         setFormData((prev) => ({
//           ...prev,
//           image: reader.result,
//         }));
//       };
//       reader.readAsDataURL(file);
//     }
//   }, []);

//   const validateForm = useCallback(() => {
//     const errors = {};
//     if (!formData.title.trim()) errors.title = 'Title is required';
//     if (!formData.content.trim()) errors.content = 'Content is required';
//     if (!formData.excerpt.trim()) errors.excerpt = 'Excerpt is required';
//     if (!formData.category) errors.category = 'Category is required';
//     if (!formData.author.trim()) errors.author = 'Author is required';
//     return errors;
//   }, [formData]);

//   const handleSubmit = useCallback(
//     async (e) => {
//       e.preventDefault();
//       const errors = validateForm();

//       if (Object.keys(errors).length > 0) {
//         setErrors(errors);
//         return;
//       }

//       setIsSubmitting(true);
//       try {
//         const newsData = {
//           title: formData.title,
//           content: formData.content,
//           category: formData.category,
//           status: formData.status === 'published' ? 'Published' : 'Draft',
//           author: formData.author,
//           excerpt: formData.excerpt,
//           featured: formData.featured,
//           image: formData.image,
//           date: formData.date,
//         };

//         console.log('Updating news:', newsData);
//         await updateNews(parseInt(id), newsData);

//         setFeedback({
//           type: 'success',
//           message: 'News updated successfully!',
//         });

//         setTimeout(() => {
//           navigate('/admin/news');
//         }, 1500);
//       } catch (error) {
//         console.error('Error updating news:', error);
//         setFeedback({
//           type: 'error',
//           message: error.message || 'Failed to update news. Please try again.',
//         });
//       } finally {
//         setIsSubmitting(false);
//       }
//     },
//     [formData, id, updateNews, navigate, validateForm]
//   );

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     );
//   }

//   return (
//     <div className="container mx-auto px-4 py-8">
//       <div className="flex items-center mb-6">
//         <button
//           onClick={() => navigate('/admin/news')}
//           className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
//         >
//           <ArrowLeft className="h-5 w-5 mr-2" />
//           Back
//         </button>
//         <h1 className="text-2xl font-bold text-gray-900">Edit News</h1>
//       </div>

//       {feedback.message && (
//         <div
//           className={`mb-6 p-4 rounded-lg ${
//             feedback.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
//           }`}
//         >
//           {feedback.message}
//         </div>
//       )}

//       <div className="bg-white rounded-lg shadow-md p-6">
//         <form onSubmit={handleSubmit}>
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div className="md:col-span-2 space-y-6">
//               {/* Title */}
//               <div>
//                 <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
//                   News Title
//                 </label>
//                 <input
//                   type="text"
//                   id="title"
//                   name="title"
//                   value={formData.title}
//                   onChange={handleChange}
//                   required
//                   className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                     errors.title ? 'border-red-500' : 'border-gray-300'
//                   }`}
//                   placeholder="Enter news title"
//                 />
//                 {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
//               </div>

//               {/* Content */}
//               <div>
//                 <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
//                   Content
//                 </label>
//                 <textarea
//                   id="content"
//                   name="content"
//                   value={formData.content}
//                   onChange={handleChange}
//                   required
//                   rows="6"
//                   className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                     errors.content ? 'border-red-500' : 'border-gray-300'
//                   }`}
//                   placeholder="Write your news content..."
//                 ></textarea>
//                 {errors.content && <p className="mt-1 text-sm text-red-600">{errors.content}</p>}
//               </div>

//               {/* Excerpt */}
//               <div>
//                 <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-1">
//                   Excerpt
//                 </label>
//                 <textarea
//                   id="excerpt"
//                   name="excerpt"
//                   value={formData.excerpt}
//                   onChange={handleChange}
//                   required
//                   rows="3"
//                   className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   placeholder="Write a short excerpt..."
//                 ></textarea>
//               </div>
//             </div>

//             <div className="space-y-6">
//               {/* News Image */}
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">News Image</label>
//                 <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg">
//                   <div className="space-y-1 text-center">
//                     {previewImage ? (
//                       <div>
//                         <img src={previewImage} alt="Preview" className="mx-auto h-32 w-auto object-cover rounded-lg" />
//                         <button
//                           type="button"
//                           onClick={() => {
//                             setPreviewImage(null);
//                             setFormData((prev) => ({ ...prev, image: null }));
//                           }}
//                           className="mt-2 text-sm text-red-600 hover:text-red-800"
//                         >
//                           Remove
//                         </button>
//                       </div>
//                     ) : (
//                       <div>
//                         <Upload className="mx-auto h-12 w-12 text-gray-400" />
//                         <div className="flex text-sm text-gray-600">
//                           <label
//                             htmlFor="file-upload"
//                             className="relative cursor-pointer bg-white rounded-md font-medium text-[#FF6B00] hover:text-[#D32F2F] focus-within:outline-none"
//                           >
//                             <span>Upload a file</span>
//                             <input
//                               id="file-upload"
//                               name="file-upload"
//                               type="file"
//                               className="sr-only"
//                               accept="image/*"
//                               onChange={handleImageChange}
//                             />
//                           </label>
//                           <p className="pl-1">or drag and drop</p>
//                         </div>
//                         <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
//                       </div>
//                     )}
//                   </div>
//                 </div>
//                 {errors.image && <p className="mt-1 text-sm text-red-600">{errors.image}</p>}
//               </div>

//               {/* Category */}
//               <div>
//                 <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
//                   Category
//                 </label>
//                 <select
//                   id="category"
//                   name="category"
//                   value={formData.category}
//                   onChange={handleChange}
//                   required
//                   className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                     errors.category ? 'border-red-500' : 'border-gray-300'
//                   }`}
//                 >
//                   <option value="">Select a category</option>
//                   <option value="Farm Updates">Farm Updates</option>
//                   <option value="Poultry Care">Poultry Care</option>
//                   <option value="Events">Events</option>
//                   <option value="Nutrition">Nutrition</option>
//                   <option value="Health">Health</option>
//                   <option value="Sustainability">Sustainability</option>
//                 </select>
//                 {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
//               </div>

//               {/* Author */}
//               <div>
//                 <label htmlFor="author" className="block text-sm font-medium text-gray-700 mb-1">
//                   Author
//                 </label>
//                 <input
//                   type="text"
//                   id="author"
//                   name="author"
//                   value={formData.author}
//                   onChange={handleChange}
//                   required
//                   className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                     errors.author ? 'border-red-500' : 'border-gray-300'
//                   }`}
//                   placeholder="Enter author name"
//                 />
//                 {errors.author && <p className="mt-1 text-sm text-red-600">{errors.author}</p>}
//               </div>

//               {/* Status */}
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
//                 <div className="mt-2 space-y-2">
//                   <div className="flex items-center">
//                     <input
//                       id="status-published"
//                       name="status"
//                       type="radio"
//                       value="published"
//                       checked={formData.status === 'published'}
//                       onChange={handleChange}
//                       className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                     />
//                     <label htmlFor="status-published" className="ml-3 block text-sm font-medium text-gray-700">
//                       Published
//                     </label>
//                   </div>
//                   <div className="flex items-center">
//                     <input
//                       id="status-draft"
//                       name="status"
//                       type="radio"
//                       value="draft"
//                       checked={formData.status === 'draft'}
//                       onChange={handleChange}
//                       className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300"
//                     />
//                     <label htmlFor="status-draft" className="ml-3 block text-sm font-medium text-gray-700">
//                       Draft
//                     </label>
//                   </div>
//                 </div>
//               </div>

//               {/* Featured */}
//               <div className="flex items-center">
//                 <input
//                   id="featured"
//                   name="featured"
//                   type="checkbox"
//                   checked={formData.featured}
//                   onChange={handleChange}
//                   className="h-4 w-4 text-[#FF6B00] focus:ring-[#FF6B00] border-gray-300 rounded"
//                 />
//                 <label htmlFor="featured" className="ml-3 block text-sm font-medium text-gray-700">
//                   Featured News
//                 </label>
//               </div>
//             </div>
//           </div>

//           <div className="mt-6 flex justify-end">
//             <button
//               type="submit"
//               disabled={isSubmitting}
//               className="bg-[#FF6B00] text-white px-6 py-2 rounded-lg hover:bg-[#FF8533] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
//             >
//               {isSubmitting ? 'Updating...' : 'Update News'}
//             </button>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default React.memo(EditNews);
