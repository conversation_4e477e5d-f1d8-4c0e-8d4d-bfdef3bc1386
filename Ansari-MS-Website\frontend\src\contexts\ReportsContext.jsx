import React, { createContext, useContext, useState, useEffect } from 'react';
import { saveToLocalStorage, loadFromLocalStorage, removeFromLocalStorage } from '../utils/localStorage';

const ReportsContext = createContext();

export const useReports = () => {
  const context = useContext(ReportsContext);
  if (!context) {
    throw new Error('useReports must be used within a ReportsProvider');
  }
  return context;
};

export const ReportsProvider = ({ children }) => {
  const [reports, setReports] = useState(() => loadFromLocalStorage('reports', []));

  useEffect(() => {
    saveToLocalStorage('reports', reports);
  }, [reports]);

  const clearReports = () => {
    setReports([]);
    removeFromLocalStorage('reports');
  };

  const addReport = (report) => {
    setReports((prev) => [...prev, { ...report, id: Date.now() }]);
  };

  const updateReport = (id, updatedReport) => {
    setReports((prev) => prev.map((report) => (report.id === id ? { ...report, ...updatedReport } : report)));
  };

  const deleteReport = (id) => {
    setReports((prev) => prev.filter((report) => report.id !== id));
  };

  return (
    <ReportsContext.Provider
      value={{
        reports,
        setReports,
        addReport,
        updateReport,
        deleteReport,
        clearReports,
      }}
    >
      {children}
    </ReportsContext.Provider>
  );
};
