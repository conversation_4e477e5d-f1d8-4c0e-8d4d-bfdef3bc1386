// import { useNotifications } from '../contexts/NotificationContext';

// class WebSocketService {
//   constructor() {
//     this.socket = null;
//     this.reconnectAttempts = 0;
//     this.maxReconnectAttempts = 5;
//     this.reconnectInterval = 3000;
//     this.notificationCallback = null;
//   }

//   setNotificationCallback(callback) {
//     this.notificationCallback = callback;
//   }

//   connect() {
//     if (this.socket && this.socket.readyState === WebSocket.OPEN) {
//       return;
//     }

//     this.socket = new WebSocket(import.meta.env.VITE_WS_URL || 'ws://localhost:3001');

//     this.socket.onopen = () => {
//       console.log('WebSocket connected');
//       this.reconnectAttempts = 0;
//     };

//     this.socket.onmessage = (event) => {
//       const data = JSON.parse(event.data);
//       this.handleMessage(data);
//     };

//     this.socket.onclose = () => {
//       console.log('WebSocket disconnected');
//       this.handleReconnect();
//     };

//     this.socket.onerror = (error) => {
//       console.error('WebSocket error:', error);
//     };
//   }

//   handleReconnect() {
//     if (this.reconnectAttempts < this.maxReconnectAttempts) {
//       this.reconnectAttempts++;
//       setTimeout(() => {
//         console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
//         this.connect();
//       }, this.reconnectInterval);
//     }
//   }

//   handleMessage(data) {
//     if (this.notificationCallback) {
//       switch (data.type) {
//         case 'notification':
//           this.notificationCallback(data.notificationType, data.message, data.data);
//           break;
//         case 'system':
//           console.log('System message:', data.message);
//           break;
//         default:
//           console.log('Unknown message type:', data.type);
//       }
//     }
//   }

//   send(data) {
//     if (this.socket && this.socket.readyState === WebSocket.OPEN) {
//       this.socket.send(JSON.stringify(data));
//     }
//   }

//   disconnect() {
//     if (this.socket) {
//       this.socket.close();
//     }
//   }
// }

// export const webSocketService = new WebSocketService();
