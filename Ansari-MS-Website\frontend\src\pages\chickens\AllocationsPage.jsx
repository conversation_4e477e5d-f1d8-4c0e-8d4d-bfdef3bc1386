import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, MoreVertical, Trash, RotateCcw, Calendar, Clock, CheckCircle } from 'lucide-react';
import { useChicken } from '../../contexts/ChickenContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import Link from '../../components/feed-components/Link';

const AllocationsPage = () => {
  const navigate = useNavigate();
  const { allocations, fetchAllocations, deleteAllocation, loading } = useChicken();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    fetchAllocations();
  }, []);

  const filteredAllocations = allocations.filter((allocation) =>
    allocation.farmName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    allocation.farmOwner.toLowerCase().includes(searchTerm.toLowerCase()) ||
    allocation.supplierName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id) => {
    if (window.confirm(t('confirm_delete_allocation') || 'Are you sure you want to delete this allocation?')) {
      try {
        await deleteAllocation(id);
      } catch (error) {
        console.error('Error deleting allocation:', error);
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    const numericAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
    }).format(numericAmount);
  };

  const getStatusBadge = (allocation) => {
    const daysElapsed = allocation.daysElapsed || 0;
    const isReady = daysElapsed >= 45;
    
    if (allocation.status === 'completed') {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>;
    } else if (isReady) {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Ready for Buyback</span>;
    } else {
      return <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">In Progress</span>;
    }
  };

  const readyForBuyback = filteredAllocations.filter(a => a.daysElapsed >= 45 && a.status === 'allocated');
  const inProgress = filteredAllocations.filter(a => a.daysElapsed < 45 && a.status === 'allocated');
  const completed = filteredAllocations.filter(a => a.status === 'completed');

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#FF6B00]"></div>
          <p className="mt-4 text-gray-600">{t('loading_allocations') || 'Loading allocations...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t('farm_allocations') || 'Farm Allocations'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('manage_farm_allocations_description') || 'Manage chickens allocated to farms for 45 days'}
          </p>
        </div>
        <div className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Link 
            to="/admin/chickens" 
            className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {t('back_to_dashboard') || 'Back to Dashboard'}
          </Link>
          <Link 
            to="/admin/chickens/allocations/add" 
            className="inline-flex items-center px-6 py-3 bg-[#FF6B00] hover:bg-[#e55a00] text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            <Plus className={`h-5 w-5 ${language === 'ps' ? 'ml-3' : 'mr-3'}`} />
            <span>{t('allocate_to_farm') || 'Allocate to Farm'}</span>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder={t('search_allocations') || 'Search allocations by farm name, owner, or supplier...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] focus:border-[#FF6B00] dark:bg-gray-700 dark:text-white transition-all duration-200`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('in_progress') || 'In Progress'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {inProgress.length}
                </h3>
                <p className="text-xs text-blue-600">
                  {inProgress.reduce((sum, a) => sum + a.quantity, 0)} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-blue-500/10 rounded-full">
                <Clock className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/10 to-orange-500/5 border-orange-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('ready_for_buyback') || 'Ready for Buyback'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {readyForBuyback.length}
                </h3>
                <p className="text-xs text-orange-600">
                  {readyForBuyback.reduce((sum, a) => sum + a.quantity, 0)} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-orange-500/10 rounded-full">
                <Calendar className="h-6 w-6 text-orange-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t('completed') || 'Completed'}
                </p>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {completed.length}
                </h3>
                <p className="text-xs text-green-600">
                  {completed.reduce((sum, a) => sum + a.quantity, 0)} {t('chickens') || 'chickens'}
                </p>
              </div>
              <div className="p-3 bg-green-500/10 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Allocations Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('allocations_list') || 'Allocations List'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('allocation_date') || 'Allocation Date'}</TableHead>
                  <TableHead>{t('farm') || 'Farm'}</TableHead>
                  <TableHead>{t('supplier') || 'Supplier'}</TableHead>
                  <TableHead>{t('quantity') || 'Quantity'}</TableHead>
                  <TableHead>{t('price_per_chicken') || 'Price/Chicken'}</TableHead>
                  <TableHead>{t('total_price') || 'Total Price'}</TableHead>
                  <TableHead>{t('days_elapsed') || 'Days Elapsed'}</TableHead>
                  <TableHead>{t('status') || 'Status'}</TableHead>
                  <TableHead>{t('bought_back') || 'Bought Back'}</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>{t('actions') || 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAllocations.length > 0 ? (
                  filteredAllocations.map((allocation) => (
                    <TableRow key={allocation.id}>
                      <TableCell>{formatDate(allocation.allocationDate)}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{allocation.farmName}</div>
                          <div className="text-sm text-gray-500">{allocation.farmOwner}</div>
                        </div>
                      </TableCell>
                      <TableCell>{allocation.supplierName}</TableCell>
                      <TableCell className="font-medium">{allocation.quantity}</TableCell>
                      <TableCell>{formatCurrency(allocation.pricePerChicken)}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(allocation.totalPrice)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{allocation.daysElapsed || 0} days</div>
                          <div className="text-gray-500">
                            {allocation.daysElapsed >= 45 ? 'Ready!' : `${45 - (allocation.daysElapsed || 0)} days left`}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(allocation)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{allocation.boughtBackQuantity || 0} / {allocation.quantity}</div>
                          <div className="text-gray-500">
                            {allocation.remainingQuantity !== undefined ? allocation.remainingQuantity : allocation.quantity} remaining
                          </div>
                          <div className="mt-1">
                            {Number(allocation.remainingQuantity !== undefined ? allocation.remainingQuantity : allocation.quantity) === 0 ? (
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Completed
                              </span>
                            ) : allocation.daysElapsed >= 45 ? (
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Ready for Buyback
                              </span>
                            ) : (
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {45 - (allocation.daysElapsed || 0)} days left
                              </span>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer">
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem
                              onClick={() => navigate(`/admin/chickens/buybacks/add?allocationId=${allocation.id}`)}
                              disabled={allocation.daysElapsed < 45 || allocation.remainingQuantity <= 0}
                            >
                              <RotateCcw className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('buyback_from_farm') || 'Buyback from Farm'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(allocation.id)}>
                              <Trash className={`h-4 w-4 text-red-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              {t('delete') || 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-4">
                      {t('no_allocations_found') || 'No allocations found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AllocationsPage;
