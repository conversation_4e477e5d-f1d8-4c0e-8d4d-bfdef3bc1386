import express from "express";
import ArticleController, {
  uploadUserPhoto,
  resizeUser<PERSON>hoto,
} from "../controllers/articleController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

router.post(
  "/",
  authenticate,
  authorizeAdmin,
  uploadUser<PERSON>hoto,
  resizeUser<PERSON>hoto,
  ArticleController.create,
);
router.get("/", ArticleController.getAll);
router.get("/:id", ArticleController.getById);
router.put(
  "/:id",
  authenticate,
  authorizeAdmin,
  uploadUserPhoto,
  resizeUserPhoto,
  ArticleController.update,
);
router.delete("/:id", authenticate, authorizeAdmin, ArticleController.delete);

export default router;
