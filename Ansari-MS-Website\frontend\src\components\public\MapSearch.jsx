import { useState } from 'react';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';

const mapContainerStyle = {
  width: '100%',
  height: '500px',
  opacity: '20',
};

const center = {
  lat: 31.6205, // Default latitude (India)
  lng: 65.7158, // Default longitude
};

const API_KEY = 'YOUR_GOOGLE_MAPS_API_KEY'; // Replace with your actual API key

function App() {
  const [location, setLocation] = useState(center);
  const [input, setInput] = useState('');

  const searchLocation = async () => {
    if (!input) return;

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(input)}&key=${API_KEY}`
    );
    const data = await response.json();

    if (data.status === 'OK') {
      const newLocation = data.results[0].geometry.location;
      setLocation(newLocation);
    } else {
      alert('Location not found!');
    }
  };

  return (
    <div className="text-center p-5 op">
      <h2>Enter a Place Name to Find It on the Map</h2>
      <input
        type="text"
        placeholder="Enter a location"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        className="p-2 w-3/5"
      />
      <button onClick={searchLocation} className="p-2 ml-2">
        Search
      </button>

      <LoadScript googleMapsApiKey={API_KEY}>
        <GoogleMap mapContainerStyle={mapContainerStyle} center={location} zoom={10}>
          <Marker position={location} />
        </GoogleMap>
      </LoadScript>
    </div>
  );
}

export default App;
