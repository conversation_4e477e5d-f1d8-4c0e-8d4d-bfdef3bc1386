import <PERSON><PERSON> from "joi";
import multer from "multer";
import path from "path";
import fs from "fs";
import sharp from "sharp";
import { fileURLToPath } from "url";

import LimitedModel from "../models/limitedModel.js";
import asyncHandler from "../middlewares/asyncHandler.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Multer Storage & Filter
const multerStorage = multer.memoryStorage();
const multerFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("logo")) {
    cb(null, true);
  } else {
    cb(new Error("Not an image! Please upload only images."), false);
  }
};

const upload = multer({ storage: multerStorage, fileFilter: multerFilter });
const uploadUserPhoto = upload.single("image");

// Image Processing Middleware
const resizeUserPhoto = asyncHandler(async (req, res, next) => {
  if (!req.file) return next();

  const dir = path.join(__dirname, ".././public/images/limited"); // Corrected path
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  const filename = `limited-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}.jpeg`;
  req.file.filename = filename;

  await sharp(req.file.buffer)
    .resize(500, 500)
    .toFormat("jpeg")
    .jpeg({ quality: 90 })
    .toFile(path.join(dir, filename));

  req.body.logo = filename;
  next();
});

const LimitedController = {
  limitedSchema: Joi.object({
    Lname: Joi.string().min(3).max(50).required(),
    LDescription: Joi.string().min(5).max(200).required(),
    Ladd: Joi.string().required(),
    logo: Joi.string().optional(),
    LEamil: Joi.string().email().required(),
    LPhone1: Joi.string().required(),
    LPhone2: Joi.string().optional(),
    userId: Joi.number().required(),
  }),
  createLimited: asyncHandler(async (req, res) => {
    const { error, value } = LimitedController.limitedSchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });
    }

    const emailExists = await LimitedModel.isEmailTaken(value.LEamil);
    if (emailExists) {
      return res.status(409).json({
        success: false,
        error: "This email is already in use. Please use another email.",
      });
    }

    const phone1Exists = await LimitedModel.isPhone1Taken(value.LPhone1);
    if (phone1Exists) {
      return res.status(409).json({
        success: false,
        error: "This phone number (LPhone1) is already in use.",
      });
    }

    if (value.LPhone2) {
      const phone2Exists = await LimitedModel.isPhone2Taken(value.LPhone2);
      if (phone2Exists) {
        return res.status(409).json({
          success: false,
          error: "This phone number (LPhone2) is already in use.",
        });
      }
    }

    const newLimited = await LimitedModel.createLimited(value);
    res.status(201).json({
      success: true,
      message: "Limited created successfully",
      limited: newLimited,
    });
  }),

  getAllLimited: asyncHandler(async (req, res) => {
    const data = await LimitedModel.getAllLimited();
    res.json({ success: true, total: data.length, data });
  }),

  getLimitedById: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const item = await LimitedModel.getLimitedById(id);
    if (!item)
      return res
        .status(404)
        .json({ success: false, error: "Limited not found" });
    res.json({ success: true, data: item });
  }),

  updateLimited: asyncHandler(async (req, res) => {
    const { error, value } = LimitedController.limitedSchema.validate(req.body);
    if (error)
      return res
        .status(400)
        .json({ success: false, error: error.details[0].message });

    const id = req.params.id;
    const result = await LimitedModel.updateLimited(id, value);
    if (result.affectedRows === 0)
      return res
        .status(404)
        .json({ success: false, error: "Limited not found" });

    res.json({ success: true, message: "Limited updated successfully" });
  }),

  deleteLimited: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const result = await LimitedModel.deleteLimited(id);
    if (result.affectedRows === 0)
      return res
        .status(404)
        .json({ success: false, error: "Limited not found" });

    res.json({ success: true, message: "Limited deleted successfully" });
  }),
};

export default LimitedController;
export { uploadUserPhoto, resizeUserPhoto };
