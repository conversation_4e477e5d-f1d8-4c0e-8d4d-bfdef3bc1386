'use client';

import { useState, useEffect } from 'react';
import { Upload } from 'lucide-react';
import profileService from '../../services/profileService';

const Profile = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    bio: '',
    birthDate: '',
    joinDate: '',
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [avatar, setAvatar] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [success, setSuccess] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    // Fetch user data from API
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        const response = await profileService.getCurrentUserProfile();

        if (response.success && response.user) {
          const userData = response.user;
          console.log('Frontend received userData:', userData);
          console.log('Frontend birthDate:', userData.birthDate);

          setProfileData({
            firstName: userData.firstName || '',
            lastName: userData.lastName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            address: userData.address || '',
            bio: userData.bio || '',
            birthDate: userData.birthDate || '',
            joinDate: userData.joinDate || '',
          });

          // Set profile image if available
          if (userData.image && userData.image !== 'default-user.jpg') {
            setPreviewImage(userData.image);
          } else {
            setPreviewImage('/placeholder.svg?height=200&width=200');
          }
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        setErrors({ general: 'Failed to load profile data. Please refresh the page.' });
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: null,
      }));
    }
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: null,
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAvatar(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateProfileForm = () => {
    const validation = profileService.validateProfileData(profileData);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const validatePasswordForm = () => {
    const newErrors = {};

    // Validate current password
    if (!passwordData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    // Validate new password
    if (!passwordData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = 'Password must be at least 6 characters';
    }

    // Validate password confirmation
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();

    if (!validateProfileForm()) return;

    setSaving(true);
    setSuccess(false);
    setErrors({});

    try {
      await profileService.updateCurrentUserProfile(profileData, avatar);

      setSaving(false);
      setSuccess(true);

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setSaving(false);
      setErrors({ general: error.message || 'Failed to update profile. Please try again.' });
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    if (!validatePasswordForm()) return;

    setSaving(true);
    setSuccess(false);
    setErrors({});

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:5432/api/v1/users/changePassword', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSaving(false);
        setSuccess(true);

        // Reset password fields
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });

        // Hide success message after 3 seconds
        setTimeout(() => {
          setSuccess(false);
        }, 3000);
      } else {
        setSaving(false);
        setErrors({ general: data.message || 'Failed to update password' });
      }
    } catch (error) {
      console.error('Error updating password:', error);
      setSaving(false);
      setErrors({ general: 'An error occurred while updating password. Please try again.' });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-4 sm:mb-6">
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
          <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
            Manage your account settings and preferences.
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-2 sm:space-x-4 mb-4 sm:mb-6">
          <button
            onClick={() => setActiveTab('profile')}
            className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'profile'
                ? 'bg-[#FF6B00] text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            Profile
          </button>
          <button
            onClick={() => setActiveTab('security')}
            className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'security'
                ? 'bg-[#FF6B00] text-white'
                : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
          >
            Security
          </button>
        </div>

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-3 sm:p-4 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg text-sm">
            Changes saved successfully!
          </div>
        )}

        {/* General Error Message */}
        {errors.general && (
          <div className="mb-4 p-3 sm:p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg text-sm">
            {errors.general}
          </div>
        )}

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <form onSubmit={handleProfileSubmit} className="space-y-4 sm:space-y-6">
            {/* Avatar Section */}
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
              <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
                <div className="relative">
                  <img
                    src={
                      previewImage && previewImage.startsWith('data:')
                        ? previewImage
                        : previewImage && previewImage !== '/placeholder.svg?height=200&width=200'
                        ? `http://localhost:5432/public/images/users/${previewImage}`
                        : '/placeholder.svg?height=200&width=200'
                    }
                    alt="Profile"
                    className="w-24 h-24 sm:w-32 sm:h-32 rounded-full object-cover"
                  />
                  <label
                    htmlFor="avatar-upload"
                    className="absolute bottom-0 right-0 p-1.5 bg-[#FF6B00] rounded-full cursor-pointer hover:bg-[#FF6B00]/90 transition-colors"
                  >
                    <Upload className="w-4 h-4 text-white" />
                  </label>
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                  />
                </div>
                <div className="text-center sm:text-left">
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                    {profileData.firstName} {profileData.lastName}
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{profileData.email}</p>
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Personal Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    First Name
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={profileData.firstName}
                    onChange={handleProfileChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.firstName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.firstName && <p className="mt-1 text-xs text-red-500">{errors.firstName}</p>}
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Last Name
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={profileData.lastName}
                    onChange={handleProfileChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.lastName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.lastName && <p className="mt-1 text-xs text-red-500">{errors.lastName}</p>}
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={profileData.email}
                    onChange={handleProfileChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email}</p>}
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Phone
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={profileData.phone}
                    onChange={handleProfileChange}
                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Address
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={profileData.address}
                    onChange={handleProfileChange}
                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  />
                </div>
                <div className="sm:col-span-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bio
                  </label>
                  <textarea
                    name="bio"
                    value={profileData.bio}
                    onChange={handleProfileChange}
                    rows="3"
                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  />
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Additional Information
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Birth Date
                  </label>
                  <input
                    type="date"
                    name="birthDate"
                    value={profileData.birthDate}
                    onChange={handleProfileChange}
                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
                  />
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Join Date (Read-only)
                  </label>
                  <input
                    type="date"
                    name="joinDate"
                    value={profileData.joinDate}
                    readOnly
                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-700 cursor-not-allowed"
                    title="Join date cannot be modified"
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <form onSubmit={handlePasswordSubmit} className="space-y-4 sm:space-y-6">
            <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Change Password</h3>

              {/* General Error Message */}
              {errors.general && (
                <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg text-sm">
                  {errors.general}
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Current Password
                  </label>
                  <input
                    type="password"
                    name="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.currentPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.currentPassword && <p className="mt-1 text-xs text-red-500">{errors.currentPassword}</p>}
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    New Password
                  </label>
                  <input
                    type="password"
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.newPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.newPassword && <p className="mt-1 text-xs text-red-500">{errors.newPassword}</p>}
                </div>
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
                      errors.confirmPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.confirmPassword && <p className="mt-1 text-xs text-red-500">{errors.confirmPassword}</p>}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Updating...' : 'Update Password'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Profile;

/////////////////////////////
// "use client"

// import { useState, useEffect } from "react"
// import { Save, Upload, Lock, Mail, User, Calendar, MapPin } from 'lucide-react'

// const Profile = () => {
//   const [loading, setLoading] = useState(true)
//   const [saving, setSaving] = useState(false)
//   const [activeTab, setActiveTab] = useState("profile")
//   const [profileData, setProfileData] = useState({
//     firstName: "",
//     lastName: "",
//     email: "",
//     phone: "",
//     address: "",
//     bio: "",
//     birthDate: "",
//     joinDate: "",
//   })
//   const [passwordData, setPasswordData] = useState({
//     currentPassword: "",
//     newPassword: "",
//     confirmPassword: "",
//   })
//   const [avatar, setAvatar] = useState(null)
//   const [previewImage, setPreviewImage] = useState(null)
//   const [success, setSuccess] = useState(false)
//   const [errors, setErrors] = useState({})

//   useEffect(() => {
//     // In a real app, you would fetch profile data from your API
//     // Simulating API call
//     setTimeout(() => {
//       setProfileData({
//         firstName: "Admin",
//         lastName: "User",
//         email: "<EMAIL>",
//         phone: "+93 (07) 3456-7890",
//         address: "123 Admin Street, Admin City, AC 12345",
//         bio: "Farm administrator with over 5 years of experience in poultry management.",
//         birthDate: "1985-06-15",
//         joinDate: "2020-03-10",
//       })
//       setPreviewImage("/placeholder.svg?height=200&width=200")
//       setLoading(false)
//     }, 800)
//   }, [])

//   const handleProfileChange = (e) => {
//     const { name, value } = e.target
//     setProfileData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))

//     // Clear error when field is edited
//     if (errors[name]) {
//       setErrors((prev) => ({
//         ...prev,
//         [name]: null,
//       }))
//     }
//   }

//   const handlePasswordChange = (e) => {
//     const { name, value } = e.target
//     setPasswordData((prev) => ({
//       ...prev,
//       [name]: value,
//     }))

//     // Clear error when field is edited
//     if (errors[name]) {
//       setErrors((prev) => ({
//         ...prev,
//         [name]: null,
//       }))
//     }
//   }

//   const handleImageChange = (e) => {
//     const file = e.target.files[0]
//     if (file) {
//       setAvatar(file)
//       const reader = new FileReader()
//       reader.onloadend = () => {
//         setPreviewImage(reader.result)
//       }
//       reader.readAsDataURL(file)
//     }
//   }

//   const validateProfileForm = () => {
//     const newErrors = {}

//     // Validate required fields
//     if (!profileData.firstName.trim()) newErrors.firstName = "First name is required"
//     if (!profileData.lastName.trim()) newErrors.lastName = "Last name is required"

//     // Validate email
//     if (!profileData.email.trim()) {
//       newErrors.email = "Email is required"
//     } else if (!/\S+@\S+\.\S+/.test(profileData.email)) {
//       newErrors.email = "Email is invalid"
//     }

//     setErrors(newErrors)
//     return Object.keys(newErrors).length === 0
//   }

//   const validatePasswordForm = () => {
//     const newErrors = {}

//     // Validate current password
//     if (!passwordData.currentPassword) {
//       newErrors.currentPassword = "Current password is required"
//     }

//     // Validate new password
//     if (!passwordData.newPassword) {
//       newErrors.newPassword = "New password is required"
//     } else if (passwordData.newPassword.length < 6) {
//       newErrors.newPassword = "Password must be at least 6 characters"
//     }

//     // Validate password confirmation
//     if (passwordData.newPassword !== passwordData.confirmPassword) {
//       newErrors.confirmPassword = "Passwords do not match"
//     }

//     setErrors(newErrors)
//     return Object.keys(newErrors).length === 0
//   }

//   const handleProfileSubmit = async (e) => {
//     e.preventDefault()

//     if (!validateProfileForm()) return

//     setSaving(true)
//     setSuccess(false)

//     try {
//       // In a real app, you would update profile data in your API
//       // Simulating API call
//       setTimeout(() => {
//         console.log("Profile updated:", { ...profileData, avatar })
//         setSaving(false)
//         setSuccess(true)

//         // Hide success message after 3 seconds
//         setTimeout(() => {
//           setSuccess(false)
//         }, 3000)
//       }, 1500)
//     } catch (error) {
//       console.error("Error updating profile:", error)
//       setSaving(false)
//     }
//   }

//   const handlePasswordSubmit = async (e) => {
//     e.preventDefault()

//     if (!validatePasswordForm()) return

//     setSaving(true)
//     setSuccess(false)

//     try {
//       // In a real app, you would update password in your API
//       // Simulating API call
//       setTimeout(() => {
//         console.log("Password updated")
//         setSaving(false)
//         setSuccess(true)

//         // Reset password fields
//         setPasswordData({
//           currentPassword: "",
//           newPassword: "",
//           confirmPassword: "",
//         })

//         // Hide success message after 3 seconds
//         setTimeout(() => {
//           setSuccess(false)
//         }, 3000)
//       }, 1500)
//     } catch (error) {
//       console.error("Error updating password:", error)
//       setSaving(false)
//     }
//   }

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF6B00]"></div>
//       </div>
//     )
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-2 sm:p-4 md:p-6">
//       <div className="max-w-4xl mx-auto">
//         {/* Header */}
//         <div className="mb-4 sm:mb-6">
//           <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
//           <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Manage your account settings and preferences.</p>
//         </div>

//         {/* Tabs */}
//         <div className="flex space-x-2 sm:space-x-4 mb-4 sm:mb-6">
//           <button
//             onClick={() => setActiveTab("profile")}
//             className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors ${
//               activeTab === "profile"
//                 ? "bg-[#FF6B00] text-white"
//                 : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//             }`}
//           >
//             Profile
//           </button>
//           <button
//             onClick={() => setActiveTab("security")}
//             className={`flex-1 sm:flex-none px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors ${
//               activeTab === "security"
//                 ? "bg-[#FF6B00] text-white"
//                 : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
//             }`}
//           >
//             Security
//           </button>
//         </div>

//         {/* Success Message */}
//         {success && (
//           <div className="mb-4 p-3 sm:p-4 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg text-sm">
//             Changes saved successfully!
//           </div>
//         )}

//         {/* Profile Tab */}
//         {activeTab === "profile" && (
//           <form onSubmit={handleProfileSubmit} className="space-y-4 sm:space-y-6">
//             {/* Avatar Section */}
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
//               <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6">
//                 <div className="relative">
//                   <img
//                     src={previewImage}
//                     alt="Profile"
//                     className="w-24 h-24 sm:w-32 sm:h-32 rounded-full object-cover"
//                   />
//                   <label
//                     htmlFor="avatar-upload"
//                     className="absolute bottom-0 right-0 p-1.5 bg-[#FF6B00] rounded-full cursor-pointer hover:bg-[#FF6B00]/90 transition-colors"
//                   >
//                     <Upload className="w-4 h-4 text-white" />
//                   </label>
//                   <input
//                     id="avatar-upload"
//                     type="file"
//                     accept="image/*"
//                     onChange={handleImageChange}
//                     className="hidden"
//                   />
//                 </div>
//                 <div className="text-center sm:text-left">
//                   <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
//                     {profileData.firstName} {profileData.lastName}
//                   </h2>
//                   <p className="text-sm text-gray-500 dark:text-gray-400">{profileData.email}</p>
//                 </div>
//               </div>
//             </div>

//             {/* Personal Information */}
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
//               <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Personal Information</h3>
//               <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     First Name
//                   </label>
//                   <input
//                     type="text"
//                     name="firstName"
//                     value={profileData.firstName}
//                     onChange={handleProfileChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.firstName ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.firstName && <p className="mt-1 text-xs text-red-500">{errors.firstName}</p>}
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Last Name
//                   </label>
//                   <input
//                     type="text"
//                     name="lastName"
//                     value={profileData.lastName}
//                     onChange={handleProfileChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.lastName ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.lastName && <p className="mt-1 text-xs text-red-500">{errors.lastName}</p>}
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Email
//                   </label>
//                   <input
//                     type="email"
//                     name="email"
//                     value={profileData.email}
//                     onChange={handleProfileChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.email ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email}</p>}
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Phone
//                   </label>
//                   <input
//                     type="tel"
//                     name="phone"
//                     value={profileData.phone}
//                     onChange={handleProfileChange}
//                     className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   />
//                 </div>
//                 <div className="sm:col-span-2">
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Address
//                   </label>
//                   <input
//                     type="text"
//                     name="address"
//                     value={profileData.address}
//                     onChange={handleProfileChange}
//                     className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   />
//                 </div>
//                 <div className="sm:col-span-2">
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Bio
//                   </label>
//                   <textarea
//                     name="bio"
//                     value={profileData.bio}
//                     onChange={handleProfileChange}
//                     rows="3"
//                     className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   />
//                 </div>
//               </div>
//             </div>

//             {/* Additional Information */}
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
//               <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Additional Information</h3>
//               <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Birth Date
//                   </label>
//                   <input
//                     type="date"
//                     name="birthDate"
//                     value={profileData.birthDate}
//                     onChange={handleProfileChange}
//                     className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Join Date
//                   </label>
//                   <input
//                     type="date"
//                     name="joinDate"
//                     value={profileData.joinDate}
//                     onChange={handleProfileChange}
//                     className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00]"
//                   />
//                 </div>
//               </div>
//             </div>

//             {/* Submit Button */}
//             <div className="flex justify-end">
//               <button
//                 type="submit"
//                 disabled={saving}
//                 className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed"
//               >
//                 {saving ? "Saving..." : "Save Changes"}
//               </button>
//             </div>
//           </form>
//         )}

//         {/* Security Tab */}
//         {activeTab === "security" && (
//           <form onSubmit={handlePasswordSubmit} className="space-y-4 sm:space-y-6">
//             <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm">
//               <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Change Password</h3>
//               <div className="space-y-4">
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Current Password
//                   </label>
//                   <input
//                     type="password"
//                     name="currentPassword"
//                     value={passwordData.currentPassword}
//                     onChange={handlePasswordChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.currentPassword ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.currentPassword && <p className="mt-1 text-xs text-red-500">{errors.currentPassword}</p>}
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     New Password
//                   </label>
//                   <input
//                     type="password"
//                     name="newPassword"
//                     value={passwordData.newPassword}
//                     onChange={handlePasswordChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.newPassword ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.newPassword && <p className="mt-1 text-xs text-red-500">{errors.newPassword}</p>}
//                 </div>
//                 <div>
//                   <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
//                     Confirm New Password
//                   </label>
//                   <input
//                     type="password"
//                     name="confirmPassword"
//                     value={passwordData.confirmPassword}
//                     onChange={handlePasswordChange}
//                     className={`w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] ${
//                       errors.confirmPassword ? "border-red-500" : "border-gray-300 dark:border-gray-600"
//                     }`}
//                   />
//                   {errors.confirmPassword && <p className="mt-1 text-xs text-red-500">{errors.confirmPassword}</p>}
//                 </div>
//               </div>
//             </div>

//             {/* Submit Button */}
//             <div className="flex justify-end">
//               <button
//                 type="submit"
//                 disabled={saving}
//                 className="px-4 py-2 text-sm font-medium text-white bg-[#FF6B00] rounded-lg hover:bg-[#FF6B00]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6B00] disabled:opacity-50 disabled:cursor-not-allowed"
//               >
//                 {saving ? "Updating..." : "Update Password"}
//               </button>
//             </div>
//           </form>
//         )}
//       </div>
//     </div>
//   )
// }

// export default Profile
