/* eslint-disable react/prop-types */
import { Star, StarHalf } from 'lucide-react';

const TestimonialCard = ({ name, review, rating, avatarUrl }) => {
  // Render star rating
  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={`full-${i}`} className="text-yellow-500 fill-yellow-500" size={16} />);
    }

    if (hasHalfStar) {
      stars.push(<StarHalf key="half" className="text-yellow-500 fill-yellow-500" size={16} />);
    }

    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="text-gray-300" size={16} />);
    }

    return <div className="flex">{stars}</div>;
  };

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border h-full flex flex-col">
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
          <img
            src={avatarUrl || 'https://api.dicebear.com/7.x/avataaars/svg?seed=default&backgroundColor=b6e3f4'}
            alt={name}
            className="w-full h-full "
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = 'https://api.dicebear.com/7.x/avataaars/svg?seed=default&backgroundColor=b6e3f4';
            }}
          />
        </div>
        <div>
          <h4 className="font-bold font-heading text-[#2C3E50]">{name}</h4>
          {renderRating(rating)}
        </div>
      </div>
      <p className="text-gray-600 italic flex-grow font-sans break-words">&quot;{review}&quot;</p>
    </div>
  );
};

export default TestimonialCard;

// "use client"

// import { Star, StarHalf } from "lucide-react"
// import { motion } from "framer-motion"

// const TestimonialCard = ({ name, review, rating, avatarUrl }) => {
//   // Generate stars based on rating
//   const renderStars = (rating) => {
//     const stars = []
//     const fullStars = Math.floor(rating)
//     const hasHalfStar = rating % 1 !== 0

//     // Add full stars
//     for (let i = 0; i < fullStars; i++) {
//       stars.push(<Star key={`star-${i}`} className="w-5 h-5 fill-[#FF6B00] text-[#FF6B00]" />)
//     }

//     // Add half star if needed
//     if (hasHalfStar) {
//       stars.push(<StarHalf key="half-star" className="w-5 h-5 fill-[#FF6B00] text-[#FF6B00]" />)
//     }

//     // Add empty stars to make total of 5
//     const emptyStars = 5 - Math.ceil(rating)
//     for (let i = 0; i < emptyStars; i++) {
//       stars.push(<Star key={`empty-${i}`} className="w-5 h-5 text-[#CCCCCC]" />)
//     }

//     return stars
//   }

//   return (
//     <motion.div
//       className="bg-white rounded-2xl shadow-lg p-8 h-full flex flex-col hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-[#EEEEEE]"
//       whileHover={{ scale: 1.02 }}
//       transition={{ duration: 0.3 }}
//     >
//       <div className="flex items-center mb-6">
//         <div className="w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-[#FF6B00] p-1">
//           <img
//             src={avatarUrl || "/placeholder.svg?height=64&width=64"}
//             alt={`${name}'s avatar`}
//             className="w-full h-full object-cover rounded-full"
//           />
//         </div>
//         <div>
//           <h4 className="font-bold text-[#333333] text-lg">{name}</h4>
//           <div className="flex mt-1">{renderStars(rating)}</div>
//         </div>
//       </div>

//       <div className="relative mb-6">
//         <div className="absolute top-0 left-0 w-10 h-10 text-[#FF6B00]/20 flex items-center justify-center">
//           <span className="text-4xl font-serif">&quot;</span>
//         </div>
//         <p className="text-[#333333]/80 pl-14 italic flex-grow">{review}</p>
//       </div>

//       <div className="mt-auto pt-4 border-t border-[#EEEEEE] flex justify-between items-center">
//         <p className="text-sm text-[#666666]">Verified Customer</p>
//         <div className="bg-[#F5F5F5] px-3 py-1 rounded-full text-xs text-[#333333] font-medium">
//           {rating === 5
//             ? "Excellent"
//             : rating >= 4
//               ? "Very Good"
//               : rating >= 3
//                 ? "Good"
//                 : rating >= 2
//                   ? "Fair"
//                   : "Poor"}
//         </div>
//       </div>
//     </motion.div>
//   )
// }

// export default TestimonialCard;
