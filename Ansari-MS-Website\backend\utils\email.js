import nodemailer from "nodemailer";

class Email {
  constructor(user, url) {
    this.to = user.U_Email;
    this.firstName = user.U_FirstName;
    this.url = url;
    this.from = `ANSARI SHERKAT <${process.env.EMAIL_FROM}>`;
  }

  newTransport() {
    return nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  }

  async send(template, subject, text) {
    const mailOptions = {
      from: this.from,
      to: this.to,
      subject,
      html: text || `<p>Please click on the following link: ${this.url}</p>`, // Ensure 'html' is used
    };

    await this.newTransport().sendMail(mailOptions);
  }

  // OTP Verification Email
  async sendOTPVerification(otp) {
    const otpText = `
      <html>
        <body style="font-family: Arial, sans-serif; color: #333; line-height: 1.5;">
          <div style="max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);">
            
            <!-- Header Section -->
            <h2 style="text-align: center; color: #4CAF50; font-size: 28px; font-weight: 600;">Email Verification</h2>
            <p style="font-size: 18px; text-align: center; color: #444;">Hello ${this.firstName},</p>
            <p style="font-size: 16px; color: #555; text-align: center; margin-bottom: 20px;">
              We're excited to have you on board! Please verify your email address to complete the registration process.
            </p>
            
            <!-- OTP Code Section -->
            <div style="background-color: #ffffff; border-radius: 6px; padding: 25px; text-align: center; margin-bottom: 20px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
              <p style="font-size: 18px; color: #555;">Your Email Verification Code:</p>
              <p style="font-size: 40px; font-weight: bold; color: #FF5722; margin: 15px 0;">${otp}</p>
              <p style="font-size: 16px; color: #888;">This code will expire in 3 minutes.</p>
            </div>
            
            <!-- Call-to-Action Button -->
            <div style="text-align: center;">
              <a href="${this.url}" style="padding: 12px 30px; background-color: #4CAF50; color: #fff; font-size: 18px; font-weight: 600; text-decoration: none; border-radius: 30px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                Verify Email
              </a>
            </div>

            <!-- Instructions Section -->
            <p style="font-size: 16px; color: #555; margin-top: 20px;">
              If you did not request this code, please ignore this email or contact our support team.
            </p>
            
            <!-- Footer Section -->
            <p style="font-size: 14px; text-align: center; color: #888; margin-top: 30px;">
              <em>We're always here to help!</em>
            </p>
            <p style="font-size: 14px; text-align: center; color: #aaa;">
              <strong>Thanks for being with us!</strong>
            </p>
          </div>
        </body>
      </html>
    `;

    await this.send("verifyOTP", "Email Verification", otpText);
  }

  // Password Reset Email
  async sendPasswordReset() {
    const resetPasswordLink = `
      <html>
        <body style="font-family: Arial, sans-serif; color: #333; line-height: 1.5;">
          <div style="max-width: 600px; margin: 0 auto; padding: 30px; background-color: #f9f9f9; border-radius: 8px; box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);">
            
            <!-- Header Section -->
            <h2 style="text-align: center; color: #4CAF50; font-size: 28px; font-weight: 600;">Password Reset Request</h2>
            <p style="font-size: 18px; text-align: center; color: #444;">Hello ${this.firstName},</p>
            <p style="font-size: 16px; color: #555; text-align: center; margin-bottom: 20px;">
              We received a request to reset your password. If you didn't request this, please ignore this email.
            </p>

            <!-- Reset Password Section -->
            <div style="background-color: #ffffff; border-radius: 6px; padding: 25px; text-align: center; margin-bottom: 20px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
              <p style="font-size: 18px; color: #555;">To reset your password, click the link below:</p>
              <a href="${this.url}" style="padding: 12px 30px; background-color: #4CAF50; color: #fff; font-size: 18px; font-weight: 600; text-decoration: none; border-radius: 30px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);">
                Reset Password link
              </a>
            </div>
            
            <!-- Instructions Section -->
            <p style="font-size: 16px; color: #555; margin-top: 20px;">
              This link will expire in 10 minutes. If you did not request a password reset, please ignore this email.
            </p>
            
            <!-- Footer Section -->
            <p style="font-size: 14px; text-align: center; color: #888; margin-top: 30px;">
              <em>We're here to help you at any time!</em>
            </p>
            <p style="font-size: 14px; text-align: center; color: #aaa;">
              <strong>Thanks for being with us!</strong>
            </p>
          </div>
        </body>
      </html>
    `;

    await this.send(
      "passwordReset",
      "Your password reset request (valid for only 10 minutes)",
      resetPasswordLink,
    );
  }
}

export default Email;
