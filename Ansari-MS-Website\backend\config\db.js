// config/db.js
import mysql from "mysql2/promise";
import dotenv from "dotenv";

dotenv.config();

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10, // Maximum number of connections in the pool
  queueLimit: 0, // Number of connection requests that can wait in the queue
});

// Function to test the database connection
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log("✨ Successfully Connected to the Database! ✨");
    connection.release(); // Release the connection back to the pool
  } catch (error) {
    console.error("Error connecting to the Database:", error.message);
  }
};

// Test the connection when the module is loaded
testConnection();

export default pool;
