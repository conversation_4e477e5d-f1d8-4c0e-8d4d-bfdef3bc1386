-- Migration to add timestamp fields to users table
-- Add createdAt and updatedAt columns to users table

ALTER TABLE users 
ADD COLUMN createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update existing users to have a createdAt value (set to current timestamp)
UPDATE users 
SET createdAt = CURRENT_TIMESTAMP, updatedAt = CURRENT_TIMESTAMP 
WHERE createdAt IS NULL;

-- Add indexes for better performance
CREATE INDEX idx_users_created_at ON users(createdAt);
CREATE INDEX idx_users_updated_at ON users(updatedAt);
