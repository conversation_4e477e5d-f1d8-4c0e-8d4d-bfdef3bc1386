@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 24 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 29% 24%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 24 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 29% 24%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 24 100% 50%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  /* * {
    @apply border-border;
  } */

  body {
    @apply bg-background text-foreground font-bodyEn;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-headingEn;
  }

  /* Pashto/Arabic support */
  [lang='ps'],
  .ps {
    direction: rtl;
    text-align: right;
    font-family: 'Noto Naskh Arabic';
  }

  [lang='ps'] h1,
  [lang='ps'] h2,
  [lang='ps'] h3,
  .ps h1,
  .ps h2,
  .ps h3 {
    @apply font-headingPs;
  }

  [lang='ps'] p,
  .ps p {
    @apply font-bodyPs;
  }
}
